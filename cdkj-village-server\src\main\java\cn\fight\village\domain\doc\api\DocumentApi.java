package cn.fight.village.domain.doc.api;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.doc.query.DocumentQuery;
import cn.fight.village.domain.doc.service.DocumentService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 档案接口层
 *
 */
@Controller
@RequestMapping("document")
public class DocumentApi {

    @Resource
    private DocumentService documentService;


    /**
     * 档案列表
     *
     * @param query
     * @return
     */
    @ResponseBody
    @PostMapping("list")
    public JsonResult getDocumentList(@RequestBody DocumentQuery query) {
        return documentService.selectDocumentList(query);
    }

    /**
     * 根据合同ID获取合同要件
     *
     * @param contractId
     * @return
     */
    @ResponseBody
    @GetMapping("selectByContract")
    public JsonResult getContractDoc(String contractId) {
        return documentService.getContractDoc(contractId);
    }
}

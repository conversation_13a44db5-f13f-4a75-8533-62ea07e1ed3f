  //生成许可证内容方法
    public static String getLicenceCode(String requestCode) {
        //私钥RSA，不可外泄
        String privetKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMom7Og3eeNztc14++aqpqBin4qBo8ixkAMS+3HUjh6JXfO2b+GCSyL/49F0CleUVVoOVftbZA9uwFCgl8WNpW9TX8Zt6oulLpeGbW7Rek3vfiztI4gVcDNZdfEK1h11+N60pm+hKugjQXbuP4Hq1mNJZQvfeF5hqe7Qm0utwTlPAgMBAAECgYAogG0Fx5ulrRi3Pq6lk/pHdwsE+p1gh+bVHtf/Afmpd5c5zYsT8QFx1TgFTo0F6APDw8yJTFG7X9KrNMnDvHHxBPIYKD2ocHmWgPDrfA9ESHpOFvgI11bJiXIYi2BReLERryUXn3nokKf/YOnlKzIQQisEeJz5YmSShOJr1PwosQJBAPuZlgNLGPSrRM5BLovJ6pIgI7Zoc340lID90aMjJj/vI7aFHwAVBfJnEkyQUgVdXiiCqt/JaL4YlcwwXM2T3F8CQQDNr/YEhdXxrgfkFKbiGHRK8PsBU8MO9/0+WJjNGAJ9WxKYsZghtDHwvMHxqevheK4DXu4fTtdkf0QvnRL5t8kRAkEAhX/K/1hbl4dA7QfdAMNUudBf4qutjGut2HvVPnCqHQZwtqoP9uUw0JwsM3/oZXxTN7+Nl0yxTRySb1PCCjrIlQJAEvQO8HXAtCd8NKkug5ELTkiMaJ/mTn/Nhyw00FlRCWoV+ZoL0bdADtXl7TXiNYGgT1E1Eg96y5jqmJSZxgcIcQJAAxMtu70IgqYfno6hlu4EPaG5dbSsHx2yqu9t69L6GuJXzDzkGI+Mv3i/s9F/qauUW6N1+XJYkXpvjLVEWytfkQ==";
        RSA rsa = new RSA(privetKey, null);

        //base64解码
        byte[] reqCodeBA = Base64.decode(requestCode);
        String decode = StrUtil.str(rsa.decrypt(reqCodeBA, KeyType.PrivateKey), CharsetUtil.CHARSET_UTF_8); //私钥解密
        JSONObject jsonObject = JSON.parseObject(decode);
        String macCode = (String)jsonObject.get("macCode");

        //获取到的mac地址再使用私钥加密
        Map<String, Object> result = new HashMap<>();
        result.put("macCode",macCode);
        String resultJson = JSON.toJSONString(result);
        byte[] encode = rsa.encrypt(resultJson, KeyType.PrivateKey);

        //密文转换为base64编码
        return Base64.encode(encode);
    }
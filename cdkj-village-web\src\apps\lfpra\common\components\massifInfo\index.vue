<!-- 地块信息 -->
<template>
  <div>
    <GroupTitle :title="props.menuName =='pitAndPond'?'坑塘信息':'地块信息'">
      <el-button v-if="props.isEdit" type="primary" @click="addFanc">新增</el-button>
    </GroupTitle>
    <funi-curd-v2
      :data="tableData"
      :columns="conColumns"
      :pagination="false"
      :loading="loading"
      :stripe="false"
      border
    />
  </div>
  <massifInfoDialog ref="massifInfoModal" @exportObject="setCollection" />
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref, watchEffect,provide } from 'vue';
import { ElNotification } from 'element-plus';
import GroupTitle from '@/apps/lfpra/common/components/groupTitle/index.vue';
import massifInfoDialog from '@/apps/lfpra/common/components/massifInfo/massifInfoDialog.vue';
import { userTableColumns } from './hooks/index.jsx';
const massifInfoModal = ref(null);
const loading = ref(false);
const tableData = ref([]); // 表格数据
// 接收数据
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true
  },
  tableData: {
    type: Array,
    default: () => []
  },
  menuName: {
    type: String,
    default: ''
  },
  layerType:{
    type: Object,
    default: () => {}
  },
  otherPatternIds:{
    type: Array,
    default: () => []
  }
});
const patternIdsArray = ref([])
provide('otherPatternIds', patternIdsArray)
const emit = defineEmits(['exMassifData']);
const conColumns = computed(() => {
  return userTableColumns({
    seeDateils,
    editFunc,
    delFunc,
    isEdit: props.isEdit, //区分新增/编辑  查看
    menuName: props.menuName //区分 家庭承包和坑塘承包 表单显示
  });
});
watchEffect(() => {
  // 编辑/查看进入 回显权属信息列表
  if (props.tableData.length > 0) {
    tableData.value = props.tableData;
  }
});

// 选择后的表格数据
const setCollection = e => {
  if (tableData.value.some(item=>item.sort ==  e.sort)) { //编辑
    tableData.value.forEach((item,index) => {
      if (item.sort == e.sort) {
        tableData.value[index] = e;
      }
    });
  } else {//新增
    tableData.value.push(e)
  }
  emit('exMassifData', unref(tableData.value));
};
// 新增
const addFanc = () => {
  getPatternIds()
  massifInfoModal.value.show('新增', props.isEdit, props.menuName,[],tableData.value,props.layerType);
};
// table表格 操作编辑
const editFunc = (row, index) => {
  getPatternIds(row)
  massifInfoModal.value.show('编辑', props.isEdit, props.menuName, row,tableData.value,props.layerType);
};
// table表格 操作移除
const delFunc = (row, index) => {
  tableData.value.splice(index, 1);
  emit('exMassifData', unref(tableData.value));
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
};
// table表格 查看详情
const seeDateils = (row, index) => {
  getPatternIds(row)
  massifInfoModal.value.show('查看', props.isEdit, props.menuName, row,tableData.value,props.layerType);
};
const getPatternIds = (row)=>{
  patternIdsArray.value = []
  let ids = []
  if(row){ //编辑查看
    tableData.value.map(item=>{
    if(item.patternId !== row.patternId){
      ids.push(item.patternId)
    }
  })
  }else{//新增
    ids =  tableData.value.map(item=>item.patternId)
  }
  patternIdsArray.value = [...props.otherPatternIds,...ids]
}
</script>

import HyperlinkInfo from '@/apps/lfpra/common/components/hyperlinkTable/infoBtn.vue';
// 家庭成员
export const useFamilyColumus = ({ seeDateils }) => {
  return [
    {
      label: '姓名',
      prop: 'memberName',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => {
              seeDateils(row);
            }}
            text={row.memberName}
          />
        );
      }
    },
    { label: '性别', prop: 'dicGenderName' },
    { label: '证件类型', prop: 'dicCardTypeName' },
    { label: '证件号码', prop: 'cerCertificateNo' },
    { label: '联系方式', prop: 'phone' },
    { label: '家庭关系', prop: 'dicRelationName' }
  ];
};

// 宅基地
export const useHomesteadColumus = ({ seeDateils }) => {
  return [
    {
      label: '宅基地编号',
      prop: 'siteCode',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => {
              seeDateils(row, 'siteCode');
            }}
            text={row.siteCode}
          />
        );
      }
    },
    { label: '宅基地面积（㎡）', prop: 'siteArea' },
    { label: '是否取证', prop: 'isForensicsName' },
    { label: '使用情况', prop: 'dicUseCondName' },
    { label: '土地四至', prop: 'allOverTheLand' }
  ];
};
// 承包地信息
export const useContractedLandColumus = ({ seeDateils }) => {
  return [
    {
      label: '地块编号',
      prop: 'code',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => {
              seeDateils(row, 'landCode');
            }}
            text={row.code}
          />
        );
      }
    },
    { label: '地块实测面积（㎡）', prop: 'area' },
    {
      label: '是否承包地',
      prop: 'isContractedLand',
      render: ({ row, index }) => {
        return <span>{row.isContractedLand ? '是' : '否'}</span>;
      }
    },
    { label: '土地类型', prop: 'dicLandName' },
    { label: '种植情况', prop: 'dicCropCondName' },
    {
      label: '是否流转',
      prop: 'isTransfer',
      render: ({ row, index }) => {
        return <span>{row.isTransfer ? '是' : '否'}</span>;
      }
    }
  ];
};
// 坑塘承包信息
export const useForestLandColumus = ({ seeDateils }) => {
  return [
    {
      label: '地块代码',
      prop: 'code',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={() => {
              seeDateils(row, 'forestCode');
            }}
            text={row.code}
          />
        );
      }
    },
    { label: '地块面积', prop: 'area' },
    { label: '土地类型', prop: 'dicLandName' },
    {
      label: '是否承包',
      prop: 'isContractedLand',
      render: ({ row, index }) => {
        return <span>{row.isContractedLand ? '是' : '否'}</span>;
      }
    }
  ];
};
// 工作信息
export const useProfessionalColumus = () => {
  return [
    { label: '工作节点', prop: 'operateDescribe' },
    { label: '处理时间', prop: 'createTime' },
    { label: '处理人员', prop: 'operatorName' },
    {
      label: '处理结果',
      prop: 'operateContent',
      render: ({ row, index }) => {
        return (
          <>
            <el-text class="w-180px" truncated>
              {row.operateContent}
            </el-text>
          </>
        );
      }
    },
    // { label: '处理意见', prop: 'operateUpdate' }
  ];
};

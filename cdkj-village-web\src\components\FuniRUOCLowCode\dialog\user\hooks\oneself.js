/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-23 17:43:07
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-24 14:59:53
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCodeV2/dialog/user/hooks/oneself.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
export const getUserInfo = async ({ loading, allList = [], callback = () => { } }, allowedSelf = []) => {
    loading && (loading.value = true)
    let data = await $http.post('/csuc/userCenter/getUserInfo').finally(() => {
        loading && (loading.value = false)
    });
    let arr = [
        {
            id: data.userId,
            nickName: data.nickName
        }
    ]
    let list = allList
    arr.forEach(item => {
        let flag = allList.findIndex(ele => ele.id === item.id);
        if (flag < 0) {
            list.push(item);
        }
    });
    callback(list)
    return arr
};
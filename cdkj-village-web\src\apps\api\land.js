/**
 * 新增地块
 * @param {*} data 
 */
export const add = (data) => {
    return $http.post("/realLand/create", data)
}
/**
 * 删除
 * @param {*} data 
 */
export const del = (data) => {
    return $http.post("/realLand/delete", data)
}
/**
 * 获取详情
 * @param {*} data 
 */
export const info = (data,config) => {
    return $http.fetch("/realLand/info", data,config)
}

/**
 * 获取地块列表情
 * @param {*} data 
 */
export const list = (data,config ={}) => {
    return $http.post("/realLand/list", data,config)
}

/**
 * 承包与流转统计
 * @param {*} data 
 */
export const getStatistic = (data,config ={}) => {
    return $http.post("/statistics/village", data,config)
}

/**
 * 获取地块所有组
 * @param {*} data 
 */
export const getVillageTeams = (data,config ={}) => {
    return $http.fetch("/statistics/getVillageTeams", data,config)
}

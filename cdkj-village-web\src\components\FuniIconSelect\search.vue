<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-05 16:04:28
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-12-06 18:25:00
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniIconSelect/search.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="icon-search">
    <!-- type select -->
    <div class="type-select-box">
      <div class="type-select-block" @click.stop="selectType">
        <transition :name="scrollBlock[typeCode]?.direction" mode="out-in">
          <span :key="typeCode" style="display: inline-block"> {{ typeCode }} </span>
        </transition>
      </div>
    </div>
    <div class="type-input-box">
      <el-input :modelValue="iconName" @input="nameInput" placeholder="请输入图标名称" :suffix-icon="Search" />
    </div>
    <div
      class="type-select"
      :class="{
        'type-select-show': selectShow
      }"
    >
      <div
        class="type-select-option"
        :style="{
          order: typeCode == item ? -1 : void 0
        }"
        v-for="item in iconTypesList"
        :key="item"
        @click="optionClick(item)"
        v-click-outside="onClickOutside"
      >
        <div
          class="type-select-option__item"
          :class="{
            'type-selected-option': typeCode == item
          }"
        >
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue';
import { ClickOutside as vClickOutside } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
const props = defineProps({
  iconTypes: {
    type: Array,
    default: []
  },
  typeCode: {
    type: String,
    default: '所有'
  },
  scrollBlock: {
    type: Object,
    default: {}
  }
});
const iconName = ref();
let timer = void 0;
const emit = defineEmits(['updateType', 'updateIconName']);
const selectShow = ref(false);

const iconTypesList = computed(() => {
  return ['所有', ...props.iconTypes];
});

const selectType = () => {
  selectShow.value = true;
};
const optionClick = item => {
  emit('updateType', item);
  selectShow.value = false;
};
const onClickOutside = () => {
  selectShow.value = false;
};
const nameInput = e => {
  iconName.value = e;
  clearTimeout(timer);
  timer = setTimeout(() => {
    emit('updateIconName', e);
  }, 800);
};
</script>
<style scoped>
@import url(./search.css);
</style>

package cn.fight.village.domain.doc.service;

import cn.fight.village.domain.common.constant.BusinessConstant;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.domain.contract.entity.Contract;
import cn.fight.village.domain.doc.entity.Document;
import cn.fight.village.domain.doc.query.DocumentQuery;
import cn.fight.village.domain.doc.repository.DocumentMapper;
import cn.fight.village.domain.doc.value.DocumentListVo;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.utils.SnowFlakeUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 合同档案信息
 */
@Service
public class DocumentService {

    @Resource
    private DocumentMapper documentMapper;

    private static String PATH = "/documents/";

    /**
     * 创建档案对象
     *
     * @param contract
     * @return
     */
    public void createDocument(Contract contract, User user) {
        if (contract == null) {
            return ;
        }

        Document inserter = new Document();
        inserter.manageCreateInfo(user);
        inserter.setDocNo(contract.getContractNo());
        inserter.setPath(PATH + contract.getContractNo());
        inserter.setContractId(contract.getUuid());

        if (documentMapper.insert(inserter) != 1) {
            //throw new BusinessException("生成合同档案失败");
        }
    }

    @Value("${business.documents_path}")
    private String documentsPath;

    /**
     * 获取档案列表
     *
     * @param query
     * @return
     */
    public JsonResult selectDocumentList(DocumentQuery query) {
        query.checkPageParam();
        PageHelper.startPage(query.getPageNo(),query.getPageSize());

        List<DocumentListVo> documentListVos = null;
        if ("家庭承包".equals(query.getContractType())) {
            documentListVos = documentMapper.queryList(query);
        } else if ("土地流转".equals(query.getContractType())) {
            if (StringUtils.hasText(query.getLocation())) {
                query.setLocation(SecureUtils.stringEncode(query.getLocation()));
            }
            if(StringUtils.hasText(query.getIdCode())) {
                query.setIdCode(SecureUtils.stringEncode(query.getIdCode()));
            }

            documentListVos = documentMapper.queryList4Trans(query);

            if (!CollectionUtils.isEmpty(documentListVos)) {
                for (DocumentListVo documentListVo : documentListVos) {
                    SecureUtils.sensitiveFieldDecrypt(documentListVo);
                }
            }
        }

        if (CollectionUtils.isEmpty(documentListVos)) {
            return JsonResult.valueOfObject(new PageInfo<>(Collections.emptyList()));
        }

        for (DocumentListVo documentListVo : documentListVos) {
            documentListVo.setDocuments(getFilenames(documentListVo.getPath()));
        }

        return JsonResult.valueOfObject(new PageInfo<>(documentListVos));
    }

    /**
     * 根据合同ID获取档案信息
     *
     * @param contractId
     * @return
     */
    public JsonResult getContractDoc(String contractId) {
        CommonUtils.notNull(contractId,"合同ID不能为空");
        DocumentListVo result = documentMapper.selectByContractId(contractId);
        if (result != null) {
            result.setDocuments(this.getFilenames(result.getPath()));
        }

        return JsonResult.valueOfObject(result);
    }

    /**
     * 获取该目录下所有文件名
     *
     * @param path
     * @return
     */
    public  List<String> getFilenames(String path) {
        List<String> filenames = new ArrayList<>();

        String folderPath = this.documentsPath + path; //文件夹全路径
        File folder = new File(folderPath);

        if (folder.exists() && folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        filenames.add("/documents" + path + "/" + file.getName());
                    }
                }
            }
        }

        return filenames;
    }
}

<template>
  <div>
    <funi-detail :bizName="bizName" :showHead="true" :steps="steps" :detailHeadOption="detailHeadOption" />
  </div>
</template>

<script setup>
import { computed, onMounted, ref, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { MemberInfo, baseTable } from './components/memberDetails';
import archivesInfo from '@/apps/lfpra/common/components/archivesFileView';
import {
  familyInfoApi,
  apiUrl,
  querySiteInfoListApi,
  queryUnderInfoListApi,
  queryFileInfoListExportApi,
  queryFileInfoListApi
} from './api/index';
import { getOperateLogHttp } from '@/apps/lfpra/common/hooks/api';
import { expotrFunction } from '@/apps/lfpra/common/hooks/utils.jsx';
import {
  useHomesteadColumus,
  useContractedLandColumus,
  useForestLandColumus,
  useProfessionalColumus
} from './hooks/detailsColumus';
const route = useRoute();
const router = useRouter();
const detail = ref({});
const bizName = ref(route.query.bizName);
const id = ref(route.query.id);
const familySn = ref(route.query.familySn);
let familyInfoVoData = ref({});
const treeDataList = ref([]);
// 头部数据
const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    hideStatusBar: false,
    statusName: '户数据状态',
    status: '有效',
    serialName: '户编号',
    hideStatusName: true,
    no: route.query.no || '--',
  };
  return obj;
});

onMounted(() => {
  // 人员与户信息数据
  familyInfoApi({ familyInfoId: id.value }).then(res => {
    familyInfoVoData.value = res.familyInfoVo;
  });
});

// 宅基地数据
const SiteInfoLodaData = async (a, b) => {
  let data = await querySiteInfoListApi({ ...a, ...b, familySn: familySn.value });
  return data;
};

//家庭承包地数据
const underInfoLodaData = (a, b) => {
  return queryUnderInfoListApi({ ...a, ...b, familySn: familySn.value, cerType: 1 });
};
// 坑塘
const forestInfoLodaData = (a, b) => {
  return queryUnderInfoListApi({ ...a, ...b, familySn: familySn.value, cerType: 2 });
};

// 档案信息
const fileInfoLodaData = async () => {
  let data = await queryFileInfoListApi({ familySn: familySn.value, flag: false });
  treeDataList.value = data.list;
};

// 工作信息
const OperateLogHttp = (a, b) => {
  return getOperateLogHttp({ ...a, ...b, principalSn: familySn.value });
};

onMounted(() => {
  fileInfoLodaData();
});

// 根据type跳转详情页
const seeDateils = (row, type) => {
  let obj = {
    siteCode: {
      name: 'lfpra_homestead_details',
      title: '宅基地信息详情'
    },
    landCode: {
      name: 'lfpra_contractedLand_details',
      title: '承包地信息详情'
    },
    forestCode: {
      name: 'lfpra_woodland_details',
      title: '坑塘信息详情'
    }
  };
  router.push({
    name: obj[type].name,
    query: {
      title: obj[type].title,
      bizName: '详情',
      type: 'info',
      id: row.id,
      no: row[type],
      tab: obj[type].title,
      principalSn:type=='siteCode'? row.siteSn:row.principalSn,
    }
  });
};

// 文件下载回调
const headBtnClick = (res, val) => {
  let type = '';
  if (res.fileName.lastIndexOf('.') !== -1) {
    type = res.fileName.substr(res.fileName.lastIndexOf('.') + 1);
  } else {
    type = 'xls';
  }
  expotrFunction({
    url: apiUrl.queryFileInfoListExport,
    params: { flag: false, fileInfoId: res.id },
    FileName: '档案信息',
    FileType: type
  });
};
const steps = computed(() => {
  let arr = [
    {
      title: '人员与户信息',
      type: MemberInfo,
      props: {
        detailData: familyInfoVoData.value
      }
    },
    {
      title: '宅基地信息',
      type: baseTable,
      props: {
        columusOptions: useHomesteadColumus({ seeDateils }),
        isPagination: true,
        lodaData: SiteInfoLodaData
      }
    },
    {
      title: '家庭承包地信息',
      type: baseTable,
      props: {
        columusOptions: useContractedLandColumus({ seeDateils }),
        isPagination: true,
        lodaData: underInfoLodaData
      }
    },
    {
      title: '坑塘承包信息',
      type: baseTable,
      props: {
        columusOptions: useForestLandColumus({ seeDateils }),
        isPagination: true,
        lodaData: forestInfoLodaData
      }
    },
    {
      title: '档案信息',
      type: archivesInfo,
      props: {
        headBtnClick: headBtnClick,
        btns: [{ key: 'download', label: '下载' }],
        treeDataList: treeDataList.value
      }
    },
    {
      title: '工作信息',
      type: baseTable,
      props: {
        columusOptions: useProfessionalColumus(),
        lodaData: OperateLogHttp,
        isPagination: true
      }
    }
  ];
  return arr;
});
</script>
<style lang="scss" scoped></style>

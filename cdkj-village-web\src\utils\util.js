import { clone, groupBy, isFunction, isNil, get, isString, isBoolean, isNumber, isUndefined,debounce } from "lodash-es"

export default {
    clone, groupBy, isFunction, isNil, get, isString, isBoolean, isNumber, isUndefined,debounce,
    guid() {
        return URL.createObjectURL(new Blob()).slice(-36);
    },
    isLightColor(color) {
        // 将颜色转换为 RGB
        const hex = color.replace(/^#/, '');
        const r = parseInt(hex.slice(0, 2), 16);
        const g = parseInt(hex.slice(2, 4), 16);
        const b = parseInt(hex.slice(4, 6), 16);

        // 计算与白色的欧几里得距离
        const distance = Math.sqrt(
            Math.pow(255 - r, 2) +
            Math.pow(255 - g, 2) +
            Math.pow(255 - b, 2)
        );

        // 设定阈值，距离越小越接近白色
        const threshold = 50; // 可根据需求调整
        return distance < threshold;
    }

}
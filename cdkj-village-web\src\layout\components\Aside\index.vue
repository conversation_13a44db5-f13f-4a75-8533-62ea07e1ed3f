<script lang="jsx">
import { computed, unref, ref, watch, nextTick, onMounted } from 'vue';
import { useRenderMenuItem } from './useRenderMenuItem';
import { useRouter } from 'vue-router';
import { useLayoutStore } from '@/layout/useLayoutStore';
import { useAppStore } from '@/stores/useAppStore';
import menus from '@/router/route.js';
export default {
  setup(props) {
    const appStore = useAppStore();
    const layoutStore = useLayoutStore();
    const collapse = computed(() => layoutStore.collapse);
    const defaultOpeneds = computed(() =>{});

    const { push } = useRouter();

    const defaultActive = ref('');

    const menuSelect = (index, indexPath, item) => {
      if(index=="mir"){
        window.open(location.origin+location.pathname+"#/mir")
      }
      else{
        push({name:index})
      }
    };

    watch(
      () => layoutStore.activeMenu,
      newMenu => !!newMenu && (defaultActive.value = newMenu.id),
      { immediate: true }
    );

    return () => (
      <aside class={['funi-layout-aside', { 'funi-layout-aside--collapse': collapse.value }]}>
        <div class="w-full flex-1 overflow-hidden">
          <el-scrollbar>
            <el-menu
              defaultActive={unref(defaultActive)}
              defaultOpeneds={unref(defaultOpeneds)}
              collapse={unref(collapse)}
              uniqueOpened={false}
              collapseTransition={false}
              onSelect={menuSelect}
            >
              {{
                default: () => {
                  const { renderMenuItem } = useRenderMenuItem();
                  return renderMenuItem(menus);
                }
              }}
            </el-menu>
          </el-scrollbar>
        </div>
      </aside>
    );
  }
};
</script>

<style lang="scss" scoped src="@/layout/styles/aside.scss"></style>
<style lang="scss" scoped>
.funi-layout-aside {
  .el-menu {
    border: none;

    .iconify {
      margin-right: 5px;
      width: var(--el-menu-icon-width);
      text-align: center;
      font-size: 18px;
      vertical-align: middle;
      flex-shrink: 0;
    }
  }

  .el-button {
    --el-button-bg-color: clear;
    --el-button-hover-bg-color: clear;
    border: none;
  }

  .el-menu-item.is-active {
    background-color: var(--aside-menu-active-bg-color);
  }

  .el-menu-item {
    margin: 4px 0 8px;
  }

  .el-sub-menu__title,
  .el-sub-menu {
    margin: 4px 0;
  }
}
</style>

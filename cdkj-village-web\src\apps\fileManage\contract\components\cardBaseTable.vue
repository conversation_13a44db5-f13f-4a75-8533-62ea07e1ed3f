<template>
  <div>
    <funiGroupTitle :title="title"></funiGroupTitle>
    <div>
      <div class="flex table-boxs">
        <div class="table-list" v-if="tableData.length" v-for="(item, index) in tableData" :key="index">
          <div class="img-box">
            <el-image :src="item.url" fit="cover" @click="openViewer(index)" />
          </div>
          <el-tooltip class="box-item" effect="dark" :content="item.name" placement="top-start">
            <p style="overflow: hidden; text-overflow: ellipsis">{{ item.name }}</p>
          </el-tooltip>
          <p>
            <el-button type="primary" link @click="download(item)">下载</el-button>
          </p>
        </div>
        <el-empty v-else style="width: 100%" />
      </div>
      <CurdPagination
        v-if="pagination"
        ref="pageRef"
        class="funi-curd__pagination"
        style="margin-top: 10px"
        :total="dataTotal"
        :pageSizes="pageSizes"
        @pageChange="doRequest"
      >
        <template #default="params">
          <slot name="pagination_extra" v-bind="params"></slot>
        </template>
      </CurdPagination>
    </div>
    <div ref="viewerContainer">
    <img v-for="item in tableData" :src="item.url" style="display: none;"/>
  </div>
  </div>
</template>

<script setup lang="jsx">
import { computed, ref, watch } from "vue";
import CurdPagination from "@/components/FuniCurdV2/components/CurdPagination.vue";
import "viewerjs/dist/viewer.css";
import Viewer from "viewerjs";

const props = defineProps({
  title: {},
  data: { type: Array, default: () => [] },
  lodaData: Function,
  // 分页
  pagination: { type: Boolean, default: false },
  pageSizes: Array,
  defaultPage: { type: Object, default: () => ({ pageSize: 10, pageNo: 1 }) },
});

const emit = defineEmits(["beforeRequest", "requestError", "afterRequest"]);
const tableData = ref([]);
const searchParams = ref([]);
const dataTotal = ref(0);
const viewerContainer = ref()
const reload = () => {
  doRequest();
};

const doRequest = async (page) => {
  try {
    emit("beforeRequest");
    let list = props.data || [];
    let total;
    if (!!props.lodaData && $utils.isFunction(props.lodaData)) {
      // 分页参数
      const pageParams = { ...page };
      // 查询参数
      const queryParams = { ...searchParams.value };
      const remoteData = await props.lodaData(pageParams, queryParams);
      list = remoteData.list || [];
      total = remoteData.total;
    }
    tableData.value = list || [];
    dataTotal.value = total;
    emit("afterRequest", list);
  } catch (error) {
    emit("requestError", error);
    console.error("doRequest - ", error);
  }
};

doRequest();

function download(item) {
  const link = document.createElement("a");
  link.href = item.url;
  link.download = item.name;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
let viewer;
function openViewer(index) {
  const images = document.querySelectorAll('.viewer-img');
  // 创建新的 Viewer 实例
    // 如果已经有 Viewer 实例，则先销毁
    if (viewer) {
      viewer.destroy();
    }
    // 创建新的 Viewer 实例
    viewer = new Viewer(viewerContainer.value, {
      initialViewIndex:index,
      inline: false, // 不使用内联模式，强制弹出（使用内联样式就会在页面展示，不弹出）
      navbar: true, // 显示导航
      title: false, // 隐藏标题
      toolbar: true, // 显示工具栏
      zoomable: true, // 支持缩放
      rotatable: true, // 支持旋转
      scalable: true, // 支持缩放
      transition: true, // 启用过渡效果
    });
    // 显示图片
    viewer.show();
}
watch(
  () => props.data,
  () => !props.lodaData && reload(),
  { deep: true }
);
</script>

<style lang="scss" scoped>
$spacing: 8px;
.title {
  font-size: 13px;
  font-weight: bold;
  color: var(--multi-tab-text-color-active);
  border: 1px solid #e8e8e8;
  padding: 12px 16px;
}
.table-boxs {
  margin: ($spacing * 2) 0 0 (-$spacing);
  flex-wrap: wrap;
  .img-box {
    aspect-ratio: 1 / 1;
    cursor: pointer;
    overflow: hidden;
    :deep(.el-image) {
      width: 100%;
      height: 100%;
    }
  }
  .table-list {
    width: calc(100% / 6 - $spacing - ($spacing * 2));
    margin: 0 0 $spacing $spacing;
    padding: $spacing;
    border-radius: 6px;
    p {
      text-align: center;
      margin: 6px 0;
      font-size: 13px;
    }
  }
}
</style>

package cn.fight.village.domain.doc.repository;

import cn.fight.village.domain.doc.entity.Document;
import cn.fight.village.domain.doc.query.DocumentQuery;
import cn.fight.village.domain.doc.value.DocumentListVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 合同档案信息mapper
 *
 */
public interface DocumentMapper extends BaseMapper<Document> {
    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    List<DocumentListVo> queryList(DocumentQuery query);

    /**
     * 根据合同ID获取档案
     *
     * @param contractId
     * @return
     */
    DocumentListVo selectByContractId(String contractId);

    /**
     * 土地流转档案
     * @param query
     * @return
     */
    List<DocumentListVo> queryList4Trans(DocumentQuery query);
}

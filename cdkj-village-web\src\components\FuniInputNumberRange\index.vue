<template>
  <div class="funi-input-number-range">
    <funi-input-number placeholder="请输入" v-model="minValue" />
    <div class="divider">~</div>
    <funi-input-number placeholder="请输入" v-model="maxValue" />
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  modelValue: { type: [Array, String], default: () => [] }
});

const emit = defineEmits(['update:modelValue']);
const minValue = ref();
const maxValue = ref();

watch(
  () => props.modelValue,
  () => {
    const [min, max] = props.modelValue;
    minValue.value = min;
    maxValue.value = max;
  }
);

watch(
  () => [minValue.value, maxValue.value],
  ([min, max]) => {
    emit('update:modelValue', [min, max]);
  }
);
</script>

<style lang="scss" scoped>
.funi-input-number-range {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;

  .divider {
    padding: 0 10px;
    width: 32px;
    flex-shrink: 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
  }

  :deep(.el-input-number) {
    flex: 1;
    width: auto;
    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }

    .el-input__wrapper {
      padding: 1px 11px !important;
    }
  }
}
</style>

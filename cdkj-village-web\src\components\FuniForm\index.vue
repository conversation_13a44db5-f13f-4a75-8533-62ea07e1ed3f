<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2022-12-03 23:11:25
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-09-20 17:54:53
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniForm/index.vue
 * @Description: FuniForm
 * Copyright (c) 2022 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <el-form
    ref="elFormRef"
    label-width="auto"
    v-bind="$attrs"
    scroll-to-error
    label-position="right"
    require-asterisk-position="left"
    :inline="inline"
    :model="formModel"
    :class="['funi-form', { form_border: border && !inline }]"
    @submit.native.prevent
  >
    <funi-wrap :useWrap="!inline" :wrapProps="{ gutter: 20 }" type="ElRow">
      <funi-wrap
        type="ElCol"
        :useWrap="!inline"
        v-for="item in validSchema"
        :wrapProps="{ key: item.prop, ...getColProps(item) }"
        :class="{ '!hidden': isHidden(item) }"
      >
        <el-form-item :key="item.prop" v-bind="item" :class="{ 'el-form-item__label-hidden': item.labelHidden }">
          <!-- default -->
          <template #default>
            <div class="funi-form-item__content">
              <slot :name="item.slots.default" v-bind="{ item, formModel }">
                <component
                  :is="componentProp(item.component, { item, formModel })"
                  v-model="formModel[item.prop]"
                  v-bind="item.props || {}"
                  v-on="item.on || {}"
                />
              </slot>
              <div class="funi-form-item__extra">
                <slot v-if="item.slots.extra" :name="item.slots.extra" :v-bind="{ item, formModel }" />
                <component v-else-if="item.extra" :is="extraRender(item, formModel)" />
              </div>
            </div>
          </template>
          <!-- label -->
          <template #label>
            <slot :name="item.slots.label" v-bind="{ item, formModel }">
              {{ item.label || '' }}
            </slot>
          </template>
        </el-form-item>
      </funi-wrap>
    </funi-wrap>
  </el-form>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref, unref, isVNode } from 'vue';

defineOptions({
  name: 'FuniForm',
  inheritAttrs: false
});

// Props
const props = defineProps({
  /** 行内模式 */
  inline: Boolean,

  /** 可配置多列item，最多4列 */
  col: Number,

  /** 表单选项配置 */
  schema: Array,
  border: { type: Boolean, default: true }
});

// Emits
const emit = defineEmits(['get-form']);

// Properties
const elFormRef = ref();
const formModel = ref({});

// Computed Properties
const validSchema = computed(() => {
  return (
    props.schema
      //过滤掉prop不存在的配置
      .filter(item => item.prop && !isHidden(item))
      .map(item => {
        const slots = Object.assign({ default: `${item.prop}_default`, label: `${item.prop}_label` }, item.slots || {});
        return Object.assign(item, { slots });
      })
  );
});

const isHidden = item => {
  if (typeof item.hidden == 'function') {
    return item.hidden({ item, formModel: unref(formModel) });
  }
  return Boolean(item.hidden);
};

// 默认ElCol响应式布局配置
const defaultColProps = computed(() => {
  //最多3列 默认1列
  const colCount = Math.max(1, Math.min(4, props.col || 1));
  const span = 24 / colCount;
  const middleSpan = Math.max(span, 12);
  return { xs: 24, sm: middleSpan, md: middleSpan, lg: span, xl: span };
});

// Lifecycle Hooks
onMounted(() => {
  emit(
    'get-form',
    Object.assign({}, unref(elFormRef), {
      validate,
      getValues,
      setValues,
      validateField,
      resetFields,
      scrollToField,
      clearValidate
    })
  );
});

// Methods
function componentProp(component, props) {
  if (typeof component == 'string') return component;

  const value = props.formModel[props.item.prop];
  if (typeof component == 'function') return component({ ...props, value });

  return <div style="word-break: break-all;">{value !== 0 ? value || '--' : value}</div>;
}

function getColProps(formItem) {
  if (props.inline) return {};
  const col = formItem.colProps || {};
  if (!!col.span) return col;
  return { ...unref(defaultColProps), ...col };
}

async function validate() {
  const fields = unref(validSchema)
    .filter(s => !isHidden(s))
    .map(item => item.prop);
  return validateField(fields);
}

async function validateField(field = []) {
  try {
    const isValid = await elFormRef.value.validateField(field);
    return Promise.resolve({ isValid, error: null, values: unref(formModel) });
  } catch (error) {
    return Promise.resolve({ isValid: false, error, values: unref(formModel) });
  }
}

function resetFields(props) {
  unref(elFormRef).resetFields(props);
}

function scrollToField(prop) {
  unref(elFormRef).scrollToField(prop);
}

function clearValidate(props) {
  unref(elFormRef).clearValidate(props);
}

function getValues() {
  return unref(formModel);
}

function setValues(values) {
  formModel.value = Object.assign(unref(formModel), values || {});
}

function extraRender(item, formModel) {
  if (typeof item.extra == 'string') return <span>{item.extra}</span>;
  if (typeof item.extra == 'function') {
    const content = item.extra({ item, formModel });
    return isVNode(content) ? content : <span>{content || ''}</span>;
  }
  return '';
}

defineExpose({
  elFormRef,
  formModel,
  validSchema,
  getValues,
  setValues,
  validate,
  validateField,
  resetFields,
  scrollToField,
  clearValidate
});
</script>

<style lang="less" scoped>
:deep(.el-form-item__label-hidden .el-form-item__label) {
  display: none;
}

:deep(.funi-curd) {
  padding: 0 !important;
}
</style>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
.funi-form-pd {
  box-sizing: border-box;
  padding: 10px 20px;
}

.funi-form {
  position: relative;

  &::before,
  &::after {
    content: '';
    display: inline-block;
    width: 1px;
    height: 100%;
    background: var(--funi-form-border-color);
    position: absolute;
    top: 0;
    z-index: 2000;
  }

  &::before {
    left: -10px;
  }

  &::after {
    right: -10px;
  }

  > :deep(.el-row) {
    overflow: hidden;
    position: relative;

    &::before,
    &::after {
      content: '';
      display: inline-block;
      height: 1px;
      width: 100%;
      background: var(--funi-form-border-color);
      position: absolute;
      left: 0;
      z-index: 2000;
    }

    &::before {
      top: 0;
      z-index: 2000;
    }

    &::after {
      bottom: 0;
    }

    > .el-col {
      position: relative;
      border-right: 1px solid var(--funi-form-border-color);
      border-bottom: 1px solid var(--funi-form-border-color);
      padding: 0 !important;

      &::before {
        content: '';
        display: inline-block;
        height: 1px;
        width: 100%;
        background: var(--funi-form-border-color);
        position: absolute;
        top: -1px;
        left: 0;
        z-index: 1;
      }
    }
  }

  .funi-form-item__content {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .funi-form-item__extra {
    font-size: 12px;
    line-height: 1;
    padding: 3px 0px;

    &:empty,
    &:has(> span:empty) {
      padding: 0;
    }
  }
}

:deep() {
  @include el.b(form-item) {
    margin-bottom: 0;
    transition: all 0.1s;
    background-color: var(--funi-form-label-bgc);
    height: 100%;
    align-items: stretch;
    &.is-error > .#{el.$namespace}-form-item__content {
      padding-bottom: 18px;

      > .#{el.$namespace}-form-item__error {
        top: unset;
        bottom: 3px;
        left: 10px;
      }
    }

    &::before {
      content: '';
      display: inline-block;
      width: 1px;
      background: var(--funi-form-border-color);
      order: 2;
    }

    > .#{el.$namespace}-form-item__content {
      padding: 6px 9px;
      order: 3;
      background-color: #fff;
    }

    > .#{el.$namespace}-form-item__label-wrap {
      align-items: center;
      order: 1;
    }
    .#{el.$namespace}-form-item__label {
      padding: 0 3px 0 10px;
      height: unset;
      align-items: center;
    }

    > .#{el.$namespace}-form-item__label:after,
    > .#{el.$namespace}-form-item__label-wrap > .#{el.$namespace}-form-item__label:after {
      content: ':';
      color: el.getCssVar('text-color', 'regular');
      margin-left: 4px;
    }
  }
}
</style>

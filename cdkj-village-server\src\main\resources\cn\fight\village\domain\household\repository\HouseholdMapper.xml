<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.household.repository.HouseholdMapper">

    <select id="selectPageList" resultType="cn.fight.village.domain.household.vo.HouseholdListVo">
        select
            h.uuid,
            h.household_code householdCode,
            h.location,
            h.poor,
            h.lower,
            m.name,
            m.gender,
            m.id_type idType,
            m.id_code idCode,
            m.phone
        from public.rlams_household h
        left join public.rlams_member m on h.householder_id = m.uuid and m.deleted = 0
        where h.deleted = 0
        <if test="name != null and name != ''">
            and m.name like '%' || #{name} || '%'
        </if>
        <if test="householdCode != null and householdCode != ''">
            and h.household_code = #{householdCode}
        </if>
        <if test="poor != null and poor != ''">
            and h.poor = #{poor}
        </if>
        <if test="lower != null and lower != ''">
            and h.lower = #{lower}
        </if>
        <if test="idCode != null and idCode != ''">
            and m.id_code = #{idCode}
        </if>
        order by h.create_time desc
    </select>

    <select id="queryHoseMember" resultType="cn.fight.village.domain.household.vo.HouseMember">
        select
            m.uuid,
            h.uuid householdId,
            h.household_code householdCode,
            h.location,
            m.name,
            m.id_code idCode,
            m.householder,
            m.phone,
            m.gender,
            m.relation,
            m.id_type idType
        from public.rlams_household h
        inner join public.rlams_member m on h.uuid = m.house_id and m.deleted = 0
        where h.deleted = 0
        <if test="keyword != null and keyword != ''">
            and (
                h.household_code = #{keyword}
                or (m.name like '%' || #{keyword} || '%'  and m.householder = 1)
                or (m.id_code = #{keyword} and m.householder = 1)
            )
        </if>
        order by h.household_code,m.householder desc
    </select>

    <select id="queryHoseMemberByHouseId" resultType="cn.fight.village.domain.household.vo.HouseMember">
         select
            m.uuid,
            h.household_code householdCode,
            h.location,
            m.name,
            m.id_code idCode,
            m.householder
        from public.rlams_household h
        inner join public.rlams_member m on h.uuid = m.house_id and m.deleted = 0
        where h.deleted = 0 and h.uuid = #{houseId}
    </select>

    <select id="getHouseHoldByHouseholder"
            resultType="cn.fight.village.domain.household.entity.Household">
        select
            h.uuid,
            h.householder_id householderId,
            h.household_code householdCode,
            h.location,
            h.poor,
            h.lower,
            h.backup,
            h.deleted,
            h.creator_id creatorId,
            h.updater_id updaterId,
            h.deleter_id deleterId,
            h.create_time createTime,
            h.update_time updateTime,
            h.delete_time deleteTime
        from public.rlams_household h
        inner join public.rlams_member m on h.householder_id = m.uuid and m.deleted = 0
        where  h.deleted = 0 and m.relation = '户主'
         <if test="houseHolderId != null and houseHolderId != ''">
             and m.id_code = #{houseHolderId}
         </if>
         <if test="houseHolderName != null and houseHolderName != ''">
             and m.name like '%' || #{houseHolderName} || '%'
         </if>
    </select>

    <select id="getHouseHoldByHouseholderName"
            resultType="cn.fight.village.domain.household.vo.HouseholderVo">
        select
        h.uuid,
        h.household_code householdCode,
        h.location,
        m.name householder
        from public.rlams_household h
        inner join public.rlams_member m on h.householder_id = m.uuid and m.deleted = 0
        where  h.deleted = 0 and m.relation = '户主'
        and m.name like '%' || #{houseHolderName} || '%'
    </select>

    <select id="selectHouseHoldByLandNo" resultType="java.util.Map">
        select distinct h.uuid ,
                        h.location,
                        m.name
        from rlams_land l
                 inner join rlams_contract ct on l.contract_id = ct.uuid and  ct.deleted = 0
                 inner join rlams_household h on h.uuid = ct.household_id and h.deleted = 0
                 inner join rlams_member m on m.uuid = h.householder_id and m.deleted = 0
        where l.land_no = #{landNo} and ct.type = '家庭承包' and l.deleted = 0
    </select>
</mapper>
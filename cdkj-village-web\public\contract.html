<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>农村土地经营权出租合同</title>
  <style>
    body,
    div,
    dl,
    dt,
    dd,
    ul,
    ol,
    li,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    pre,
    code,
    form,
    fieldset,
    legend,
    input,
    button,
    textarea,
    p,
    blockquote,
    th,
    td {
      margin: 0;
      padding: 0;
    }

    .container {
      width: 640px;
      margin: 0 auto;
      font-size: 18px;
      font-family: "宋体";
      line-height: 2;
    }

    .title {
      font-size: 40px;
      padding: 50px 0;
      text-align: center;
      line-height: 1.5;
    }

    .section {
      font-size: 22px;
    }

    p {
      margin: 5px 0;
      text-indent: 2em;
      text-align: justify;
      margin: 4px 0;
    }

    .bold {
      font-weight: bold;
      font-family: "microsoft yahei";
    }

    .line {
      display: flex;
      align-items: center;
    }

    .line input[type="text"] {
      flex: 1;
    }

    input[type="text"] {
      height: 20px;
      border-bottom: 1px solid #000 !important;
      border-right: none;
      border-left: none;
      border-top: none;
      vertical-align: text-top;
      color: #000;
      outline: none;
    }

    input[type="number"] {
      height: 20px;
      border-bottom: 1px solid #000 !important;
      border-right: none;
      border-left: none;
      border-top: none;
      vertical-align: text-top;
      color: #000;
      outline: none;
    }
    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    input[type="radio"] {
      appearance: none;
      -webkit-appearance: none;
      width: 14px;
      height: 14px;
      cursor: pointer;
      border: 1px solid #676565;
      position: relative;

    }

    /* 用伪元素模拟选中状态 */
    input[type="radio"]:checked::before {
      content: '✓';
      position: absolute;
      left: 2px;
      top: -6px;
      font-size: 16px;
    }

    input[type="checkbox"] {
      appearance: none;
      -webkit-appearance: none;
      width: 14px;
      height: 14px;
      cursor: pointer;
      border: 1px solid #676565;
      position: relative;

    }

    /* 用伪元素模拟选中状态 */
    input[type="checkbox"]:checked::before {
      content: '✓';
      position: absolute;
      left: 2px;
      top: -6px;
      font-size: 16px;
    }

    label input {
      margin-right: 2px;
    }

    input,
    button,
    textarea,
    select {
      font-family: inherit;
      font-size: inherit;
      font-weight: inherit;
      background: transparent;
    }

    input[disabled] {
      cursor: not-allowed;
    }

    .center {
      text-align: center;
    }

    .textarea {
      border: 1px solid #ddd;
      border: 1px solid #ddd;
      min-height: 74px;
      padding: 0 10px;
    }

    .date-input {
      width: 2.4em;
      text-align: center;
    }

    table {
      border-collapse: collapse;
      table-layout: fixed;
      width: 100%;
    }

    th,
    td {
      border: 1px solid;
      text-align: center;
      word-break: break-all;
    }

    p .required {
      color: #f00 !important;
      border-bottom: 1px solid #f00 !important;
    }

    .underlined-text {
      border-bottom: 1px solid;
      text-underline-offset: 4px;
      text-decoration-thickness: 0px;
      outline: none;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1 class="title">农村土地经营权出租合同</h1>

    <div class='section'>
      <p>
        根据《中华人民共和国民法典》《中华人民共和国农村土地承包 法》和《农村土地经营权流转管理办法》等相关法律法规，本着平等、自愿、公平、诚信、有偿的原则， 经甲乙双方协商一致，就土地经营权出租事宜，签订本合同。
      </p>
    </div>
    <div class="content">
      <p class="bold">一、当事人</p>
      <p class="line">甲方（出租方）：<input type="text" id="partyA" required /></p>
      <p class="line">身份证号码：<input type="text" id="aId" required /></p>
      <p class="line">联系地址：<input type="text" id="aLocation" required /></p>
      <p>
        经营主体类型：
        <label><input type="radio" name="aType" value="1" />自然人</label>
        <label><input type="radio" name="aType" value="2" />农村承包经营户</label>
        <label><input type="radio" name="aType" value="3" />农民专业合作社 </label>
        <label><input type="radio" name="aType" value="4" />家庭农场 </label>
        <label><input type="radio" name="aType" value="5" />农村集体经济组织 </label>
        <label><input type="radio" name="aType" value="6" />公司</label>
        <label><input type="radio" name="aType" value="7" />其他</label>
        <input type="text" id="aTypeOther" style="width:320px" />
      </p>
      <p style="padding-top: 20px;"></p>
      <p class="line">乙方（承租方）：<input type="text" id="partyB" required /></p>
      <p class="line">社会信用代码：<input type="text" id="bId" required /></p>
      <p class="line">法定代表人：<input type="text" id="bReprese" required /></p>
      <p class="line">身份证号码：<input type="text" id="bRepId" required /></p>
      <p class="line">联系地址：<input type="text" id="bLocation" required /></p>
      <p class="line">联系电话：<input type="text" id="bPhone" required /></p>
      <p>
        经营主体类型：
        <label><input type="radio" name="bType" value="1" />自然人</label>
        <label><input type="radio" name="bType" value="2" />农村承包经营户</label>
        <label><input type="radio" name="bType" value="3" />农民专业合作社 </label>
        <label><input type="radio" name="bType" value="4" />家庭农场 </label>
        <label><input type="radio" name="bType" value="5" />农村集体经济组织 </label>
        <label><input type="radio" name="bType" value="6" />公司</label>
        <label><input type="radio" name="bType" value="7" />其他</label>
        <input type="text" id="bTypeOther" />
      </p>
      <p class="bold">二、租赁物</p>
      <p>（一）经自愿协商， 甲方将<input type="text" class="center" id="totalArea" disabled />亩土地经营权（具体见下表及附图）出租给乙方。</p>
      <p>
      <table>
        <thead>
          <tr>
            <th rowspan="2">序号</th>
            <th rowspan="2">村（组）</th>
            <th rowspan="2">地块名称</th>
            <th rowspan="2">地块代码</th>
            <th colspan="4">坐落（四至）</th>
            <th rowspan="2">面积（亩）</th>
            <th rowspan="2">质量等级</th>
            <th rowspan="2">土地类型</th>
            <th rowspan="2">承包合同代码</th>
            <th rowspan="2">备注</th>
          </tr>
          <tr>
            <th>东</th>
            <th>南</th>
            <th>西</th>
            <th>北</th>
          </tr>
        </thead>
        <tbody class="landTableBody">
        </tbody>
      </table>
      </p>
      <p>（二）出租土地上的附属建筑和资产情况现状描述：
      <div class="textarea" id="subject" contenteditable="true"></div>
      </p>
      <p>
        出租土地上的附属建筑和资产的处置方式描述（可另附件）：
      <div class="textarea" name="subjectDetail" contenteditable="true"></div>
      </p>
      <p class="bold">三、出租土地用途</p>
      <p>出租土地用途为<input type="text" id="usage" /></p>
      <p class="bold">四、租赁期限</p>
      <p>出租期限自
        <input type="number" name="startTime" class="date-input" required />年
        <input type="number" name="startTime" class="date-input" required />月
        <input type="number" name="startTime" class="date-input" required />日
        起至
        <input type="number" name="endTime" class="date-input" required />年
        <input type="number" name="endTime" class="date-input" required />月
        <input type="number" name="endTime" class="date-input" required />日
        止。
      </p>
      <p class="bold">五、出租土地交付时间</p>
      <p>甲方应于
        <input type="number" class="date-input" name="deliverTime" required />年
        <input type="number" class="date-input" name="deliverTime" required />月
        <input type="number" class="date-input" name="deliverTime" required />日
        前完成土地交付。
      </p>
      <p class="bold">六、租金及青苗支付方式</p>
      <p>（一）租金标准</p>
      <p>即每亩每年人民币 <input type="number" id="rent" required />元(大写： <span id="rentChn"></span> )。</p>
      <p>租金变动：根据当地土地流转价格水平，每 <input type="number" name="textObject.rentUpdateYear" required /> 年调整一次租金。 具体调整方式： <span class="underlined-text"
          type="text" name="rentUpdate" required contenteditable="true"></span></p>
      <p>（二）租金支付</p>
      <p>乙方第一次支付租金之后每年须于 12月31前支付(<label><input type="radio" name="rentPay" value="1" />前</label> <label><input type="radio"
            name="rentPay" value="2" />后</label>）一年租金 。</p>
      <p>（三）青苗支付</p>
      <div class="textarea" name="cropsPay" contenteditable="true"></div>
      <p>（四）付款方式</p>
      <p>银行汇款</p>
      <p class="line">甲方账户名称：<input type="text" id="aAccountName" required /></p>
      <p class="line">银行账号：<input type="text" id="aAccountCode" required /></p>
      <p class="line">开户行：<input type="text" id="aAccountBank" required /></p>
      <p class="bold">七、甲方的权利和义务</p>
      <p>（一）甲方的权利</p>
      <p>1.要求乙方按合同约定支付租金；</p>
      <p>2.监督乙方按合同约定的用途依法合理利用和保护出租土地；</p>
      <p>3.制止乙方损害出租土地和农业资源的行为；</p>
      <p>4.租赁期限届满后收回土地经营权；</p>
      <p class="line">5.其他：<input type="text" name="textObject.aAuthOther" /></p>
      <p>（一）甲方的义务</p>
      <p>1.按照合同约定交付出租土地；</p>
      <p>2.合同生效后<input type="number" class="date-input" name="textObject.aDutyDate" />日内依据《中华人民共和国农村土地承包法》 第三十六条的规定向发包方备案
      </p>
      <p>3.不得干涉和妨碍乙方依法进行的农业生产经营活动；</p>
      <p class="line">4.其他：<input type="text" name="textObject.aDutyOther" /></p>
      <p class="bold">八、乙方的权利和义务</p>
      <p>（一）乙方的权利</p>
      <p>1.要求甲方按照合同约定交付出租土地；</p>
      <p>2.在合同约定的期限内占有农村土地， 自主开展农业生产经营并 取得收益；</p>
      <p>3.经甲方同意，乙方依法投资改良土壤，建设农业生产附属、配 套设施，并有权按照合同约定对其投资部分获得合理补偿；</p>
      <p>4.租赁期限届满，有权在同等条件下优先承租；</p>
      <p class="line">5.其他：<input type="text" name="textObject.bAuthOther" /></p>
      <p>（二）乙方的义务</p>
      <p>1.按照合同约定及时接受出租土地并按照约定向甲方支付租金；</p>
      <p>2.在法律法规政策规定和合同约定允许范围内合理利用出租土地，确保农地农用，符合当地粮食生产等产业规划，不得弃耕抛荒，不得破坏农业综合生产能力和农业生态环境；</p>
      <p>3.依据有关法律法规保护出租土地，禁止改变出租土地的农业用
        途，禁止占用出租土地建窑、建坟或者擅自在出租土地上建房、挖砂、采石、采矿、取土等，禁止占用出租的永久基本农田发展林果业和挖 塘养鱼；
      </p>
      <p class="line">4.其他：<input type="text" name="textObject.bDutyOther" /></p>
      <p class="bold">九、其他约定</p>
      <p>（一 ）甲方同意乙方依法</p>
      <p style="display: flex;">
        <label style="flex:1"><input type="checkbox" name="textObject.aAgreeb" value="1" />投资改良土壤</label>
        <label style="flex:1.5"><input type="checkbox" name="textObject.aAgreeb" value="2" />建设农业生产附属、配套设施</label>
      </p>
      <p style="display: flex;">
        <label style="flex:1"><input type="checkbox" name="textObject.aAgreeb" value="3" />以土地经营权融资担保</label>
        <label style="flex:1.5"><input type="checkbox" name="textObject.aAgreeb" value="4" />再流转土地经营权</label>
      </p>
      <p class="line">
        <label><input type="checkbox" name="textObject.aAgreeb" value="5" />其他</label>
        <input type="text" name="textObject.aAgreebOther" />
      </p>
      <p>
        （二）该出租土地的财政补贴等归属：<span class="underlined-text" name="textObject.btgs" contenteditable="true"></span> 。
      </p>
      <p>
        （三）本合同期限内， 出租土地被依法征收、征用、占用时，有 关地上附着物及青苗补偿费的归属：<span class="underlined-text" name="textObject.bcgs"
          contenteditable="true"></span>。
      </p>
      <p>
        （四）其他事项：<input type="text" name="textObject.qtsx" />
      </p>
      <p>十、合同变更、解除和终止</p>
      <p>（一）合同有效期间，因不可抗力因素致使合同全部不能履行时，本合同自动终止，甲方将合同终止日至租赁到期日的期限内已收取的租金退还给乙方；致使合同部分不能履行的，其他部分继续履行，租金可以作相应调整。</p>
      <p>（二）如乙方在合同期满后需要继续经营该出租土地，必须在合同期满前<input type="number" class="date-input" name="textObject.contractDate1" />
        日内书面向甲方提出申请。如乙方不再继续经营的，必须在合同期满前 <input type="number" class="date-input"
          name="textObject.contractDate2" />日内书面通知甲方，并在合同期满后<input type="number" class="date-input"
          name="textObject.contractDate3" /> 日内将原出租的土地交还给甲方。</p>
      <p>（三）合同到期或者未到期由甲方依法提前收回出租土地时，乙方依法投资建设的农业生产附属、配套设施处置方式：</p>
      <p><label><input type="checkbox" name="textObject.czfs" value="1" />由甲方无偿处置。</label></p>
      <p><label><input type="checkbox" name="textObject.czfs" value="2" />经双方协商后，由甲方支付价款购买。</label></p>
      <p><label><input type="checkbox" name="textObject.czfs" value="3" />由乙方恢复原状。 </label></p>
      <p class="line">
        <label><input type="checkbox" name="textObject.czfs" value="4" />其他：</label>
        <input type="text" name="textObject.czfsOther" />
      </p>
      <p>十一、违约责任</p>
      <p>（一）任何一方违约给对方造成损失的，违约方应承担赔偿责任</p>
      <p>（二）甲方应按合同规定按时向乙方交付土地，逾期一 日应向乙方支付年租金的千分之<input type="number" class="date-input"
          name="textObject.wyzzMoney1" />作为违约金。逾期超过 <input type="number" class="date-input" name="textObject.wyzzDate1" />
        日，乙方有权解除合同，甲方应当赔偿损失。</p>
      <p>（三）甲方出租的土地存在权属纠纷或经济纠纷，致使合同全部或部分不能履行的，甲方应当赔偿损失。</p>
      <p>（四）甲方违反合同约定擅自干涉和破坏乙方的生产经营,致使乙方无法进行正常的生产经营活动的，乙方有权解除合同， 甲方应当赔偿损失。</p>
      <p>（五）乙方应按照合同规定按时足额向甲方支付租金， 逾期一 日 乙方应向甲方支付年租金的千分之<input type="number" class="date-input"
          name="textObject.wyzzMoney2" /> 作为违约金。逾期超过<input type="number" class="date-input"
          name="textObject.wyzzDate2" />日，甲方有权解除合同，乙方应当赔偿损失。</p>
      <p>（六）乙方擅自改变出租土地的农业用途、弃耕抛荒连续两年以上、给出租土地造成严重损害或者严重破坏土地生态环境的，甲方有权解除合同、收回该土地经营权，并要求乙方赔偿损失。</p>
      <p>（七）合同期限届满的， 乙方应当按照合同约定将原出租土地交还给甲方，逾期一日应向甲方支付年租金的千分之<input type="number" class="date-input"
          name="textObject.wyzzRatio" />作为违约金。</p>
      <p>十二、合同争议解决方式</p>
      <p>本合同发生争议的，
        甲乙双方可以协商解决，也可以请求村民委员会、乡（镇）人民政府等调解解决。当事人不愿协商、调解或者协商、调解不成的，可以依据《中华人民共和国农村土地承包法》第五十五条的规定向农村土地承包仲裁委员会申请仲裁，
        也可以直接向人民法院起诉。</p>
      <p>十二、合同争议解决方式</p>
      <p>十三、附则</p>
      <p>（一）本合同未尽事宜，经甲方、乙方协商一致后可签订补充协议。补充协议与本合同具有同等法律效力。</p>
      <p class="line">补充条款（可另附件）：<input type="text" name="textObject.bctk" /></p>
      <p>（二）本合同自甲乙双方签字、盖章或者按指印之日起生效。本合同一式 肆份，由甲方、乙方、农村集体经济组织、乡（镇）人民政府农村土地承包管理部门，各执一份</p>
      <p style="padding-top:100px"></p>
      <div style="display: flex;">
        <div style="flex:1">
          <div style="height:100px;text-indent: 2em;">甲方：</div>
          <p>农户代表人签字：</p>
          <p>签订时间：<input type="number" class="date-input" name="textObject.aSignTime" />年 <input type="number"
              class="date-input" name="textObject.aSignTime" />月<input type="number" class="date-input"
              name="textObject.aSignTime" />日</p>
          <p class="line"><span style="white-space: nowrap;">签订地点：</span><input type="text"
              name="textObject.aSignLocation" /></p>
        </div>
        <div style="flex:1">
          <div style="height:100px;display: flex;">
            <div style="flex-shrink: 0;text-indent: 2em;">乙方：</div>
            <div></div>
          </div>
          <p>法定代表人签字：</p>
          <p>签订时间：<input type="number" class="date-input" name="textObject.bSignTime" />年 <input type="number"
              class="date-input" name="textObject.bSignTime" />月<input type="number" class="date-input"
              name="textObject.bSignTime" />日</p>
          <p class="line"><span style="white-space: nowrap;">签订地点：</span><input type="text"
              name="textObject.bSignLocation" /></p>
        </div>
      </div>
    </div>
  </div>
</body>
<script>
  /**
   * 初始化
  */
  function init() {
    document.querySelector("#rent").oninput = res => {
      if (res.target.value) {
        document.querySelector("#rentChn").innerHTML = amountToChinese(parseFloat(res.target.value))
      }
      else {
        ocument.querySelector("#rentChn").innerHTML = ""
      }
    }
    document.querySelectorAll("[contenteditable]").forEach(item=>{
      item.addEventListener('input', function() {
        console.log(111)
        const text = this.innerText;
        if (text.length > 200) {
          // 截断超出部分
          this.innerText = text.substring(0, 200);
          // 将光标移动到末尾
          const range = document.createRange();
          range.selectNodeContents(this);
          range.collapse(false);
          const selection = window.getSelection();
          selection.removeAllRanges();
          selection.addRange(range);
        }
      });
    })
    //
    document.querySelectorAll(`input`).forEach(item => {
      //必填
      if (item.required) {
        item.addEventListener("input", () => {
          if (!item.value) {
            item.classList.add("required")
          }
          else {
            item.classList.remove("required")
          }
        })
      }
    })
  }
  init();

  function setDetail(){
    document.querySelectorAll("body [id]").forEach(item=>{
      item.setAttribute("readonly",true)
      item.removeAttribute("contenteditable")
    })
    document.querySelectorAll("body [name]").forEach(item=>{
      item.setAttribute("readonly",true)
      item.setAttribute("disabled",true)
    })
  }
  let lands;//土地信息
  /**
   * 设置默认值
  */
  function setValues(obj) {
    for (let key in obj) {
      let value = obj[key];
      //土地
      if (key == "lands") {
        lands = value;
        const tbody = document.querySelector(".landTableBody");
        let htmlString = ""
        value.forEach((item, index) => {
          htmlString += `
          <tr>
            <td>${(index + 1)}</td>
            <td contenteditable="true">${item.village}</td>
            <td>${item.landName}</td>
            <td>${item.landNo}</td>
            <td>${item.east}</td>
            <td>${item.south}</td>
            <td>${item.west}</td>
            <td>${item.north}</td>
            <td>${item.area}</td>
            <td contenteditable="true">${item.quality}</td>
            <td>${item.landType}</td>
            <td contenteditable="true">${item.contractCode}</td>
            <td>${item.remark}</td>
          </tr>`;
        })
        tbody.innerHTML = htmlString;
      }
      else if (key == "textObject") {
        value = typeof value =="object"?value : JSON.parse(value)
        for (let i in value) {
          let val = value[i];
          let el = document.getElementsByName(`textObject.${i}`)

          el.forEach((item,index) => {
            if (item.type == 'radio' || item.type == 'checkbox') {
              if ((val || '').split(",").includes(item.value)) {
                item.checked = true
              }
            }
            else if (item.nodeName == 'INPUT') {//日期
              item.value = (val || '').split("-")[index] || ''
            }
            else {
              item.textContent = val
            }
          })
        }
      }
      else {
        let el = document.querySelector(`[id=${key}]`)
        if (el) {
          if (el.nodeName == 'INPUT') {
            el.value = value
          }
          else {
            el.textContent = value
          }
        }
        else {
          el = document.querySelectorAll(`[name=${key}]`);
          el.forEach((item, index) => {
            if (item.type == 'radio' || item.type == 'checkbox') {
              if ((value || '').split(",").includes(item.value)) {
                item.checked = true
              }
            }
            else if (item.nodeName == 'INPUT') {//日期
              item.value = value.split("-")[index] || ''
            }
            else {
              item.textContent = value
            }
          })
        }

      }
    }
  }

  /**
   * 获取表单值
  */
  function getValues() {
    return new Promise((resolve, reject) => {

      let obj = {}
      let isPass = true;
      document.querySelectorAll("body [id]").forEach(item => {
        if (!isPass) {
          return;
        }
        if (item.required && !item.value) {
          item.classList.add("required")
          item.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest"
          });
          isPass = false;
        }
        obj[item.id] = item.value || item.textContent
      })

      document.querySelectorAll("body [name]").forEach(item => {
        if(!item.name) return;
        let key = item.name
        let arr = item.name.split(".")
        let _obj = obj;
        if(arr.length>1){
          if(!obj[arr[0]]){
            obj[arr[0]] = {}
          }
          _obj = obj[arr[0]]
          key = arr[1]
        }
       
        if (item.type == 'radio') {
          if(item.checked){
              _obj[key] = item.value
          }
        }
        else if (item.type == 'checkbox') {
          if(item.checked){
            _obj[key] = (_obj[key] ? _obj[key] + "," : "") + item.value
          }
        }
        else {//日期
          _obj[key] = (_obj[key]?_obj[key]+"-" : '')+ item.value
        }
      })
      //json字段
      if(obj.textObject){
        obj.textObject = JSON.stringify(obj.textObject)
      }
      //土地信息
      const table = document.querySelector('.landTableBody');
        // 遍历每一行
        for (let i = 0; i < table.rows.length; i++) {
          const row = table.rows[i];
          lands[i].village = row.cells[1].textContent
          lands[i].quality = row.cells[9].textContent
          lands[i].contractCode = row.cells[11].textContent

      }
      obj.lands= lands;
      isPass ? resolve(obj) : reject()
    })
  }

  /**
   * 金额转换
  */
  function amountToChinese(num) {
    var str1 = '零壹贰叁肆伍陆柒捌玖';  //0-9所对应的汉字
    var str2 = '万仟佰拾亿仟佰拾万仟佰拾元角分'; //数字位所对应的汉字
    var str3;    //从原num值中取出的值
    var str4;    //数字的字符串形式
    var str5 = '';  //人民币大写金额形式
    var i;    //循环变量
    var j;    //num的值乘以100的字符串长度
    var ch1;    //数字的汉语读法
    var ch2;    //数字位的汉字读法
    var nzero = 0;  //用来计算连续的零值是几个

    num = Math.abs(num).toFixed(2);  //将num取绝对值并四舍五入取2位小数
    str4 = (num * 100).toFixed(0).toString();  //将num乘100并转换成字符串形式
    j = str4.length;      //找出最高位

    str2 = str2.substr(15 - j);

    //循环取出每一位需要转换的值
    for (i = 0; i < j; i++) {
      str3 = str4.substr(i, 1);   //取出需转换的某一位的值
      if (i != (j - 3) && i != (j - 7) && i != (j - 11) && i != (j - 15)) {    //当所取位数不为元、万、亿、万亿上的数字时
        if (str3 == '0') {
          ch1 = '';
          ch2 = '';
          nzero = nzero + 1;
        }
        else {
          if (str3 != '0' && nzero != 0) {
            ch1 = '零' + str1.substr(str3 * 1, 1);
            ch2 = str2.substr(i, 1);
            nzero = 0;
          }
          else {
            ch1 = str1.substr(str3 * 1, 1);
            ch2 = str2.substr(i, 1);
            nzero = 0;
          }
        }
      }
      else { //该位是万亿，亿，万，元位等关键位
        if (str3 != '0' && nzero != 0) {
          ch1 = "零" + str1.substr(str3 * 1, 1);
          ch2 = str2.substr(i, 1);
          nzero = 0;
        }
        else {
          if (str3 != '0' && nzero == 0) {
            ch1 = str1.substr(str3 * 1, 1);
            ch2 = str2.substr(i, 1);
            nzero = 0;
          }
          else {
            if (str3 == '0' && nzero >= 3) {
              ch1 = '';
              ch2 = '';
              nzero = nzero + 1;
            }
            else {
              if (j >= 11) {
                ch1 = '';
                nzero = nzero + 1;
              }
              else {
                ch1 = '';
                ch2 = str2.substr(i, 1);
                nzero = nzero + 1;
              }
            }
          }
        }
      }
      if (i == (j - 11) || i == (j - 3)) {  //如果该位是亿位或元位，则必须写上
        ch2 = str2.substr(i, 1);
      }
      str5 = str5 + ch1 + ch2;

      if (i == j - 1 && str3 == '0') {   //最后一位（分）为0时，加上“整”
        str5 = str5 + '整';
      }
    }
    if (num == 0) {
      str5 = '零元整';
    }
    return str5;
  }
</script>

</html>
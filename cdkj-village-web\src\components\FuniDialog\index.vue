<template>
  <div>
    <el-dialog
      class="funi-dialog"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="false"
      v-bind="$attrs"
      v-model="dialogVisible"
      :width="width"
      :center="false"
      :draggable="false"
      :align-center="true"
    >
      <template v-if="!hideFooter" #footer="params">
        <slot name="footer" v-bind="params || {}">
          <div class="funi-dialog__footer">
            <el-button @click="handleCancel"> 取消 </el-button>
            <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
          </div>
        </slot>
      </template>
      <template v-for="slot in compSlots" #[slot]="params">
        <slot :name="slot" v-bind="params || {}" />
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, useAttrs, watch, ref, useSlots } from 'vue';

defineOptions({
  name: 'FuniDialog',
  inheritAttrs: false
});

const props = defineProps({
  /**
   * Dialog大小
   * small - 600px
   * default - 800px
   * large - 1000px
   * max - (100vw - 40px * 2) 最大宽度为浏览器宽度减去左右各40px。高度同。例如1280窗口，对话框最大宽度为1280-80=1200
   */
  size: {
    type: String,
    default: 'default',
    validator(value) {
      return ['small', 'default', 'large', 'max'].includes(value);
    }
  },

  modelValue: Boolean,

  // 是否隐藏footer
  hideFooter: Boolean
});

const emit = defineEmits(['update:modelValue']);

const attrs = useAttrs();
const slots = useSlots();

// 拦截footer
const compSlots = computed(() => Object.keys(slots).filter(key => key !== 'footer'));

const width = computed(() => {
  switch (props.size) {
    case 'small':
      return '600px';
    case 'default':
      return '800px';
    case 'large':
      return '1000px';
    case 'max':
      return 'calc(100vw - 80px)';
    default:
      return '800px';
  }
});

const dialogVisible = ref(false);

// Watcher
watch(
  () => props.modelValue,
  val => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, val => {
  emit('update:modelValue', val);
});

// Function

/** 取消 */
const handleCancel = () => {
  if (!!attrs.onCancel && $utils.isFunction(attrs.onCancel)) {
    attrs.onCancel();
  } else {
    dialogVisible.value = false;
  }
};

/** 确认 */
const handleConfirm = () => {
  if (!!attrs.onConfirm && $utils.isFunction(attrs.onConfirm)) {
    attrs.onConfirm();
  } else {
    dialogVisible.value = false;
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-dialog__footer) {
  padding: 0px 20px;
  height: 64px;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>

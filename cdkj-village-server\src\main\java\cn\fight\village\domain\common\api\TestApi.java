package cn.fight.village.domain.common.api;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.user.entity.User;
import com.alibaba.fastjson.JSON;;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

/**
 * 测试接口
 */
@Controller
@RequestMapping("test")
public class TestApi {

    @Value("${business.env}")
    private String env;

    @ResponseBody
    @GetMapping("hello")
    public String hello(@UserInfo User user, String input) {
        System.out.println("JSON.toJSONString(user) = " + JSON.toJSONString(user));
        return "hello ,input is '" + input + "'";
    }

    /**
     * 系统异常全局处理测试-post
     * @return
     */
    @GetMapping("sysException")
    @ResponseBody
    public JsonResult sysExceptionTest(String input) {
        int i = 1 / 0;
        return JsonResult.successMessage("请求成功");
    }

    /**
     * 业务异常全局处理测试
     * @param input
     * @return
     */
    @GetMapping("bizException")
    @ResponseBody
    public JsonResult bizExceptionTest(String input) {
        if ("yes".equals(input))
            throw new BusinessException("业务出现异常了哦");
        return JsonResult.successMessage("请求成功");
    }

}

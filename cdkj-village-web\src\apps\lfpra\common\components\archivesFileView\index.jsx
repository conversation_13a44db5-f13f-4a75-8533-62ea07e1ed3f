/**
 *  档案文件夹
 *
 *  @param { array } treeDataList : 数据 - tree树格式
 *  @param { array } btns : 操作按钮 - default:[ { key: 'download', label: '下载' } ]
 *  @param { function } headBtnClick : 操作回调
 *
 */
import { ref, reactive, watch } from 'vue';
import styles from './fileStyle.module.less';
import ControlsView from './ControlsView';
export default {
  props: {
    treeDataList: {
      type: Function,
      default: [
        {
          label: 'Level one 1',
          children: [
            {
              label: 'Level two 1-1',
              children: [
                {
                  label: 'Level three 1-1-1'
                }
              ]
            }
          ]
        },
        {
          label: 'Level one 2',
          children: [
            {
              label: 'Level two 2-1',
              children: [
                {
                  label: 'Level three 2-1-1'
                }
              ]
            },
            {
              label: 'Level two 2-2',
              children: [
                {
                  label: 'Level three 2-2-1'
                }
              ]
            }
          ]
        },

        {
          label: 'Level one 4',
          children: [
            {
              label: 'Level two 1-4',
              children: [
                {
                  label: 'Level three 1-1-4',
                  children: [
                    {
                      label: 'Level three 1-1-1-4'
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          label: 'Level one 5',
          children: [
            {
              label: 'Level two 1-5',
              children: [
                {
                  label: 'Level three 1-1-5'
                }
              ]
            }
          ]
        },
        {
          label: 'Level one 6',
          children: [
            {
              label: 'Level two 1-6',
              children: [
                {
                  label: 'Level three 1-1-6'
                }
              ]
            }
          ]
        },
        {
          label: 'Level one 3'
        }
      ]
    },
    btns: {
      type: Array,
      default: []
    },
    headBtnClick: {
      type: Function,
      default: () => {}
    }
  },
  setup(props) {
    let { headBtnClick } = props;
    const treeData = ref();
    let btns = ref();
    // 面包屑数据
    let breadcrumbOptions = ref([
      {
        id: 'root',
        creatorName: '根目录'
      }
    ]);
    // 选中文件内数据
    const childerList = ref();

    // 点击文件夹回调
    const treeClick = res => {
      if (res.children) {
        if (!breadcrumbOptions.value.find(item => item.id == res.id)) {
          breadcrumbOptions.value.push({ id: res.id, creatorName: res.fileName });
        }
        childerList.value = res.children;
      }
    };

    // 面包屑回调
    const breadcrumbClick = item => {
      // 返回根目录
      if (item.id === 'root') {
        childerList.value = treeData.value;
        breadcrumbOptions.value = [
          {
            id: 'root',
            creatorName: '根目录'
          }
        ];
      } else {
        let findIndex =  breadcrumbOptions.value.findIndex(itemChild=>itemChild.id == item.id)
        breadcrumbOptions.value.splice(findIndex+1, breadcrumbOptions.value.length-findIndex-1)
        console.log(breadcrumbOptions.value,'breadcrumbOptions.value--');
        filterTree(treeData.value, item);
      }
    };
    const filterTree = (data, res) => {

      data.forEach(item => {
        if (item.id === res.id) {
          childerList.value = item.children;
          return;
        } else {
          if (!item.children) return;
          filterTree(item.children, res);
        }
      });
    };

    watch(
      () => props.treeDataList,
      () => {
        treeData.value = props.treeDataList;
        childerList.value = props.treeDataList;
        btns.value = props.btns;
        breadcrumbOptions.value = [
          {
            id: 'root',
            creatorName: '根目录'
          }
        ];
      },
      { immediate: true, deep: true }
    );

    return { childerList, treeClick, breadcrumbOptions, breadcrumbClick, headBtnClick, btns };
  },
  render() {
    let { breadcrumbOptions, treeClick, headBtnClick, btns, breadcrumbClick, childerList } = this;
    let childern = { treeClick, headBtnClick, btns };
    return (
      <div class={styles.archivesInfo}>
        <el-breadcrumb separator="/" style="width:100%;padding: 10px;box-sizing: border-box;">
          {breadcrumbOptions.map(item => (
            <el-breadcrumb-item
              onClick={e => {
                e.stopPropagation();
                breadcrumbClick(item);
              }}
            >
              <a>{item.creatorName}</a>
            </el-breadcrumb-item>
          ))}
        </el-breadcrumb>
        <article class={styles.container}>
          {childerList &&
            childerList.map((item, index) => {
              return <ControlsView key={index} item={item} {...childern} />;
            })}
        </article>
      </div>
    );
  }
};

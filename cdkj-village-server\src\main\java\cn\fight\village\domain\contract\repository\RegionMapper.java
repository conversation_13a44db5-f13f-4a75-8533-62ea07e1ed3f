package cn.fight.village.domain.contract.repository;

import cn.fight.village.domain.common.entity.Region;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface RegionMapper extends BaseMapper<Region> {
    /**
     * 查询子区域
     *
     * @param regionCode
     * @return
     */
    List<Region> selectByParent(String regionCode);

    /**
     * 根据code获取区域
     *
     * @param regionCode
     * @return
     */
    Region selectByCode(String regionCode);
}

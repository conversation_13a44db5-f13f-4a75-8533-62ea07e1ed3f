<template>
  <vxe-grid
    ref="xGrid"
    v-bind="options"
    :data="data"
    :columns="columns"
    :row-config="rowConfig"
    :edit-rules="editRules"
    :edit-config="editConfig"
  ></vxe-grid>
</template>

<script setup>
import { computed, reactive, ref } from 'vue';
import { VXETable } from 'vxe-table';
import Render from './element-plus-render';
VXETable.use(Render);

const props = defineProps({
  editable: { type: Boolean, default: true },
  columns: { type: Array, default: () => [] },
  data: { type: Array, default: () => [] },
  rowKey: String
});

const xGrid = ref();

const options = reactive({
  border: true,
  showHeaderOverflow: true,
  showOverflow: true,
  keepSource: true,
  id: $utils.guid(),
  columnConfig: { resizable: true },
  headerAlign: 'center'
});

const editRules = computed(() => {
  if (!props.columns) return [];
  return props.columns
    .filter(col => col.field && col.rules)
    .reduce((prev, col) => {
      prev[col.field] = col.rules;
      return prev;
    }, {});
});

const editConfig = computed(() => {
  return { mode: 'row', trigger: 'click', showIcon: false, showStatus: true, enabled: props.editable };
});

const rowConfig = computed(() => ({ keyField: props.rowKey, isHover: true }));

const getTableData = async ({ validate = false } = {}) => {
  const { tableData } = xGrid.value.getTableData() || {};
  if (validate && !!(await xGrid.value.validate(true))) {
    return Promise.reject();
  }
  return Promise.resolve(tableData);
};

const insertRowAt = async index => {
  const newRow = await xGrid.value.createRow({});
  return xGrid.value.insertAt(newRow, index);
};
const insertRow = () => insertRow(-1);

const removeRow = async row => {
  return xGrid.value.remove(row);
};

defineExpose({
  xGrid,
  getTableData,
  insertRow,
  insertRowAt,
  removeRow
});
</script>

<style lang="scss" scoped>
:deep() {
  .col--valid-error > .vxe-cell > .el-input .el-input__wrapper,
  .col--valid-error > .vxe-cell > .el-autocomplete .el-input__wrapper,
  .col--valid-error > .vxe-cell > .el-input-number .el-input__wrapper,
  .col--valid-error > .vxe-cell > .el-select .el-input__wrapper,
  .col--valid-error > .vxe-cell > .el-cascader .el-input__wrapper,
  .col--valid-error > .vxe-cell > .el-date-picker .el-input__wrapper,
  .col--valid-error > .vxe-tree-cell > .el-input .el-input__wrapper,
  .col--valid-error > .vxe-tree-cell > .el-autocomplete .el-input__wrapper,
  .col--valid-error > .vxe-tree-cell > .el-input-number .el-input__wrapper,
  .col--valid-error > .vxe-tree-cell > .el-select .el-input__wrapper,
  .col--valid-error > .vxe-tree-cell > .el-cascader .el-input__wrapper,
  .col--valid-error > .vxe-tree-cell > .el-date-picker .el-input__wrapper {
    box-shadow: 0 0 0 1px #f56c6c inset;
  }

  .vxe-table--render-default .vxe-body--column.col--valid-error .vxe-cell--valid {
    width: 100%;
    position: absolute;
    left: 50%;
    font-size: 12px;
    line-height: 1.2em;
    transform: translateX(-50%);
    text-align: left;
    pointer-events: none;
    z-index: 4;
    padding-left: var(--vxe-table-cell-padding-left);
    padding-right: var(--vxe-table-cell-padding-right);
  }
}
</style>

<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-08-14 19:55:24
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-16 17:12:13
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniGroupTitle\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funi-group-title"
    :style="{'border-left-color':titleStyle&&titleStyle.color?titleStyle.color:'var(--el-color-primary)',margin:groupMargin}">
    <slot :title="titleComputed"
      :titleStyle="titleStyle">
      <div class="title"
        :style="titleStyle">
        {{ titleComputed }}
      </div>
    </slot>
  </div>
</template>

<script>
import { computed } from 'vue'
export default {
  name: 'FuniGroupTitle',
  components: {

  },
  props: {
    modelValue: [String],
    title: [String],
    titleStyle: {
      type: Object,
      default: () => {
        return {
          color: 'var(--el-color-primary)',
          marginLeft: '8px',
          fontSize: '18px'
        }
      }
    },
    groupMargin: {
      type: String,
      default: '16px 0px'
    }
  },
  setup (props) {
    const titleComputed = computed(() => {
      return props.modelValue ?? props.title;
    })
    return {
      titleComputed
    }
  }
}
</script>
<style lang="scss" scoped>
.funi-group-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-left: 4px solid;
  line-height: 24px;
}
</style>
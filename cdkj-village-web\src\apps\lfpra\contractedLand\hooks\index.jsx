import Upload from '@/apps/lfpra/common/components/upload/upload.vue';
import { apiUrlCommon } from '@/apps/lfpra/common/hooks/api.js';
import { apiUrl } from './api.js';
import { moreBtnRender,erm_intl } from '@/apps/lfpra/common/hooks/utils.jsx';
export const userTableColumns = ({ seeDateils, editFunc = () => {}, delFunc = () => {} }) => {
  return [
    {
      label: '承包经营权证书号',
      prop: 'cerNumber',
      render: ({ row, index }) => {
        return (<el-button type="primary" link  onClick={()=>seeDateils} >{row.memberCode}</el-button>);
      },
      fixed: 'left'
    },
    {
      label: '地确权（合同）总面积（亩）',
      prop: 'awardCertArea',
      render: ({ row }) => {
        return `${erm_intl(row.awardCertArea)}`;
      }
    },
    {
      label: '土地承包用途',
      prop: 'contractingUse'
    },
    {
      label: '发包方名称',
      prop: 'employerName'
    },
    {
      label: '发包方负责人姓名',
      prop: 'employerHeadName'
    },
    {
      label: '承包方式',
      prop: 'dicConModeName'
    },
    {
      label: '承包方（代表）名称',
      prop: 'contractorName'
    },
    {
      label: '承包方地址',
      prop: 'address'
    },
    {
      label: '核查时间',
      prop: 'createTime'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => {
                _edit(row);
              }}
            >
              编辑
            </el-button>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                deleteClick(row);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ];
};

// 按钮
export const useBtnsConfig = ({ addFunc = () => {}, importFunc = () => {},exportFunc=()=>{} }) => {
  return [
    {
      component: () => (
        // <el-button v-auth="ERM_CONTRACTINCOME_ADD" onClick={addFunc} type="primary">
        //   新建
        // </el-button>
         <el-button onClick={addFunc} type="primary">
         新增
       </el-button>
      )
    },
    {
      component: () => (
        // <Upload
        //   v-auth="ERM_PROJECT_MANPOWER_IMPORT"
        //   dc={downloadCode.ps}
        //   url={apiUrl.monthInvestmentInfoImport}
        //   callbackFun={importFunc}
        //   templateName="员工信息"
        //   title="批量导入"
        //   type="primary"
        // >
        //   导入
        // </Upload>
        <Upload
        dc={apiUrlCommon.downloadImportTemplate}
        dcParams={{downloadTemplate:'JTCB_INFO'}}
        url={apiUrl.importSiteInfo}
        callbackFun={importFunc}
        templateName="家庭承包信息模板"
        title="批量导入"
        type="primary"
      >
        导入
      </Upload>
      )
    },
    {
      component: () => (
        // <el-button v-auth="ERM_CONTRACT_COLLECTION_EXPORT" onClick={exportFun} type="primary">
        //   导出
        // </el-button>
         <el-button onClick={exportFunc} type="primary">
         导出
       </el-button>
      )
    }
  ];
};

package cn.fight.village.handler;

import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.service.CacheService;
import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.task.CheckLicenceTask;
import cn.hutool.core.convert.NumberWithFormat;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 认证拦截器
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {
    //token有效时间
    private static final long TOKEN_FAILURE_TIME = 30 * 60 * 1000;

    private CacheService cacheService;

    @Resource
    private CheckLicenceTask checkLicenceTask;

    @Autowired
    public AuthInterceptor(CacheService cacheService) {
        this.cacheService = cacheService;
    }

    /**
     * 登录拦截
     * @param request 请求体
     * @param response 响应体
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!CheckLicenceTask.getLicenceAvailable()) {
            throw new BusinessException(401,"应用系统许可证不可用，请联系软件服务开发商");
        }

        String token = request.getHeader("token");
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(401,"未登录");
        }

        //验签
        /*JWT jwt = null;
        String tokenStr = null;
        try {
            tokenStr = SecureUtils.stringDecode(token);
            jwt = JWT.of(tokenStr);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException(401,"无效token");
        }

        if (jwt == null) {
            throw new BusinessException(401, "无效token");
        }

        try {
            JWTUtil.parseToken(tokenStr);
        } catch (Exception e) {
            throw new BusinessException(401,"token格式异常");
        }

        boolean pass = false;
        try {
            pass = SecureUtils.verifySigner(tokenStr);
        }catch (Exception e) {
            log.error("验签失败" + e.getMessage());
            log.error("token="+tokenStr);
            //throw new BusinessException(401, "验签异常");
        }

        if (pass) {
            //throw new BusinessException(401, "令牌验签未通过");
            log.error("令牌验签未通过");
        }*/
        String tokenStr = null;
        try {
            tokenStr = SecureUtils.stringDecode(token);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException(401,"令牌解密失败,请重新登录");
        }
        DecodedJWT decodedJWT = null;

        try {
            decodedJWT = SecureUtils.verifyToken(tokenStr);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(401,"令牌校验失败,请重新登录");
        }
        Long createTime = decodedJWT.getClaim("create").asLong();

        //有效时间验证
        /*NumberWithFormat createTimeFormat = (NumberWithFormat)jwt.getPayload("create");
        if (createTimeFormat == null ) {
            throw new BusinessException("获取令牌有效时间失败");
        }*/

        //long failureTime = createTimeFormat.longValue() + TOKEN_FAILURE_TIME;
        long failureTime = createTime + TOKEN_FAILURE_TIME;
        if (failureTime < DateUtil.current()) {
            throw new BusinessException(401,"令牌失效,请重新登录");
        }

        //从缓存中获取用户
        User user = cacheService.getUserLoginCache(tokenStr);
        if (user == null) {
            throw new BusinessException(401, "登录失效,请重新登录");
        }

        //todo token续费
        return true;
    }
}

<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-07-26 11:16:37
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-08-22 14:51:16
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniFormV2/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->

<template>
  <el-form
    ref="elFormRef"
    v-bind="$attrs"
    scroll-to-error
    label-position="right"
    require-asterisk-position="left"
    :inline="inline"
    :model="formModel"
    :class="['funi-form-v2', { form_border: border && !inline }]"
    @submit.native.prevent
  >
    <div :class="['funi-form-v2-grid', { expand }]">
      <template v-for="item in validSchema" :key="item.prop">
        <div :class="{ '!hidden': isHidden(item) }" class="funi-form-v2-grid__item-label">{{ item.label }}</div>
        <el-form-item
          :class="{ '!hidden': isHidden(item) }"
          v-bind="item"
          class="funi-form-v2-grid__item el-form-item__label-hidden"
        >
          <!-- default -->
          <template #default>
            <slot :name="item.slots.default" v-bind="{ item, formModel }">
              <component
                :is="componentProp(item.component, { item, formModel })"
                v-model="formModel[item.prop]"
                v-bind="item.props || {}"
                v-on="item.on || {}"
              />
            </slot>
          </template>
          <!-- label -->
          <template #label>
            <slot :name="item.slots.label" v-bind="{ item, formModel }">
              {{ item.label || '' }}
            </slot>
          </template>
        </el-form-item>
      </template>
    </div>
  </el-form>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref, unref } from 'vue';

defineOptions({
  name: 'FuniFormV2',
  inheritAttrs: false
});

// Props
const props = defineProps({
  /** 行内模式 */
  inline: Boolean,

  /** 可配置多列item，最多4列 */
  col: Number,

  /** 表单选项配置 */
  schema: Array,
  border: { type: Boolean, default: true },
  expand: Boolean
});

// Emits
const emit = defineEmits(['get-form']);

// Properties
const elFormRef = ref();
const formModel = ref({});

// Computed Properties
const validSchema = computed(() => {
  return (
    props.schema
      //过滤掉prop不存在的配置
      .filter(item => $utils.isString(item.prop) && !!item.prop)
      .map(item => {
        const slots = Object.assign({ default: `${item.prop}_default`, label: `${item.prop}_label` }, item.slots || {});
        return Object.assign(item, { slots });
      })
  );
});

const isHidden = item => {
  if ($utils.isBoolean(item.hidden)) return item.hidden;

  if ($utils.isFunction(item.hidden)) {
    return item.hidden({ item, formModel: unref(formModel) });
  }
  return false;
};

// 默认ElCol响应式布局配置
const defaultColProps = computed(() => {
  //最多3列 默认1列
  const colCount = Math.max(1, Math.min(4, props.col || 1));
  const span = 24 / colCount;
  const middleSpan = Math.max(span, 12);
  return { xs: 24, sm: middleSpan, md: middleSpan, lg: span, xl: span };
});

// Lifecycle Hooks
onMounted(() => {
  emit(
    'get-form',
    Object.assign({}, unref(elFormRef), {
      validate,
      getValues,
      setValues,
      validateField,
      resetFields,
      scrollToField,
      clearValidate
    })
  );
});

// Methods
function componentProp(component, props) {
  Object.assign(props, { value: props.formModel[props.item.prop] });
  if ($utils.isString(component)) return component;
  if ($utils.isFunction(component)) return component(props);
  const value = props.formModel[props.item.prop];
  return <div style="word-break: break-all;">{value !== 0 ? value || '--' : value}</div>;
}

function getColProps(formItem) {
  if (props.inline) return {};
  const col = formItem.colProps || {};
  if (!!col.span) return col;
  return { ...unref(defaultColProps), ...col };
}

async function validate() {
  const fields = unref(validSchema)
    .filter(s => !isHidden(s))
    .map(item => item.prop);
  return validateField(fields);
}

async function validateField(field = []) {
  try {
    const isValid = await elFormRef.value.validateField(field);
    return Promise.resolve({ isValid, error: null, values: unref(formModel) });
  } catch (error) {
    return Promise.resolve({ isValid: false, error, values: unref(formModel) });
  }
}

function resetFields(props) {
  unref(elFormRef).resetFields(props);
}

function scrollToField(prop) {
  unref(elFormRef).scrollToField(prop);
}

function clearValidate(props) {
  unref(elFormRef).clearValidate(props);
}

function getValues() {
  return unref(formModel);
}

function setValues(values) {
  formModel.value = Object.assign(unref(formModel), values || {});
}

defineExpose({
  elFormRef,
  formModel,
  validSchema,
  getValues,
  setValues,
  validate,
  validateField,
  resetFields,
  scrollToField,
  clearValidate
});
</script>

<style lang="less" scoped>
:deep(.el-form-item__label-hidden .el-form-item__label) {
  display: none;
}

:deep(.funi-curd) {
  padding: 0 !important;
}
</style>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
:deep() {
  @include el.b(form-item) {
    > .#{el.$namespace}-form-item__label:after,
    > .#{el.$namespace}-form-item__label-wrap > .#{el.$namespace}-form-item__label:after {
      content: ':';
      color: el.getCssVar('text-color', 'regular');
      margin-left: 4px;
    }

    > .#{el.$namespace}-form-item__content > *:nth-child(1) {
      width: 100% !important;
    }
  }
}
</style>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
:deep() {
  .funi-form-v2-grid {
    display: grid;
    column-gap: 10px;
    grid-template-columns: repeat(3, auto minmax(min-content, 1fr)) auto [tool-start] minmax(min-content, 1fr) [tool-end];
    grid-template-rows: auto;
    &__item-label {
      display: inline-flex;
      align-items: center;
      justify-content: flex-end;

      flex: 0 0 auto;
      font-size: var(--el-form-label-font-size);
      color: var(--el-text-color-regular);
      height: 32px;
      line-height: 32px;
      box-sizing: border-box;
      align-self: center;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: keep-all;

      &:after {
        content: ':';
        color: el.getCssVar('text-color', 'regular');
        margin-left: 4px;
      }

      &:empty:after {
        content: '';
      }
    }

    &__item {
      margin: 8px 0px !important;
      &:last-child {
        grid-column-start: tool-start;
        grid-column-end: tool-end;
        min-width: 220px;
      }
    }
  }
}

@media (max-width: 1800px) {
  .funi-form-v2-grid.expand {
    grid-template-columns: repeat(2, auto minmax(min-content, 1fr)) auto [tool-start] minmax(min-content, 1fr) [tool-end];
  }
}

@media (max-width: 1000px) {
  .funi-form-v2-grid.expand {
    grid-template-columns: repeat(1, auto minmax(min-content, 1fr)) auto [tool-start] minmax(min-content, 1fr) [tool-end];
  }
}
</style>

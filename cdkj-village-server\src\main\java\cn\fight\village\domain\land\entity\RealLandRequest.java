package cn.fight.village.domain.land.entity;

import cn.fight.village.domain.common.entity.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 地理土地请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RealLandRequest extends BaseRequest {
    //删除二次确认
    private Integer sure;

    //地块编码
    private String landNo;

    //真实土地对象
    private RealLand land;

    //gis土地对象
    private GisLand gisLand;

    //承包家庭ID
    private String householdId;
    //承包户户主
    private String householder;

    //承包方姓名
    private String underName;

    //地块编码列表
    private List<String> landNoList;
}

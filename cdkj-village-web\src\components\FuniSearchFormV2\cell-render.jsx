import SearchFormDateItem from './cell/searchFormDateItem.vue';
import SearchFormInputItem from './cell/searchFormInputItem.vue';
import SearchFormSelectItem from './cell/searchFormSelectItem.vue';
import SearchFormRegionItem from './cell/searchFormRegionItem.vue';
import SearchFormInputNumberItem from './cell/searchFormInputNumberItem.vue';
import SearchFormInputNumberRangeItem from './cell/searchFormInputNumberRangeItem.vue';

const cellMap = {
  DATE: ({ model, fieldItem }) => (
    <SearchFormDateItem
      v-model={model.value}
      column={model.column}
      operate={model.operate}
      attribute={fieldItem.attribute}
    />
  ),
  TEXT: ({ model, fieldItem }) => (
    <SearchFormInputItem
      v-model={model.value}
      column={model.column}
      operate={model.operate}
      attribute={fieldItem.attribute}
    />
  ),
  SELECT: ({ model, fieldItem }) => (
    <SearchFormSelectItem
      v-model={model.value}
      column={model.column}
      operate={model.operate}
      attribute={fieldItem.attribute}
    />
  ),
  REGION: ({ model, fieldItem }) => (
    <SearchFormRegionItem
      v-model={model.value}
      column={model.column}
      operate={model.operate}
      attribute={fieldItem.attribute}
    />
  ),
  NUMBER: ({ model, fieldItem }) => (
    <SearchFormInputNumberItem
      v-model={model.value}
      column={model.column}
      operate={model.operate}
      attribute={fieldItem.attribute}
    />
  ),
  NUMBER_RANGE: ({ model, fieldItem }) => (
    <SearchFormInputNumberRangeItem
      v-model={model.value}
      column={model.column}
      operate={model.operate}
      attribute={fieldItem.attribute}
    />
  )
};

/**
 * 内置渲染器
 */
const innerRenderKeys = ['DATE', 'TEXT', 'SELECT', 'REGION', 'NUMBER', 'NUMBER_RANGE'];

export const cellRender = {
  get(name) {
    return cellMap[name] || null;
  },

  add(name, render) {
    if (name && !innerRenderKeys.includes(name) && render) {
      cellMap[name] = render;
    }
    return cellRender;
  }
};

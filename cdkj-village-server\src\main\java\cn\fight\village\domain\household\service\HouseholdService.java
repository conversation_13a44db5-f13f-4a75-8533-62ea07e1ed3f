package cn.fight.village.domain.household.service;

import cn.fight.village.domain.common.constant.BusinessConstant;
import cn.fight.village.domain.common.entity.BaseEntity;
import cn.fight.village.domain.common.entity.BaseQuery;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.domain.homestead.entity.Homestead;
import cn.fight.village.domain.homestead.service.HomesteadService;
import cn.fight.village.domain.household.query.HouseholdListQuery;
import cn.fight.village.domain.household.repository.HouseholdMapper;
import cn.fight.village.domain.household.repository.HouseholdMemberMapper;
import cn.fight.village.domain.household.entity.Household;
import cn.fight.village.domain.household.entity.HouseholdMember;
import cn.fight.village.domain.household.request.HouseholdMemberRequest;
import cn.fight.village.domain.household.request.HouseholdRequest;
import cn.fight.village.domain.household.vo.*;
import cn.fight.village.domain.user.entity.User;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.compat.Jre19Compat;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 家庭户信息服务层
 *
 */
@Slf4j
@Service
public class HouseholdService {

    @Resource
    private HouseholdMemberMapper householdMemberMapper;

    @Resource
    private HouseholdMapper householdMapper;

    @Lazy
    @Resource
    private HomesteadService homesteadService;

    /**
     * 添加家庭户信息
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult addHousehold(User user, HouseholdRequest request) {
        this.checkRepeat(request.getHouseholdCode());
        String houseId = CommonUtils.getGuid(); //户ID

        //获取户主信息
        List<HouseholdMemberRequest> members = request.getMembers();
        String householderId = this.insertHouseMember(members,user,houseId);

        //保存家庭信息
        Household household = new Household();
        BeanUtils.copyProperties(request,household);
        household.manageCreateInfo(user);
        household.setHouseholderId(householderId);
        household.setUuid(houseId);
        householdMapper.insert(household);

        return JsonResult.valueOfObject(household.getUuid());
    }

    /**
     * 维护家庭成员信息
     *
     * @param members
     * @param user
     * @param houseId
     * @return 户主ID
     */
    private String insertHouseMember(List<HouseholdMemberRequest> members,User user,String houseId) {
        String householderId = null;

        if (CollectionUtils.isNotEmpty(members)) {
            //成员对象转换
            for (HouseholdMemberRequest member : members) {

                HouseholdMember householdMember = new HouseholdMember();
                BeanUtils.copyProperties(member,householdMember);
                householdMember.manageCreateInfo(user);
                householdMember.setHouseId(houseId);

                //户主信息获取
                if (BusinessConstant.TRUE.equals(member.getHouseholder())) {
                    if (StringUtils.isNotBlank(householderId)) {
                        throw new BusinessException("户主成员只能存在一个");
                    }

                    householderId = householdMember.getUuid();
                }

                //敏感数据加密
                SecureUtils.sensitiveFieldEncrypt(householdMember);
                householdMemberMapper.insert(householdMember);
            }
        }

        return householderId;
    }

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    public JsonResult queryList(HouseholdListQuery query) {
        PageHelper.startPage(query.getPageNo(),query.getPageSize());
        List<HouseholdListVo> result = householdMapper.selectPageList(query);
        if (!CollectionUtils.isEmpty(result)) { //敏感数据解密
            result.forEach(SecureUtils::sensitiveFieldDecrypt);
        }

        return JsonResult.valueOfObject(new PageInfo<>(result));
    }

    /**
     * 根据户编号查询户对象
     *
     * @param code
     * @return
     */
    public Household getByHouseholdCode(String code) {
        return householdMapper.selectOne(new LambdaQueryWrapper<Household>()
                .eq(Household::getHouseholdCode, code)
                .eq(BaseEntity::getDeleted,User.IS_NOT_DELETED));
    }

    /**
     * 对象查询
     *
     * @param uuid
     * @return
     */
    public JsonResult queryInfo(String uuid) {
        Household household = householdMapper.selectOne(new LambdaQueryWrapper<Household>()
                .eq(Household::getUuid, uuid)
                .eq(BaseEntity::getDeleted, Household.IS_NOT_DELETED));

        if (household == null) {
            throw new BusinessException("未查询到对应的户信息");
        }

        HouseholdVo result = new HouseholdVo();
        BeanUtils.copyProperties(household,result);

        List<HouseholdMember> members = householdMemberMapper.selectList(new LambdaQueryWrapper<HouseholdMember>()
                .eq(HouseholdMember::getHouseId, result.getUuid())
                .eq(HouseholdMember::getDeleted, HouseholdMember.IS_NOT_DELETED)
        );

        if (CollectionUtils.isNotEmpty(members)) {
            List<HouseholdMemberVo> memberVos = members.stream().map(i -> {
                HouseholdMemberVo memberVo = new HouseholdMemberVo();
                BeanUtils.copyProperties(i, memberVo);

                //敏感数据解密
                SecureUtils.sensitiveFieldDecrypt(memberVo);

                return memberVo;
            }).collect(Collectors.toList());

            result.setMembers(memberVos);
        }

        return JsonResult.valueOfObject(result);
    }

    /**
     * 对象删除
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult remove(HouseholdRequest request,User user) {
        String uuid = request.getUuid();
        CommonUtils.notNull(uuid,"待删除ID不能为空");

        List<Homestead> homesteads = homesteadService.selectHomesteadByHouseId(uuid);
        if (CollectionUtils.isNotEmpty(homesteads)) {
            throw new BusinessException("当前家庭户已经绑定宅基地信息，无法删除");
        }

        Household deleter = new Household();
        deleter.manageDeleteInfo(user);
        deleter.setUuid(uuid);

        int delMems = householdMemberMapper.deleteByHouseId(uuid,user.getUuid());
        log.debug("删除了" + delMems + "条成员数据");

        if (householdMapper.updateById(deleter) != 1) {
            throw new BusinessException("删除失败");
        }

        return JsonResult.successMessage("删除成功");
    }

    /**
     * 检查户编号是否重复
     *
     * @param householdCode
     */
    private synchronized void checkRepeat(String householdCode) {
        CommonUtils.notNull(householdCode, "户编号不能为空");
        Household existHousehold = this.getByHouseholdCode(householdCode);
        if (existHousehold != null) {
            throw new BusinessException("户编号不允许重复");
        }
    }

    /**
     * 数据更新
     *
     * @param request
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult update(HouseholdRequest request, User user) {
        String householdId = request.getUuid();
        CommonUtils.notNull(householdId,"待修改的件ID不能为空");

        //数据校验
        Household household = householdMapper.selectById(householdId);
        if (household == null) {
            throw new BusinessException("待更新的对象不存在");
        }
        //户编号重复校验
        if (!household.getHouseholdCode().equals(request.getHouseholdCode())) {
            this.checkRepeat(request.getHouseholdCode());
        }

        //更新户信息
        Household updater = new Household();
        BeanUtils.copyProperties(request,updater);
        updater.manageUpdateInfo(user);

        //更新成员信息
        householdMemberMapper.deleteByHouseId(householdId,user.getUuid()); //清除旧数据
        String householderId = this.insertHouseMember(request.getMembers(), user, householdId);

        updater.setHouseholderId(householderId);
        if (householdMapper.updateById(updater) != 1) {
            throw new BusinessException("更新失败");
        }

        return JsonResult.valueOfObject(householdId);
    }

    /**
     * 通过家庭ID获取家庭成员
     *
     * @param houseId
     * @return
     */
    public List<HouseholdMember> getHouseholdMemberByHouseId(String houseId) {
        return householdMemberMapper.selectList(new LambdaQueryWrapper<HouseholdMember>()
                .eq(HouseholdMember::getHouseId,houseId)
                .eq(HouseholdMember::getDeleted,HouseholdMember.IS_NOT_DELETED)
        );
    }

    /**
     * 获取家庭成员信息
     *
     * @param query
     * @return
     */
    public JsonResult getHouseMembers(BaseQuery query) {
        query.checkPageParam();
        PageHelper.startPage(query.getPageNo(),query.getPageSize());
        List<HouseMember> result = householdMapper.queryHoseMember(query);
        return JsonResult.valueOfObject(new PageInfo<>(result));
    }

    /**
     * 根据户ID获取家庭成员
     *
     * @param houseId
     * @return
     */
    public List<HouseMember> getHouseMembersByHouseId(String houseId) {
        if (StrUtil.isEmpty(houseId)) {
            return null;
        }

        //return householdMapper.queryHoseMemberByHouseId(houseId);
        return householdMemberMapper.selectHomeMembers(houseId,0);
    }

    /**
     * 获取家庭成员信息
     *
     * @param houseHoldId
     * @param exHouseholder
     * @return
     */
    public JsonResult getHomeMembers(String houseHoldId, Integer exHouseholder) {
        CommonUtils.notNull(houseHoldId,"家庭户ID不能为空");
        List<HouseMember> result = householdMemberMapper.selectHomeMembers(houseHoldId,exHouseholder);
        if (CollectionUtils.isEmpty(result)) {
            return JsonResult.valueOfObject(Collections.emptyList());
        }
        for (HouseMember houseMember : result) {
            SecureUtils.sensitiveFieldDecrypt(houseMember);
        }

        return JsonResult.valueOfObject(result);
    }

    /**
     * 根据户主身份证号获取家庭对象
     *
     * @param houseHolderId
     * @return
     */
    public Household getHouseHoldByHouseholderIdCode(String houseHolderId) {
        if (StrUtil.isEmpty(houseHolderId)) {
            return null;
        }

        houseHolderId = SecureUtils.stringEncode(houseHolderId); //加密
        Map<String, Object> params = new HashMap<>();
        params.put("houseHolderId",houseHolderId);

        List<Household> houseHoldByHouseholders = householdMapper.getHouseHoldByHouseholder(params);
        if (CollectionUtils.isEmpty(houseHoldByHouseholders)) {
            return null;
        }

        return houseHoldByHouseholders.get(0);
    }

    /**
     * 根据户主姓名获取家庭信息
     *
     * @param name
     * @return
     */
    public JsonResult queryByHouseholder(String name,Integer pageSize,Integer pageNo) {
        if (StrUtil.isEmpty(name)) {
            return null;
        }

        PageHelper.startPage(pageNo,pageSize);
        Map<String, Object> params = new HashMap<>();
        params.put("houseHolderName",name);
        List<HouseholderVo> houseHoldByHouseholders = householdMapper.getHouseHoldByHouseholderName(name);

        return JsonResult.valueOfObject(new PageInfo<>(houseHoldByHouseholders));
    }
}

<template>
  <div class="el-button uploadBefore">
    <div class="btn_upload_coutinho" @click="btnClick">
      <slot>上传</slot>
    </div>
    <funi-dialog v-model="dialogVisible" :title="title" size="small">
      <div class="uploadBox">
        <p class="download-link" v-if="!onlyUpload">
          一、
        <div class="download-link download-link__text" @click="downloadTemplateUse(dc,dcParams,templateName)">
          <span>下载导入模板</span>
          <funi-icon icon="material-symbols:cloud-download-outline-rounded"></funi-icon>
        </div>
        </p>
        <p v-if="!onlyUpload">二、上传文件</p>
        <div class="uploadBox__func">
          <el-upload class="upload-demo" drag action="#" :accept="accept" :auto-upload="true"
                     :http-request="httpRequest"
                     v-loading="loading"
                     >
            <el-icon class="el-icon--upload">
              <upload-filled/>
            </el-icon>
            <div class="el-upload__text">
              <div class="upload__text">点击选择或拖动文件到此区域上传</div>
              <div class="tips">
                <div>1.请上传 {{ accept }} 文件</div>
                <div>2.请上传小于10M文件</div>
                <div style="color:red;">*请使用下载模板文件进行上传</div>
              </div>
            </div>
          </el-upload>

        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </funi-dialog>

  </div>
</template>
<script setup lang="jsx">
import {ref, onMounted, watch} from 'vue';
import {ElNotification} from 'element-plus';
import {UploadFilled} from '@element-plus/icons-vue';
import {downloadTemplateUse} from './uploadUse.js'
const loading = ref(false)
const emit = defineEmits(['updateCurd'])
const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  callbackFun: {
    type: Function,
    default: () => {
    }
  },
  dc: {
    type: String,
    default: ''
  },
  dcParams:{
    type: Object,
    default: () =>{}
  },
  title: {
    type: String,
    default: '导入',
  },
  templateName: {
    type: String,
    default: '模板',
  },
  onlyUpload: {
    type: Boolean,
    default: false
  },
  accept: {
    type: String,
    default: '.xlsx,.xls',
  }
});


let dialogVisible = ref(false);
const httpRequest = async options => {
  const formData = new FormData();
  formData.append('file', options.file);
  if (props.url) {
    loading.value = true
    try{
      let res = await $http.upload(props.url, formData)
    if (res && res.fileName) {
      ElNotification({
        title: '成功',
        message: `${res.fileName}${props.onlyUpload ? '上传' : '导入'}成功`,
        type: 'success'
      });
    } else {
      ElNotification({
        title: '成功',
        message: '导入成功',
        type: 'success'
      });
    }
    loading.value = false
    props.callbackFun(res)
    }catch(err){
      loading.value = false
    }
    dialogVisible.value = false;
    return Promise.resolve({})
  }
};

const btnClick = () => {
  // dom && dom.click();
  dialogVisible.value = true;
};
</script>
<style scoped>
@import url(./upload.css);
</style>

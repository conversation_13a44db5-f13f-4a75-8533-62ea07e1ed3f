/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-24 00:48:34
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-24 15:21:57
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCodeV2/hooks/config.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import OrgUser from './../dialog/user/org_user.vue';
import RoleUser from './../dialog/user/role_user.vue';
import UserOneself from './../dialog/user/oneself.vue';
import OrgTree from './../dialog/org/org.vue';
import OrgOneself from './../dialog/org/oneself.vue';

export const iconClass = {
    user: 'humbleicons:user',
    role: 'mdi:user-box-outline',
    org: 'fluent-mdl2:org'
}


export const getTabs = (config) => {
    let orgUser = {
        tag: OrgUser,
        name: 'orgUser',
        title: '部门选人员'
    }
    let roleUser = {
        tag: RoleUser,
        name: 'roleUser',
        title: '角色选人员'
    }
    let userOneself = {
        tag: UserOneself,
        name: 'oneself',
        title: '当前用户'
    }
    let orgTree = {
        tag: OrgTree,
        name: 'org',
        title: '部门'
    }
    let orgOneself = {
        tag: OrgOneself,
        name: 'oneself',
        title: '本部门'
    }
    let { type, allowedOrg, allowedRole, allowedSelf } = config

    if (type === 'user') {
        return [
            ...allowedOrg && allowedOrg.length ? [orgUser] : [],
            ...allowedRole && allowedRole.length ? [roleUser] : [],
            ...allowedSelf ? [userOneself] : []
        ]
    } else if (type === 'org') {
        return [
            ...allowedOrg && allowedOrg.length ? [orgTree] : [],
            ...allowedSelf ? [orgOneself] : []
        ]
    }

    return []



}
import SearchFormDateItem from './cell/searchFormDateItem.vue';
import SearchFormInputItem from './cell/searchFormInputItem.vue';
import SearchFormSelectItem from './cell/searchFormSelectItem.vue';
import SearchFormRegionItem from './cell/searchFormRegionItem.vue';
import SearchFormInputNumberItem from './cell/searchFormInputNumberItem.vue';
import SearchFormAutocompleteItem from './cell/searchFormAutocompleteItem.vue';
import SearchFormInputNumberRangeItem from './cell/searchFormInputNumberRangeItem.vue';

const cellMap = {
  DATE: () => <SearchFormDateItem />,
  TEXT: () => <SearchFormInputItem />,
  SELECT: () => <SearchFormSelectItem />,
  REGION: () => <SearchFormRegionItem />,
  NUMBER: () => <SearchFormInputNumberItem />,
  AUTOCOMPLETE: () => <SearchFormAutocompleteItem />,
  NUMBER_RANGE: () => <SearchFormInputNumberRangeItem />
};

/**
 * 内置渲染器
 */
const innerRenderKeys = ['DATE', 'TEXT', 'SELECT', 'REGION', 'NUMBER', 'NUMBER_RANGE', 'AUTOCOMPLETE'];

export const cellRender = {
  get(name) {
    return cellMap[name] || null;
  },

  add(name, render) {
    if (name && !innerRenderKeys.includes(name) && render) {
      cellMap[name] = render;
    }
    return cellRender;
  }
};

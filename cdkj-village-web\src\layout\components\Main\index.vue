<template>
  <main style="padding-top: 10px; padding: 12px; background-color: rgb(245, 247, 250); overflow: hidden">
    <el-scrollbar class="rounded-[4px]" height="100%" wrap-class="layout-content__wrap">
      <router-view>
        <template #default="{ Component, route }">
          <keep-alive ref="keepAlive">
            <component :is="Component" :key="componentKeyMap.get(route.fullPath)" />
          </keep-alive>
        </template>
      </router-view>
    </el-scrollbar>
  </main>
</template>

<script setup>
import { useSessionStorage } from '@vueuse/core';
import { ref, unref } from 'vue';
import { onBeforeRouteUpdate } from 'vue-router';
import { useKeepAliveCache } from './useKeepAliveCache';
import { useAppStore } from '@/stores/useAppStore';

const keepAlive = ref();
const appStore = useAppStore();
const componentKeyMap = useSessionStorage('component-key-map', new Map());

function removeCache(tabName) {
  const { pruneCache } = useKeepAliveCache(unref(keepAlive));
  const cacheKey = unref(componentKeyMap).get(tabName);
  pruneCache(cacheKey);
  componentKeyMap.value.delete(tabName);
}

onBeforeRouteUpdate((to, from) => {
  if (!unref(componentKeyMap).has(to.fullPath)) {
    componentKeyMap.value.set(to.fullPath, new Date().getTime());
  }
});

defineExpose({
  removeCache
});
</script>

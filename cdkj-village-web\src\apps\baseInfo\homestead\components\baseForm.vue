<template>
  <div>
    <funiGroupTitle title="宅基地信息"></funiGroupTitle>
    <funiForm v-bind="formConfig" ref="refForm" :col="2" />
    <ownerInfo
      ref="ownerInfoRef"
      :isDetail="isDetail"
      :personnelData="personnelData"
      @dataListCallBack="dataListCallBack"
    />
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { homesteadAdd, homesteadInfo, homesteadUpdate } from '@/apps/api/homestead.js';
import { useRoute } from 'vue-router';
import ownerInfo from './ownerInfo/index.vue';
import { ElNotification } from 'element-plus';

const props = defineProps({
  isDetail: {}
});
const { id } = useRoute().query;
if (id) {
  homesteadInfo({ uuid: id }).then(res => {
    refForm.value.setValues(res);
    personnelData.value = res.houseMembers.filter(x => x.householder == 1);
  });
}

const dicEnum = {
  landType: [
    {
      label: '宅基地',
      value: '宅基地'
    },
    {
      label: '耕地',
      value: '耕地'
    },
    {
      label: '水域',
      value: '水域'
    },
    {
      label: '其他',
      value: '其他'
    }
  ],
  floor: [
    {
      label: '1层',
      value: '1层'
    },
    {
      label: '2层',
      value: '2层'
    },
    {
      label: '3层',
      value: '3层'
    },
    {
      label: '3层以上',
      value: '3层以上'
    }
  ],
  structure: [
    {
      label: '土木/石木结构',
      value: '土木/石木结构'
    },
    {
      label: '砌体结构',
      value: '砌体结构'
    },
    {
      label: '混杂结构',
      value: '混杂结构'
    },
    {
      label: '钢结构',
      value: '钢结构'
    },
    {
      label: '钢筋混泥土结构',
      value: '钢筋混泥土结构'
    },
    {
      label: '底部框架-上部砌体结构',
      value: '底部框架-上部砌体结构'
    },
    {
      label: '木(竹)结构',
      value: '木(竹)结构'
    },
    {
      label: '窑洞',
      value: '窑洞'
    },
    {
      label: '其他',
      value: '其他'
    }
  ],
  usage: [
    {
      label: '在建',
      value: '在建'
    },
    {
      label: '在用',
      value: '在用'
    },
    {
      label: '废弃',
      value: '废弃'
    },
    {
      label: '回收',
      value: '回收'
    },
    {
      label: '其他',
      value: '其他'
    }
  ]
};

const refForm = ref();
const ownerInfoRef = ref();
const personnelData = ref([]);
const formConfig = reactive({
  schema: [
    {
      prop: 'code',
      label: '宅基地编号',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'area',
      label: '宅基地面积（㎡）',
      component: !props.isDetail ? 'funi-input-number' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'hasCert',
      label: '是否取证',
      component: !props.isDetail ? 'funi-select' : null,
      props: {
        options: [
          {
            label: '是',
            value: '是'
          },
          {
            label: '否',
            value: '否'
          }
        ]
      }
    },
    {
      prop: 'landType',
      label: '土地类型',
      component: !props.isDetail ? 'funi-select' : null,
      props: {
        options: dicEnum.landType
      }
    },
    {
      prop: 'certNo',
      label: '证书编号',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      hidden: ({ item, formModel }) => {
        return !formModel.hasCert;
      },
      props: { type: 'textarea', placeholder: '输入内容' }
    },
    {
      prop: 'noCertReason',
      label: '未取证原因',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      hidden: ({ item, formModel }) => {
        return formModel.hasCert;
      },
      props: { type: 'textarea', placeholder: '输入内容' }
    },
    {
      prop: 'east',
      label: '土地四至-东至',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'west',
      label: '地四至-西至',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'south',
      label: '土地四至-南至',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'north',
      label: '土地四至-北至',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'floor',
      label: '房屋楼层',
      component: !props.isDetail ? 'funi-select' : null,
      props: {
        options: dicEnum.floor
      }
    },
    {
      prop: 'structure',
      label: '房屋结构',
      component: !props.isDetail ? 'funi-select' : null,
      props: {
        options: dicEnum.structure
      }
    },
    {
      prop: 'usage',
      label: '使用情况',
      component: !props.isDetail ? 'funi-select' : null,
      props: {
        options: dicEnum.usage
      }
    },
    {
      prop: 'remark',
      label: '备注',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    }
  ],
  rules: !props.isDetail
    ? {
        code: [{ required: true, message: '请输入' }],
        area: [{ required: true, message: '请输入' }],
        landType: [{ required: true, message: '请选择' }],
        hasCert: [{ required: true, message: '请选择' }],
        noCertReason: [{ required: true, message: '请输入' }],
        certNo: [{ required: true, message: '请输入' }],
        east: [{ required: true, message: '请输入' }],
        west: [{ required: true, message: '请输入' }],
        south: [{ required: true, message: '请输入' }],
        north: [{ required: true, message: '请输入' }],
        floor: [{ required: true, message: '请选择' }],
        structure: [{ required: true, message: '请选择' }],
        usage: [{ required: true, message: '请选择' }]
      }
    : undefined
});

/**
 * 提交
 */
async function submit() {
  let res = await refForm.value.validate();
  if (res.isValid) {
    let ownerData = await ownerInfoRef.value.getData();
    if (!ownerData.length) {
      ElNotification({
        title: '请添加权属信息',
        type: 'warning'
      });
    } else {
      //编辑
      if (id) {
        await homesteadUpdate({ ...res.values, uuid: id, household: ownerData[0].householdId});
      }
      //新增
      else {
        await homesteadAdd({ ...res.values, household: ownerData[0].householdId});
      }
      return Promise.resolve();
    }
  }
  return Promise.reject();
}

defineExpose({
  submit
});
</script>

<style lang="scss" scoped></style>

import HyperlinkInfo from '@/apps/lfpra/common/components/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/lfpra/common/components/hyperlinkTable/index.vue';
import Upload from '../component/upload/upload.vue';
import TableHeaderItem from '../component/tableHeaderItem/index.vue'
import { apiUrlCommon } from '@/apps/lfpra/common/hooks/api.js';
import { apiUrl } from './api.js';
import { moreBtnRender } from '@/apps/lfpra/common/hooks/utils.jsx';
export const userTableColumns = ({
  renameFunc = () => {},
  associationFunc = () => {},
  unfollowFunc = () => {},
  delFunc = () => {},
  exportRowFunc = () => {},
  addRowFunc = () => {},
  folderRowFunc = () => {},
  importRowFunc = () => {}
}) => {
  return [
    {
      label: '文件名',
      prop: 'fileName',
      width: 200,
      render: ({ row, index }) => {
        return (
          <TableHeaderItem fileName={row.fileName}/>
        );
      },
      fixed: 'left'
    },
    {
      label: '创建者',
      prop: 'creatorName'
    },
    {
      label: '创建时间',
      prop: 'createTime'
    },
    {
      label: '文件大小',
      prop: 'fileSizeName'
    },
    {
      label: '关联户主姓名',
      prop: 'ownerName'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {
          RENAME: <Hyperlink row={row} index={index} func={renameFunc} text={'重命名'}></Hyperlink>,
          ASSOCIA: <Hyperlink row={row} index={index} func={associationFunc} text={'关联户主'}></Hyperlink>,
          DOW: <Hyperlink row={row} index={index} func={exportRowFunc} text={'下载'}></Hyperlink>,
          ADD: <Hyperlink row={row} index={index} func={addRowFunc} text={'新建文件夹'}></Hyperlink>,
          FOLDER: <Hyperlink row={row} index={index} func={folderRowFunc} text={'上传文件夹'}></Hyperlink>,
          FILE: <Hyperlink row={row} index={index} func={importRowFunc} text={'上传文件'}></Hyperlink>,
          UNFOLLOW: (
            <el-popconfirm
              title="确定取消关联？"
              width="220"
              onConfirm={() => {
                unfollowFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink text={'取消关联'}></Hyperlink>
              }}
            </el-popconfirm>
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前数据？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          )
        };

        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(row.buttonEnums, operationBtn)}
          </div>
        );
      }
    }
  ];
};

// 按钮
export const useBtnsConfig = ({
  addFunc = () => {},
  folderFunc = () => {},
  importFunc = () => {},
  exportFunc = () => {}
}) => {
  return [
    {
      component: () => (
        // <el-button v-auth="ERM_CONTRACTINCOME_ADD" onClick={addFunc} type="primary">
        //   新建
        // </el-button>
        <el-button onClick={addFunc} type="primary">
          新建文件夹
        </el-button>
      )
    },
    {
      component: () => (
        <el-button onClick={folderFunc} type="primary">
          上传文件夹
        </el-button>
      )
    },
    {
      component: () => (
        <Upload
          url={apiUrl.importSiteInfo}
          onlyUpload={false}
          callbackFun={importFunc}
          templateName="家庭承包信息模板"
          title="批量导入"
          type="primary"
        >
          上传文件
        </Upload>
      )
    },
    {
      component: () => (
        // <el-button v-auth="ERM_CONTRACT_COLLECTION_EXPORT" onClick={exportFun} type="primary">
        //   导出
        // </el-button>
        <el-button onClick={exportFunc} type="primary">
          下载
        </el-button>
      )
    }
  ];
};

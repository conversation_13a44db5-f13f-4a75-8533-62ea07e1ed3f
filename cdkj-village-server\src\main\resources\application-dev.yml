#服务端口号
server:
  port: 8080

#日志级别
logging:
  level:
    root: error

spring:
  #数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ******************************************
    username: postgres
    password: fight@postgres@2025
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000

#mybatis-plus 配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#系统业务参数
business:
  file-store-path: E:\\FileStore\\
  env: dev
  licence: jRLykPBATcsSx+zxvMz9kVw/EaQBvqrAEbMEtqe5ZpfGuo7GEGX4Y7Kz7r8NeZdGVBb5l2eTMjDYqRaA73QQC3W8xRdwgkmMWsVrobVEclfGfpyrEPutwhePPbqH8crWu8duAibFOFNOx/4jywoGMHJIZMeazKfjWq34yVGzsG8=
  documents_path: E:\\Studio\\tomcat-9\\webapps

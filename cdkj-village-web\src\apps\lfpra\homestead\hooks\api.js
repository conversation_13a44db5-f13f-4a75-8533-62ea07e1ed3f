export const apiUrl = {
  queryList: '/lfpra/siteInfoList/querySiteInfoList',
  delete: '/lfpra/siteInfo/delete',
  queryExport:'/lfpra/siteInfoList/querySiteInfoListExport',
  importSiteInfo:'/lfpra/siteInfo/importSiteInfo',
  info:'/lfpra/siteInfo/info',
  new:'/lfpra/siteInfo/new',
  getOtherSiteinfoPatternIds:'/lfpra/siteInfo/getOtherSiteinfoPatternIds'//获取其他宅基地使用的图斑ids
};

// 获取列表
export const queryListHttp = params => {
  return $http.post(apiUrl.queryList, params);
};

// 删除
export const deleteHttp = params => {
  return $http.fetch(apiUrl.delete, params);
};
// 查询详情
export const infoHttp = params => {
  return $http.post(`${apiUrl.info}?siteInfoId=${params.siteInfoId}`);
};
//新增/编辑保存
export const newHttp = params => {
  return $http.post(apiUrl.new, params);
};
//获取其他宅基地使用的图斑ids
export const getOtherSiteinfoPatternIdsHttp = params => {
  return $http.fetch(apiUrl.getOtherSiteinfoPatternIds, params);
};

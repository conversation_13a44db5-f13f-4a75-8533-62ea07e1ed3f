<template>
  <el-autocomplete
    ref="autocompleteRef"
    v-model="state"
    clearable
    fit-input-width
    style="width: 100%"
    :placeholder="attribute.placeholder || '请输入'"
    value-key="key"
    :debounce="400"
    :fetch-suggestions="suggestions"
    :popper-class="popperClass"
    @select="handleSelect"
    @clear="handleClear"
    @blur="handleBlur"
    @input="handleInput"
    @focus="handleFocus"
  >
    <template #default="{ item }">
      <el-tooltip placement="left" :disabled="tooltipDisabledMap[item.key]">
        <template #content>
          <div class="max-w-[240px]">{{ item.key }}</div>
        </template>
        <template #default>
          <span :ref="el => handleToolContentRef(el, item)">{{ item.key }}</span>
        </template>
      </el-tooltip>
    </template>
  </el-autocomplete>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch, inject } from 'vue';

const props = defineProps({
  attribute: Object,
  column: String,
  modelValue: String
});
const emit = defineEmits(['update:modelValue']);
const { getFilters } = inject($utils.symbols.searchForm);

const autocompleteRef = ref();

const state = ref();
const selectedItem = ref({});
const pageNo = ref(1);
const suggestions = ref([]);
const loadMore = ref(true);
const tooltipDisabledMap = reactive({});

const popperClass = 'funi-search-form__autocomplete-' + $utils.guid();

const querySearchAsync = async queryString => {
  if (!props.attribute?.url || !loadMore) return;

  const keyword = props.attribute?.keyword || 'queryKey';
  const requestParams = {
    pageNo: pageNo.value,
    pageSize: 10,
    queryField: props.column,
    [keyword]: queryString,
    ...props.attribute?.params
  };

  if (props.attribute?.dynamic) {
    Object.assign(requestParams, {
      filters: getFilters().filter(item => item.column !== props.column)
    });
  }

  const res = await $http.post(props.attribute?.url, requestParams);
  if (res.total === 0) {
    suggestions.value = [{ key: '暂无数据', value: -999 }];
  } else {
    suggestions.value = pageNo.value === 1 ? res.list : [...suggestions.value, ...res.list];
  }
  autocompleteRef.value.suggestions = suggestions.value;
  loadMore.value = suggestions.value.length < res.total;
  suggestionWrapDom.value.querySelector('.empty-placeholder').style.display = res.total === 0 ? '' : 'none';
};

const suggestionWrapDom = computed(() => {
  const popper = document.querySelector(`.${popperClass}`);
  const suggestionDom = popper?.querySelector(`.${popperClass}` + ' > .el-autocomplete-suggestion');
  const scrollbarDom = suggestionDom?.querySelector('.el-autocomplete-suggestion > .el-scrollbar');
  const wrapDom = scrollbarDom?.querySelector('.el-scrollbar > .el-autocomplete-suggestion__wrap');
  return wrapDom;
});

watch(
  () => props.modelValue,
  val => {
    if (!val) {
      handleClear();
    }
  }
);

onMounted(() => {
  suggestionWrapDom.value?.addEventListener(
    'scroll',
    e => {
      const { scrollTop, clientHeight, scrollHeight } = e.target || {};
      if (loadMore.value && scrollTop >= 60 && scrollTop + clientHeight >= scrollHeight) {
        pageNo.value++;
        querySearchAsync(state.value);
      }
    },
    false
  );

  const emptyPlaceholder = document.createElement('div');
  emptyPlaceholder.className = 'empty-placeholder';
  emptyPlaceholder.style.position = 'absolute';
  emptyPlaceholder.style.inset = 0;
  emptyPlaceholder.style.display = 'none';
  suggestionWrapDom.value.style.position = 'relative';
  suggestionWrapDom.value?.appendChild(emptyPlaceholder);
});

onUnmounted(() => {
  suggestionWrapDom.value?.removeEventListener('scroll', () => {}, false);
});

const handleFocus = () => {
  loadMore.value = true;
  !!state.value && querySearchAsync(state.value);
};

const handleInput = value => {
  pageNo.value = 1;
  loadMore.value = true;
  !value ? handleClear() : querySearchAsync(value);
};

const handleSelect = item => {
  pageNo.value = 1;
  selectedItem.value = item;
  loadMore.value = true;
  emit('update:modelValue', item.value);
};

const isTrue = value => value === 1 || value === '1' || value === true;

const handleBlur = () => {
  pageNo.value = 1;
  suggestions.value = [];
  loadMore.value = true;
  if (isTrue(props.attribute?.disableUnmatch)) {
    state.value = selectedItem.value.key;
  } else if (state.value !== selectedItem.value.key) {
    selectedItem.value = {};
    emit('update:modelValue', state.value);
  }
};

const handleClear = () => {
  state.value = '';
  pageNo.value = 1;
  suggestions.value = [];
  selectedItem.value = {};
  loadMore.value = true;
  emit('update:modelValue', '');
};

const isOverflow = el => {
  const range = document.createRange();
  range.setStart(el, 0);
  range.setEnd(el, el.childNodes.length);
  const rangeWidth = range.getBoundingClientRect().width;

  const parentStyle = window.getComputedStyle(el.parentElement, null);
  const paddingLeft = Number.parseInt(parentStyle.paddingLeft, 10) || 0;
  const paddingRight = Number.parseInt(parentStyle.paddingRight, 10) || 0;
  const horizontalPadding = paddingLeft + paddingRight;

  const { offsetWidth, scrollWidth } = el.parentElement;
  return rangeWidth + 2 > offsetWidth || scrollWidth > offsetWidth + horizontalPadding;
};

const handleToolContentRef = (el, item) => {
  if (!!el) {
    tooltipDisabledMap[item.key] = !isOverflow(el);
  }
};
</script>

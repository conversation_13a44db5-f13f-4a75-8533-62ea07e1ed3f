package cn.fight.village.domain.homestead.request;

import cn.fight.village.domain.common.entity.BaseQuery;

/**
 * 宅基地列表查询对象
 *
 */
public class HomesteadQuery extends BaseQuery {
    private String householder;

    private String usage;

    private Double areaMin;

    private Double areaMax;

    private String hasCert;

    private String code;

    private String idCode;

    public String getHasCert() {
        return hasCert;
    }

    public void setHasCert(String hasCert) {
        this.hasCert = hasCert;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getHouseholder() {
        return householder;
    }

    public void setHouseholder(String householder) {
        this.householder = householder;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public Double getAreaMin() {
        return areaMin;
    }

    public void setAreaMin(Double areaMin) {
        this.areaMin = areaMin;
    }

    public Double getAreaMax() {
        return areaMax;
    }

    public void setAreaMax(Double areaMax) {
        this.areaMax = areaMax;
    }
}

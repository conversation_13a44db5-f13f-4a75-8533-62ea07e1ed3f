package cn.fight.village.domain.user.api;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.domain.user.request.UserQuery;
import cn.fight.village.domain.user.request.UserRequest;
import cn.fight.village.domain.user.service.UserService;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 系统用户接口层
 */
@RestController
@RequestMapping("user")
public class UserApi {
    @Resource
    private UserService userService;

    /**
     * 添加系统用户
     * @param userRequest
     * @return
     */
    @PostMapping("add")
    public JsonResult addUser(@UserInfo User user, @RequestBody UserRequest userRequest) {
        return userService.addUser(user,userRequest);
    }

    /**
     * 删除用户
     * @param user
     * @param paramMap
     * @return
     */
    @PostMapping("remove")
    public JsonResult removeUser(@UserInfo User user, @RequestBody Map<String,Object> paramMap) {
        String userId = (String)paramMap.get("userId");
        if (StrUtil.isBlank(userId))
            throw new BusinessException("待删除的用户不能为空");

        return userService.removeUser(user,userId);
    }

    /**
     * 用户列表
     * @param userQuery
     * @return
     */
    @PostMapping("list")
    public JsonResult getUserList(@RequestBody UserQuery userQuery) {
        return userService.getUserList(userQuery);
    }

    /**
     * 用户信息
     * @param userId
     * @return
     */
    @GetMapping("info")
    public JsonResult getUserInfo(String userId) {
        if (StrUtil.isBlank(userId))
            throw new BusinessException("查询条件不能为空");
        return userService.getUserInfo(userId);
    }

    /**
     * 修改用户
     * @param user
     * @param userRequest
     * @return
     */
    @PostMapping("update")
    public JsonResult updateUser(@UserInfo User user,@RequestBody UserRequest userRequest) {
        return userService.updateUser(user,userRequest);
    }
}

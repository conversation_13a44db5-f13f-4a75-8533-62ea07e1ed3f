<!-- 地图斑 -->
<template>
  <div class="owner_info">
    <div class="owner_info_add" @click="handleShow" v-if="!patternId">
      <span>关联图斑</span>
    </div>
    <div style="margin-top:25px;" v-else>
      <el-button v-if="props.isEdit" type="primary" @click="handleShow" style="margin-bottom: 10px;">重新关联图斑</el-button>
      <polygonMap ref="polygonMapModel" :layerType="props.layerType" :isEdit="false" :id="props.id || patternId" idNum="1" height="300" />
    </div>
  </div>
  <mapSpotDialog  ref="chooseModal" @exportObject="setCollection"></mapSpotDialog>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref,watchEffect } from 'vue';
import mapSpotDialog from '@/apps/lfpra/common/components/mapSpot/mapSpotDialog.vue';
import polygonMap from '@/apps/lfpra/common/components/polygonMap/index.vue'
const props = defineProps({
  layerType:{},
  isEdit:{
    type:Boolean,
    default:true
  },
  id:{
    type:Number || String,
    default:''
  }
})
const polygonMapModel = ref(null)
const emit = defineEmits(['mapExportData'])
const chooseModal = ref(null)
const tableData = ref([]); // 表格数据
const patternId = ref('')
watchEffect(()=>{
  // 编辑/查看进入 回显权属信息列表
  if(props.id){
    patternId.value = props.id
  }
})
// 打开图斑
const handleShow =()=>{
  chooseModal.value.show(props.layerType,props.isEdit,props.id|| patternId.value)
}
// 选择 图斑后的数据
const setCollection =async e=>{
  // tableData.value = e
  patternId.value = e[0].properties.id
  nextTick(()=>{
    polygonMapModel.value.getBusinessEntity(patternId.value)
  })
  emit('mapExportData',patternId.value)
}

</script>
<style scoped lang="less">
.owner_info{
  width: 100%;
  .owner_info_add{
    width: 100%;
    padding: 30px 0;
    display: flex;
    justify-content: center;
    border: 1px dashed var(--el-color-primary);
    border-radius: 8px;
    cursor: pointer;
    span{
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }
}
</style>

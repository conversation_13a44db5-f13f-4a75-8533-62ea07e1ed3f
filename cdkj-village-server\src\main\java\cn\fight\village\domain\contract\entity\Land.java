package cn.fight.village.domain.contract.entity;

import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 合同土地信息-承包
 *
 */
@TableName("public.rlams_land")
public class Land extends BaseEntity {
    //合同ID
    private String contractId;

    //类型
    private String type;

    //土地编号
    private String landNo;

    //土地类型
    private String landType;

    //是否承包
    private String contract;

    //面积
    private Double area;

    //种植情况
    private String farming;

    //是否流转
    private String transfer;

    //备注
    private String remark;

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLandNo() {
        return landNo;
    }

    public void setLandNo(String landNo) {
        this.landNo = landNo;
    }

    public String getLandType() {
        return landType;
    }

    public void setLandType(String landType) {
        this.landType = landType;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getFarming() {
        return farming;
    }

    public void setFarming(String farming) {
        this.farming = farming;
    }

    public String getTransfer() {
        return transfer;
    }

    public void setTransfer(String transfer) {
        this.transfer = transfer;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

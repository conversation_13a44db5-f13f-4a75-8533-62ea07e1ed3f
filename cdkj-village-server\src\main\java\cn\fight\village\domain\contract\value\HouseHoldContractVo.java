package cn.fight.village.domain.contract.value;

import cn.fight.village.domain.common.entity.BaseValue;

/**
 * 家庭关联承包地视图对象
 *
 */
public class HouseHoldContractVo extends BaseValue {
    private String type;

    //土地编号
    private String landNo;

    //土地类型
    private String landType;

    //是否承包
    private String contract;

    //面积
    private Double area;

    //种植情况
    private String farming;

    //是否流转
    private String transfer;

    private String  contractCretNo;

    public String getContractCretNo() {
        return contractCretNo;
    }

    public void setContractCretNo(String contractCretNo) {
        this.contractCretNo = contractCretNo;
    }

    public String getLandNo() {
        return landNo;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setLandNo(String landNo) {
        this.landNo = landNo;
    }

    public String getLandType() {
        return landType;
    }

    public void setLandType(String landType) {
        this.landType = landType;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getFarming() {
        return farming;
    }

    public void setFarming(String farming) {
        this.farming = farming;
    }

    public String getTransfer() {
        return transfer;
    }

    public void setTransfer(String transfer) {
        this.transfer = transfer;
    }
}

.uploadBefore {
    background-color: var(--el-color-primary);
}

.btn_upload_coutinho {
    background-color: transparent;
    border: 1px solid transparent;
    font-size: inherit;
    color: #fff;
    width: 100%;
    height: 34px;
    display: inline-block;
    line-height: 34px;
}

#file_coutinho {
    display: none;
}

.uploadBox {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: flex-start;
}

.uploadBox>p {
    font-size: 16px;
    font-weight: bolder;
}

.uploadBox__func {
    width: 100%;
    padding: 0 2rem;
    box-sizing: border-box;
}

.tips {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: flex-start;
    margin-top: 40px;
    box-sizing: border-box;
    padding: 0 10px;
}

.tips>div {
    margin-bottom: 10px;
    color: #a7a4a4;
    word-break: break-all;
    white-space: normal;
}

.upload__text {
    font-size: 17px;
}

:deep(.el-upload-dragger) {
    padding: 20px;
    background-color: #fafafa;
}

.download-link {
    display: flex;
    justify-content: start;
    align-items: center;

}

.download-link__text {
    color: var(--el-color-primary);
    cursor: pointer;
}

.download-link__text>span {
    font-size: 14px;
    display: inline-block;
    margin-right: 10px;
}

.download-link__text:hover {
    color: var(--el-color-primary-light-5);
}
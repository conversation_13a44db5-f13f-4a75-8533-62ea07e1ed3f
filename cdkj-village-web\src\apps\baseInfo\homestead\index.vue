<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="cardTab" @headBtnClick="headBtnClick" />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { homesteadList, homesteadRemove } from '@/apps/api/homestead.js';

const router = useRouter();
const listPage = ref();
/**
 * 列表配置
 */
const cardTab = reactive([
  {
    curdOption: {
      reloadOnActive: true,
      lodaData: (pages, parmas) => {
        return homesteadList({ ...pages, ...parmas });
      },
      searchConfig: {
        schema: [
          {
            prop: 'code',
            label: '宅基地编号',
            component: 'el-input'
          },
          {
            prop: 'householder',
            label: '户主姓名',
            component: 'el-input'
          },
          {
            prop: 'idCode',
            label: '户主身份证号码',
            component: 'el-input'
          },
          {
            prop: 'usage',
            label: '用途',
            component: 'el-input'
          },
          {
            prop: 'areaMin',
            label: '面积范围小',
            component: 'el-input'
          },
          {
            prop: 'areaMax',
            label: '面积范围大',
            component: 'el-input'
          },
          {
            prop: 'hasCert',
            label: '是否取证',
            component: 'funi-select',
            props: {
              options: [
                {
                  label: '是',
                  value: '是'
                },
                {
                  label: '否',
                  value: '否'
                }
              ]
            }

          }
        ]
      },
      btns: [{ key: 'add', label: '新增' }],
      columns: [
        {
          label: '宅基地编号',
          prop: 'code',
          render: ({ row, index }) => {
            return (
              <el-button type="primary" link onClick={() => detail(row)}>
                {row.code}
              </el-button>
            );
          }
        },
        {
          label: '宅基地面积（㎡）',
          prop: 'area'
        },
        { label: '是否取证', prop: 'hasCert' },
        { label: '使用情况', prop: 'usage' },
        { label: '使用人（户主）名称', prop: 'name' },
        { label: '使用人（户主）证件类型', prop: 'idType' },
        { label: '使用人（户主）证件号码', prop: 'idCode' },
        {
          label: '使用人（家庭）地址',
          prop: 'location'
        },
        {
          label: '操作',
          prop: 'opt',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            return (
              <div>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    edit(row);
                  }}
                >
                  编辑
                </el-button>
                <el-popconfirm
                  title="确认删除该条数据吗？"
                  onConfirm={() => {
                    del(row);
                  }}
                  v-slots={{
                    reference: () => (
                      <el-button type="primary" link>
                        删除
                      </el-button>
                    )
                  }}
                ></el-popconfirm>
              </div>
            );
          }
        }
      ]
    }
  }
]);

/**
 * 编辑
 * @param row 数据行
 */
function edit(row) {
  router.push({
    name: 'HomesteadAdd',
    query: {
      id: row.uuid,
      title: '宅基地信息编辑',
      bizName: '编辑',
      tab: `宅基地信息-${row.code}-编辑`
    }
  });
}
/**
 * 删除
 * @param row 数据行
 */
function del(row) {
  homesteadRemove({ uuid: row.uuid }).then(res => {
    listPage.value.reload();
  });
}
/**
 * 详情
 * @param row 数据行
 */
function detail(row) {
  router.push({
    name: 'HomesteadDetail',
    query: {
      id: row.uuid,
      title: '宅基地信息详情',
      bizName: '详情',
      tab: `宅基地信息-${row.code}-详情`
    }
  });
}
/**
 * 按钮点击事件
 */
function headBtnClick(res) {
  switch (res) {
    case 'add':
      router.push({
        name: 'HomesteadAdd',
        query: {
          title: '宅基地信息新增',
          bizName: '新建',
          tab: '宅基地信息-新增'
        }
      });
      break;
    default:
      break;
  }
}
</script>

<style lang="scss" scoped></style>

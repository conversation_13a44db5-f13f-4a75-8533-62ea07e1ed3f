<template>
  <el-image fit="scale-down" v-bind="$attrs" loading="lazy">
    <template #error>
      <slot name="error">
        <div class="image-slot">
          <el-icon><icon-picture /></el-icon>
        </div>
      </slot>
    </template>
    <template #placeholder>
      <slot name="placeholder">
        <div class="image-slot">
          <el-icon><icon-picture /></el-icon>
        </div>
      </slot>
    </template>
  </el-image>
</template>
<script setup>
import { Picture as IconPicture } from '@element-plus/icons-vue';
</script>
<style scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}
</style>

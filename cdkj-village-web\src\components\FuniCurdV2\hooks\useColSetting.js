import { ref, watchEffect } from 'vue';

export const useColSetting = (columns, s1ettings) => {
  const { remoteColumns } = s1ettings;
  const dynamicColumns = ref([]);
  const dynamicSettings = ref([]);

  watchEffect(() => {
    dynamicSettings.value = columns?.map(column => remoteColumns[column.prop] || { prop: column.prop }) ?? [];
  });

  watchEffect(() => {
    dynamicColumns.value = columns?.filter(column => !dynamicSettings[column.prop]?.hidden) ?? [];
  });

  const toggle = col => {
    const index = dynamicSettings.findIndex(i => i.prop === col);
    if (index > -1) {
      const setting = dynamicSettings[index];
      setting.hidden = !setting.hidden;
      dynamicSettings.value.splice(index, 1, setting);
    }
  };

  const sort = (col, targetIndex) => {
    const index = dynamicSettings.findIndex(i => i.prop === col);
    if (index > -1) {
      const setting = dynamicSettings[index];
      dynamicSettings.value.splice(index, 1);
      dynamicSettings.value.splice(targetIndex, 0, setting);
    }
  };

  const reset = () => (dynamicColumns.value = cols);
  return {
    sort,
    reset,
    toggle,
    dynamicColumns,
    dynamicSettings
  };
};

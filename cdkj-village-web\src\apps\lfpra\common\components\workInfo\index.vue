<!-- 工作信息 -->
<template>
  <div >
      <funi-curd-v2
        :isShowSearch="false"
        :columns="conColumns"
        :lodaData="lodaData"
        :loading="loading"
        :stripe="false"
        :pagination="pagination"
        border
      />
  </div>

</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref,onMounted } from 'vue';
import { getOperateLogHttp } from '@/apps/lfpra/common/hooks/api.js';
import { isArray } from 'xe-utils';
// 接收数据
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
});

const loading = ref(false)
const pagination = ref(false)
const conColumns = computed(() => {
  return [
    {
      label: '工作节点',
      prop: 'operateDescribe'
    },
    {
      label: '处理时间',
      prop: 'createTime'
    },
    {
      label: '处理人员',
      prop: 'operatorName'
    },
    {
      label: '处理结果',
      prop: 'operateContent'
    },
    // {
    //   label: '处理意见',
    //   prop: 'deptName'
    // }
  ];
});
//获取列表数据
const lodaData = async (page, params) => {
   loading.value = true;
  const resData = await getOperateLogHttp({
    ...page,
    principalSn:props.id
  }).finally(() => {
    loading.value = false;
  });
  console.log(resData,'resData');
  pagination.value = resData.list.length > 0
  return resData;
};
onMounted(()=>{
  // tableData.value = [{projectSn:'111'}]

})
// const getData = ()=>{

// }
</script>


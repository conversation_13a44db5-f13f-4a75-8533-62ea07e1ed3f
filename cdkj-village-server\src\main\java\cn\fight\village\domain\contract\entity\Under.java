package cn.fight.village.domain.contract.entity;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 承包方信息
 *
 */
@TableName("public.rlams_under")
public class Under extends BaseEntity {
    //合同ID
    private String  contractId;
    //承包方ID
    private String  underId;
    //承包方编号
    private String  underNo;
    //名称
    private String  upper;
    //是否村民
    private String  villager;
    //负责人姓名
    private String  underName;
    //负责人证件类型
    private String  underIdType;
    //证件号
    @Sensitive
    private String  underIdNo;
    //地址
    private String  underLocation;
    //联系电话
    @Sensitive
    private String  underPhone;
    //承包类型 - 方式
    private String  contractType;
    //承包经营产权证书号
    private String  contractCretNo;
    //经营承包权取得方式
    private String  rightOrg;
    //用途
    private String  usage;
    //流转金额
    private BigDecimal price;
    //确权面积
    private Double  rightArea;
    //备注
    private String  remark;

    //承包方共有人信息
    @TableField(exist = false)
    private List<UnderMembers> underMembersList = new ArrayList<>();

    public List<UnderMembers> getUnderMembersList() {
        return underMembersList;
    }

    public void setUnderMembersList(List<UnderMembers> underMembersList) {
        this.underMembersList = underMembersList;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getUnderId() {
        return underId;
    }

    public void setUnderId(String underId) {
        this.underId = underId;
    }

    public String getUnderNo() {
        return underNo;
    }

    public void setUnderNo(String underNo) {
        this.underNo = underNo;
    }

    public String getUpper() {
        return upper;
    }

    public void setUpper(String upper) {
        this.upper = upper;
    }

    public String getVillager() {
        return villager;
    }

    public void setVillager(String villager) {
        this.villager = villager;
    }

    public String getUnderName() {
        return underName;
    }

    public void setUnderName(String underName) {
        this.underName = underName;
    }

    public String getUnderIdType() {
        return underIdType;
    }

    public void setUnderIdType(String underIdType) {
        this.underIdType = underIdType;
    }

    public String getUnderIdNo() {
        return underIdNo;
    }

    public void setUnderIdNo(String underIdNo) {
        this.underIdNo = underIdNo;
    }

    public String getUnderLocation() {
        return underLocation;
    }

    public void setUnderLocation(String underLocation) {
        this.underLocation = underLocation;
    }

    public String getUnderPhone() {
        return underPhone;
    }

    public void setUnderPhone(String underPhone) {
        this.underPhone = underPhone;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractCretNo() {
        return contractCretNo;
    }

    public void setContractCretNo(String contractCretNo) {
        this.contractCretNo = contractCretNo;
    }

    public String getRightOrg() {
        return rightOrg;
    }

    public void setRightOrg(String rightOrg) {
        this.rightOrg = rightOrg;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Double getRightArea() {
        return rightArea;
    }

    public void setRightArea(Double rightArea) {
        this.rightArea = rightArea;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="cardTab" @headBtnClick="headBtnClick" />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { householdList, householdRemove } from '@/apps/api/household.js';

const router = useRouter();
const listPage = ref();
/**
 * 列表配置
 */
const cardTab = reactive([
  {
    curdOption: {
      reloadOnActive: true,
      lodaData: (pages, parmas) => {
        return householdList({ ...pages, ...parmas });
      },
      searchConfig: {
        schema: [
          {
            prop: 'name',
            label: '姓名',
            component: 'el-input'
          },
          {
            prop: 'householdCode',
            label: '家庭编号',
            component: 'el-input'
          },
          {
            prop: 'poor',
            label: '是否贫困户',
            component: 'funi-select',
            props: {
              options: [
                {
                  label: '是',
                  value: '是'
                },
                {
                  label: '否',
                  value: '否'
                }
              ]
            }
          },
          {
            prop: 'lower',
            label: '是否低保户',
            component: 'funi-select',
            props: {
              options: [
                {
                  label: '是',
                  value: '是'
                },
                {
                  label: '否',
                  value: '否'
                }
              ]
            }
          },
          {
            prop: 'idCode',
            label: '证件号',
            component: 'el-input'
          }
        ]
      },
      btns: [{ key: 'add', label: '新增' }],
      columns: [
        {
          label: '户编号',
          prop: 'householdCode',
          render: ({ row, index }) => {
            return (
              <el-button type="primary" link onClick={() => detail(row)}>
                {row.householdCode}
              </el-button>
            );
          }
        },
        {
          label: '户主姓名',
          prop: 'name'
        },
        { label: '户主性别', prop: 'gender' },
        { label: '户主证件类型', prop: 'idType' },
        { label: '户主证件号码', prop: 'idCode' },
        { label: '户主联系方式', prop: 'phone' },
        { label: '家庭住址', prop: 'location' },
        {
          label: '是否贫困户',
          prop: 'poor'
        },
        {
          label: '是否低保户',
          prop: 'lower'
        },
        // { label: '核查时间', prop: 'createTime' },
        {
          label: '操作',
          prop: 'opt',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            return (
              <div>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    edit(row);
                  }}
                >
                  编辑
                </el-button>
                <el-popconfirm
                  title="确认删除该条数据吗？"
                  onConfirm={() => {
                    del(row);
                  }}
                  v-slots={{
                    reference: () => (
                      <el-button type="primary" link>
                        删除
                      </el-button>
                    )
                  }}
                ></el-popconfirm>
              </div>
            );
          }
        }
      ]
    }
  }
]);

/**
 * 编辑
 * @param row 数据行
 */
function edit(row) {
  router.push({
    name: 'HouseholdAdd',
    query: {
      id: row.uuid,
      title: '人员与户信息编辑',
      bizName: '编辑',
      tab: `人员与户信息-${row.householdCode}-编辑`
    }
  });
}
/**
 * 删除
 * @param row 数据行
 */
function del(row) {
  householdRemove({ uuid: row.uuid }).then(res => {
    listPage.value.reload();
  });
}
/**
 * 详情
 * @param row 数据行
 */
function detail(row) {
  router.push({
    name: 'HouseholdDetail',
    query: {
      id: row.uuid,
      title: '人员与户信息详情',
      bizName: '详情',
      tab: `人员与户信息-${row.householdCode}-详情`
    }
  });
}
/**
 * 按钮点击事件
 */
function headBtnClick(res) {
  switch (res) {
    case 'add':
      router.push({
        name: 'HouseholdAdd',
        query: {
          title: '人员与户信息新增',
          bizName: '新建',
          tab: '人员与户信息-新增'
        }
      });
      break;
    default:
      break;
  }
}
</script>

<style lang="scss" scoped></style>

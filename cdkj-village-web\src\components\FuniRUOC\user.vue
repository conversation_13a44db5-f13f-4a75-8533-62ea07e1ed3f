<template>
  <div
    class="userOrgList"
    :style="{
      '--mode-border-radius': mode === 'multiple' ? 'var(--el-checkbox-border-radius)' : '50%'
    }"
  >
    <div class="orglist" v-loading="loadingOrg">
      <el-tree
        :data="orgList"
        :props="defaultProps"
        node-key="id"
        :expand-on-click-node="false"
        check-on-click-node
        @node-click="nodeClick"
        highlight-current
      >
      </el-tree>
    </div>
    <div class="userList" v-loading="loadingUser">
      <div style="padding: 0 5px">
        <el-input
          v-model="userName"
          :validateEvent="false"
          clearable
          @input="search"
          placeholder="请搜索人员"
          :suffix-icon="Search"
        ></el-input>
      </div>
      <div
        class="userListS"
        v-if="userList.length"
        v-infinite-scroll="load"
        :infinite-scroll-immediate="false"
        :infinite-scroll-distance="40"
      >
        <el-checkbox-group :validateEvent="false" v-model="userSelectId" @change="checkedChange">
          <template v-for="(item, index) in userList" :key="item[userProps.id]">
            <el-checkbox :validateEvent="false" :id="item[userProps.id]" :label="item[userProps.id]">
              <div>
                <span>{{ item[userProps.name] }}</span>
              </div>
            </el-checkbox>
          </template>
        </el-checkbox-group>
      </div>
      <div class="nodata" v-if="!userList?.length">暂无数据</div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { Search } from '@element-plus/icons-vue';
const props = defineProps({
  orgRequest: {
    type: Object,
    default: () => {
      return {
        api: '/csccs/orgList/orgTree',
        method: 'fetch',
        param: {}
      };
    }
  },
  userRequest: {
    type: Object,
    default: () => {
      return {
        api: '/csccs/orgList/findOrgAllPersonnel',
        method: 'post',
        param: {}
      };
    }
  },
  orgDefaultProps: {
    type: Object,
    default: () => {
      return {
        id: 'id',
        name: 'name',
        children: 'children'
      };
    }
  },
  userDefaultProps: {
    type: Object,
    default: () => {
      return {
        id: 'id',
        name: 'name'
      };
    }
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'multiple'
  }
});
const orgProps = computed(() => {
  return Object.assign(
    {
      id: 'id',
      name: 'name',
      children: 'children'
    },
    props.orgDefaultProps
  );
});
const userProps = computed(() => {
  return Object.assign(
    {
      id: 'id',
      name: 'name'
    },
    props.userDefaultProps
  );
});
const defaultProps = computed(() => {
  return {
    children: orgProps.value.children,
    label: orgProps.value.name
  };
});

const orgList = ref([]);
const loadingOrg = ref(false);
const loadingUser = ref(false);
const userList = ref([]);
const allUserList = ref([]);
const userName = ref('');
const searchName = ref('');
let nowOrg = void 0;
let pageNo = 1;
let pageSize = 20;
let total = 0;
let timer = null;
let controller = void 0;
const userSelectId = ref([]);
const emits = defineEmits(['update:modelValue', 'updateUserList']);

onMounted(() => {
  getOrgList();
  resetSearch();
  getUserList();
});

const getOrgList = async () => {
  loadingOrg.value = true;
  let { list } = await $http[props.orgRequest.method](props.orgRequest.api, {
    ...props.orgRequest.param
  }).finally(() => {
    loadingOrg.value = false;
  });
  list = list.map(item => {
    return {
      ...item,
      checked: false
    };
  });
  orgList.value = list;
};

const load = () => {
  if (Math.ceil(total / 20) <= pageNo) {
    return false;
  }
  pageNo++;
  getUserList();
};

const search = async e => {
  userName.value = e;
  await nextTick();
  clearTimeout(timer);
  timer = setTimeout(() => {
    clearTimeout(timer);
    searchName.value = userName.value;
    resetSearch();
    getUserList();
  }, 600);
};

const nodeClick = async e => {
  nowOrg = e;
  searchName.value = '';
  userName.value = '';
  resetSearch();
  getUserList();
};

const resetSearch = () => {
  pageNo = 1;
  total = 0;
  userList.value = [];
};
const getUserList = async () => {
  loadingUser.value = true;
  if (pageNo === 1) {
    controller && controller.abort();
  }
  controller = new AbortController();
  let data = await $http[props.userRequest.method](
    props.userRequest.api,
    {
      orgId: nowOrg?.id,
      isContainChild: true,
      nickname: searchName.value || void 0,
      pageNo,
      pageSize,
      isAllAccount: !nowOrg?.id ? true : void 0,
      ...props.userRequest.param
    },
    {
      signal: controller.signal
    }
  ).finally(() => {
    controller = void 0;
    loadingUser.value = false;
  });
  if (!searchName.value) total = data.total;
  userList.value.push(
    ...JSON.parse(JSON.stringify(data.list)).map(item => {
      return {
        ...item,
        nickName: item.nickname,
        id: item.accountId
      };
    })
  );
  userList.value.forEach(item => {
    let flag = allUserList.value.includes(ele => item[userProps.value.id] === ele[userProps.value.id]);
    if (!flag) {
      allUserList.value.push(JSON.parse(JSON.stringify(item)));
    }
  });
  emits('updateUserList', allUserList.value);
};

const checkedChange = async e => {
  await nextTick();
  if (props.mode !== 'multiple' && e && e.length) {
    userSelectId.value = [e[e.length - 1]];
  }
  emits(
    'update:modelValue',
    userSelectId.value.map(item => {
      let a = allUserList.value.find(ele => item === ele[userProps.value.id]);
      if (!a) {
        let obj = props.modelValue.find(el => el.id == item);
        a = {
          [userProps.value.id]: obj.id,
          [userProps.value.name]: obj.name
        };
      }
      return {
        id: a[userProps.value.id],
        name: a[userProps.value.name],
        type: 'user'
      };
    })
  );
};
const setValue = newVal => {
  if (props.mode !== 'multiple') {
    userSelectId.value = newVal && newVal.length ? [newVal[newVal.length - 1].id] : [];
  } else {
    userSelectId.value = newVal.map(item => item.id);
  }
};

watch(
  () => props.modelValue,
  newVal => {
    setValue(newVal);
  },
  {
    deep: true
  }
);

defineExpose({
  setValue
});
</script>
<style scoped>
.userOrgList {
  width: 100%;
  height: var(--tabPaneHeight);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.orglist,
.userList {
  width: 50%;
  height: 100%;
  overflow: hidden;
}
.orglist {
  border-right: 1px solid var(--el-border-color);
  overflow-y: auto;
}

.userListS {
  width: 100%;
  height: calc(100% - 35px);
  overflow: hidden;
  overflow-y: auto;
}

.custom-tree-node {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

:deep(.el-checkbox) {
  width: 100%;
  justify-content: space-between;
}

:deep(.el-checkbox__input) {
  order: 2;
}

:deep(.el-checkbox__inner) {
  border-radius: var(--mode-border-radius);
}

.nodata {
  width: 100%;
  height: calc(100% - 35px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-placeholder);
}
</style>

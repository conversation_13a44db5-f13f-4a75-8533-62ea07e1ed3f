import { isVNode, unref } from 'vue';

export const useRender = () => {
  const contentRender = (render, scope) => {
    const content = render({ row: unref(scope.row), index: scope.$index });
    return isVNode(content) ? content : <span>{content || '--'}</span>;
  };

  const defaultRenderCell = ({ row, column, $index }) => {
    const property = column.property;
    const value = property && getProp(row, property).value;
    if (column && column.formatter) {
      return column.formatter(row, column, value, $index) || '--';
    }
    return $utils.isNil(value) ? '--' : value.toString();
  };

  const getProp = (obj, path, defaultValue) => {
    return {
      get value() {
        return $utils.get(obj, path, defaultValue);
      },
      set value(val) {
        $utils.set(obj, path, val);
      }
    };
  };

  return { contentRender, defaultRenderCell };
};

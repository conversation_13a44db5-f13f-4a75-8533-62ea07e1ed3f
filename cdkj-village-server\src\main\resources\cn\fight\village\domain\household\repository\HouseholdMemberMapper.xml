<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.household.repository.HouseholdMemberMapper">

    <update id="deleteByHouseId">
        update public.rlams_member
        set deleted = 1,
            deleter_id = #{userId},
            delete_time = LOCALTIMESTAMP
        where house_id = #{houseId}
    </update>

    <select id="selectHomeMembers" resultType="cn.fight.village.domain.household.vo.HouseMember">
        select
            m.uuid,
            h.uuid householdId,
            h.household_code householdCode,
            m.name,
            m.id_code idCode,
            m.id_type idType,
            m.gender,
            m.relation,
            h.location,
            m.householder,
            m.phone
        from public.rlams_household h
        inner join public.rlams_member m on h.uuid = m.house_id and m.deleted = 0
        where h.deleted = 0 and h.uuid = #{householdId}
        <if test="exHouseholder != null and exHouseholder == 1">
            and m.householder != 1
        </if>
    </select>

    <select id="selectByIdCode" resultType="cn.fight.village.domain.household.vo.HouseMember">
        select
            m.uuid,
            m.name,
            m.id_code idCode,
            m.id_type idType,
            m.gender,
            m.relation,
            m.householder,
            m.phone
        from public.rlams_member m
        where m.id_code = #{idCode} and m.deleted = 0
    </select>
</mapper>
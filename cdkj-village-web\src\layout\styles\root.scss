@use '@/styles/mixins/utils' as *;
@use './var.scss' as *;

:root {
  @include set-component-css-var('header', $header);
  @include set-component-css-var('aside-menu', $aside-menu);
  @include set-component-css-var('nav-menu', $nav-menu);
  @include set-component-css-var('logo', $logo);
  @include set-component-css-var('multi-tab', $multi-tab);
  @include set-component-css-var('app-content', $app-content);
  @include set-component-css-var('funi-curd', $funi-curd);
  @include set-component-css-var('funi-form', $funi-form);
  @include set-css-var-value('transition-time-02', 0.2s);
}

.el-popper .el-menu--popup-container > .el-menu.el-menu--popup > .el-menu-item {
  color: var(--el-text-color-primary);
}
.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-menu .el-sub-menu__title {
  color: var(--el-text-color-primary);
}

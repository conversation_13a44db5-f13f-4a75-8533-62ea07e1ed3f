package cn.fight.village.domain.land;

import cn.fight.village.domain.common.constant.BusinessConstant;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.contract.entity.Contract;
import cn.fight.village.domain.contract.entity.TransProtocolLand;
import cn.fight.village.domain.contract.repository.TransProtocolLandRepository;
import cn.fight.village.domain.land.entity.*;
import cn.fight.village.domain.land.repository.GisLandRepository;
import cn.fight.village.domain.land.repository.RealLandRepository;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.domain.user.service.UserService;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 地理土地服务层
 */
@Service
public class RealLandService {

    @Resource
    private RealLandRepository realLandRepository;

    @Resource
    private GisLandRepository gisLandRepository;

    @Resource
    private TransProtocolLandRepository transProtocolLandRepository;

    @Transactional(rollbackFor = BusinessException.class)
    public JsonResult createLand(RealLandRequest realLand, User user) {
        RealLand land = realLand.getLand();
        land.manageCreateInfo(user);
        land.setNewCreated(BusinessConstant.TRUE);
        land.setLandNo(this.createRealLandNo()); //土地序列号

        if(land.getArea() == null) {
            throw new BusinessException("土地面积不能为空");
        }

        if (this.selectByLandNo(land.getLandNo()) != null) {
            throw new BusinessException("地块编号重复");
        }

        if (realLandRepository.insert(land) != 1 ) {
            throw new BusinessException("插入地理土地信息数据失败");
        }

        //gis土地信息入库
        GisLand gisLand = realLand.getGisLand();
        if (gisLand != null) {
            gisLand.setDkbm(land.getLandNo());
            gisLand.setGid(gisLandRepository.getNextGid());
            //gisLand.setScmj(land.getArea().toString());

            if (gisLandRepository.insert(gisLand) != 1) {
                throw new BusinessException("插入GIS信息失败");
            }

            //维护坐标信息
            if (StringUtils.isNotBlank(gisLand.getGeomStr())) {
                gisLandRepository.updateGeo(gisLand);
            }
        } else {
            throw new BusinessException("请输入GIS土地信息");
        }

        //家庭承包信息关联,当仅有家庭户ID时才保存
        if (!StringUtils.isBlank(realLand.getHouseholdId())) {
            String householdId = realLand.getHouseholdId();
            String householder = realLand.getHouseholder();
            if (StringUtils.isBlank(householdId) || StringUtils.isBlank(householder)) {
                throw new BusinessException("请输入家庭承包户信息");
            }
            Map<Object, Object> chenbaoInsert = new HashMap<>();
            chenbaoInsert.put("householdId", householdId);
            chenbaoInsert.put("householder", householder);
            chenbaoInsert.put("realLandId", land.getUuid());
            if (realLandRepository.insertChenbaoRelation(chenbaoInsert) != 1) {
                throw new BusinessException("插入土地家庭承包信息失败");
            }

        }

        return JsonResult.valueOfObject(land.getLandNo());
    }


    public JsonResult getLandInfo(String landNo) {
        RealLandValue result = new RealLandValue();
        RealLand realLand = this.selectByLandNo(landNo);

        if (realLand == null) {
            throw new BusinessException("未查询到指定地块");
        }

        //BeanUtils.copyProperties(realLand, result);
        result.setLand(realLand);

        //查询Gis土地
        result.setGisLand(this.getGisLand(realLand.getLandNo()));

        //查询承包家庭信息
        Map<String, Object> chenbaoInfo = realLandRepository.getChenbaoInfo(realLand.getUuid());
        if (MapUtil.isNotEmpty(chenbaoInfo)) {
            result.setHouseholdId(MapUtil.getStr(chenbaoInfo,"householdid"));
            result.setHouseholder(MapUtil.getStr(chenbaoInfo,"householder"));
        }

        return JsonResult.valueOfObject(result);
    }


    private String createRealLandNo(){
        //日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String datePart = dateFormat.format(new Date());

        //序列
        String seqPart = String.format("%04d", realLandRepository.getRealLandNoSec()); // 限制在4位数内
        return 'X' + datePart + seqPart;
    }


    private RealLand selectByLandNo(String landNo) {
        if (StringUtils.isBlank(landNo)) {
            return null;
        }

        return realLandRepository.selectOne(new LambdaQueryWrapper<RealLand>()
                .eq(RealLand::getLandNo,landNo)
                .eq(RealLand::getDeleted,Contract.IS_NOT_DELETED)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult deleteLand(RealLandRequest realLand, User user) {
        String landNo = realLand.getLandNo();
        RealLand land = this.selectByLandNo(landNo);
        if (land == null) {
            throw new BusinessException("待删除的土地不存在");
        }

        //已经签订协议的土地
        List<TransProtocolLand> transProtocolLand = transProtocolLandRepository.selectList(new LambdaQueryWrapper<TransProtocolLand>()
                .eq(TransProtocolLand::getLandNo, landNo));
        if(CollectionUtils.isNotEmpty(transProtocolLand)) {
            throw new BusinessException("该土地已经签订流转协议，不能删除");
        }

        RealLand deleter = new RealLand();
        deleter.setUuid(land.getUuid());
        deleter.manageDeleteInfo(user);

        if (realLandRepository.updateById(deleter) != 1 ) {
            throw new BusinessException("删除土地信息失败");
        }

        //删除土地承包关联关系
        realLandRepository.deleteChenbaoRel(land.getUuid());

        //删除Gis地块
        GisLand gisLand = getGisLand(realLand.getLandNo());
        gisLandRepository.deleteGisLand(landNo);

        return JsonResult.successMessage("删除成功");
    }

    /**
     * 查询地块列表
     * 目前仅支持承包方姓名查找
     * @param query
     * @return
     */
    public JsonResult selectList(RealLandQuery query) {
        String underName = query.getUnderName();
        if (StringUtils.isBlank(underName)) {
            throw new BusinessException("请输入承包方姓名查找地块");
        }
        query.checkPageParam();
        PageHelper.startPage(query.getPageNo(),query.getPageSize());

        List<RealLandValue> result = realLandRepository.selectRealLandList(query);
        return JsonResult.valueOfObject(new PageInfo<>(result));
    }

    /**
     * 根据地块编号获取地块
     *
     * @param landNo
     * @return
     */
    public GisLand getGisLand(String landNo) {
        if (StringUtils.isBlank(landNo)) {
            return null;
        }

        return gisLandRepository.selectOne(
                new LambdaQueryWrapper<GisLand>().eq(GisLand::getDkbm,landNo)
        );
    }

    public JsonResult getLandsInfo(RealLandRequest query) {
        return JsonResult.valueOfObject(gisLandRepository.selectProtocolLands(query));
    }
}

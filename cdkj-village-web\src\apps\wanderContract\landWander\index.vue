<template>
  <div>
    <funi-detail :bizName="bizName" :showHead="true" :steps="steps" :detailHeadOption="detailHeadOption" />
  </div>
</template>

<script setup>
import { reactive, computed, ref } from "vue";
import { useRoute } from "vue-router";
import BigNumber from "bignumber.js";
import baseForm from "./components/baseForm.vue";
import contractTemplate from "./components/contractTemplate.vue";
import contractSupplement from "./components/contractSupplement.vue";
import { add } from "@/apps/api/landContract.js";

const route = useRoute();
const bizName = ref(route.query.bizName);
// 头部数据
const detailHeadOption = reactive({
  title: route.query.title,
  hideStatusBar: true,
});

const data = reactive({
  type: "土地流转",
  transProtocol: {
    aType: "2",
    bType: "3",
    partyB:"成都市耘光里土地股份专业合作社",
    bId:"93510112MAEEK96E1A",
    bReprese:"刘明",
    bRepId:"51011219750516301X",
    bLocation:"阳光村村民委员会",
    bPhone:"13608190510",
    usage:"农业种植用途",
    startTime:""+(new Date()).getFullYear(),
    endTime:((new Date()).getFullYear()+2)+"-12-31",//间隔2年
    rent:"1800",
    rentChs:"壹仟捌佰元整",
    rentUpdate:" 每三年在上一年租金基础上递增5%",
    rentPay:"2",
    textObject:{
      rentUpdateYear:"3",
      aDutyDate:"10",
      aAgreeb:"1,2,3,4",
      btgs:"成都市耘光里土地股份专业合作社",
      bcgs:"成都市耘光里土地股份专业合作社",
      contractDate1:"30",
      contractDate2:"30",
      contractDate3:"30",
      czfs:"3,4",
      czfsOther:"但如承租期内修建的基础设施配套所占用的面积，根据总面积核算后退还给甲方。",
      wyzzMoney1:"5",
      wyzzDate1:"30",
      wyzzMoney2:"5",
      wyzzDate2:"30",
      wyzzRatio:"5"
    },
    cropsPay:"青苗费补偿按照第一年30%、第二年30%、第三年支付40%",
    totalArea: "",
    lands: [],
  },
  transProtocolSup: {
    partyB:"成都市耘光里土地股份专业合作社",
    pbReprese:"刘明",
    pbId:"51011219750516301X",
    appointTime: ((new Date()).getFullYear()+2)+"-12-31",//间隔2年
    startTime:""+(new Date()).getFullYear(),
    endTime:((new Date()).getFullYear()+2)+"-12-31",//间隔2年
  },
});

const steps = [
  {
    title: "选地",
    preservable: false,
    type: baseForm,
    props: {
      lands: [],
    },
    on: {
      output: (res,project) => {
        let totalArea = BigNumber(0);
        res.forEach((item) => {
          totalArea = totalArea.plus(item.area);
        });
        data.project = project;//项目归属
        data.transProtocol.totalArea = totalArea.toString();
        data.transProtocol.lands = res.map((x) => {
          return {
            village: "",
            landName: x.dkmc,
            landNo: x.landNo,
            east: x.dkdz,
            south: x.dknz,
            north: x.dkbz,
            west: x.dkxz,
            area: x.area,
            quality: '',
            landType: x.landType,
            contractCode: '',
            remark: x.remark,
          };
        });
      },
    },
  },
  {
    title: "出租协议",
    preservable: false,
    type: contractTemplate,
    props: {
      transProtocol: data.transProtocol,
    },
    on: {
      output: (values) => {
        data.transProtocol = values;
      },
    },
  },
  {
    title: "补充协议",
    preservable: false,
    type: contractSupplement,
    props: {},
    on: {
      output: (values, next) => {
        data.transProtocolSup = values;
        add(data, { isLoading: true }).then((res) => {
          next();
        });
      },
    },
  },
];
</script>

<style lang="scss" scoped></style>

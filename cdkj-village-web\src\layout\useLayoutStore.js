import { defineStore } from 'pinia';
import { ref, unref } from 'vue';

export const useLayoutStore = defineStore('layout', () => {
  // collapse
  const collapse = ref(false);
  const layoutMode = ref('vertical');
  const toggleCollapse = () => (collapse.value = !unref(collapse));
  const toggleLayoutMode = () => {
    layoutMode.value = (() => {
      if (layoutMode.value === 'vertical') return 'horizontal';
      if (layoutMode.value === 'horizontal') return 'vertical';
      return 'vertical';
    })();
  };

  // multi tab
  const activeTab = ref('');

  const activeMenu = ref({});
  return {
    collapse,
    toggleCollapse,
    layoutMode,
    toggleLayoutMode,
    activeTab,
    activeMenu
  };
});

#公共服务配置
spring:
  #环境指定
  profiles:
    active: @profileActive@
  mvc:
    servlet:
      load-on-startup: 10
  application:
    name: cdkj-village
  #线程池
  task:
    execution:
      pool:
        max-size: 30
        core-size: 10
        keep-alive: 60
        queue-capacity: 100

  #模板引擎
  thymeleaf:
    mode: HTML
    cache: true
    prefix: classpath:/templates/
    encoding: UTF-8
    suffix: .html
    check-template-location: true
    template-resolver-order: 1

  #文件上传
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 120MB

#应用上下文
server:
  servlet:
    context-path: /village

#mybatis-plus配置
mybatis-plus:
  configuration:
    call-setters-on-nulls: true

#系统业务参数
business:
  #登录超时时间
  login-timeout: 1800000




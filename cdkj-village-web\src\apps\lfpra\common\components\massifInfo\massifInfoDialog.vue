<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" :title="dialogData.title">
      <div>
        <polygonMap
          @exportMap="exportMap"
          :layerType="dialogData.layerType"
          :isEdit="dialogData.isEdit"
          :id="dialogData.patternId"
          height="300"
          style="margin-bottom: 20px"
        />
        <funi-form :schema="schema" @get-form="e => (form = e)" :rules="rules" :border="false" :col="2" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref, reactive } from 'vue';
import polygonMap from '@/apps/lfpra/common/components/polygonMap/index.vue';
import { ElNotification } from 'element-plus';
import { dictListHttp } from '@/apps/lfpra/common/hooks/api';
import { useSchema, useRules } from './hooks/index.jsx';
// 表单查询
const dialogVisible = ref(false); // 控制模态框显示隐藏
const selection = ref([]);
const form = ref(null);
const loading = ref(false);
const dialogData = reactive({
  title: '',
  isEdit: true,
  menuName: '', //坑塘/家庭承包管理
  addTableList: [], //新增拿到整个列表数据
  rowList: [], //用于编辑 判断是否重复
  patternId: '' //图斑 id
});

//下来选择框
const selectList = reactive({
  obtainType: [
    { label: '是', value: true },
    { label: '否', value: false }
  ], //是否取证
  LandType: [], //土地类型
  cropType: [] //种植情况
});
const emit = defineEmits(['exportObject']);
//显示dailog框
const show = async (title, isEdit, menuName, list, tableList, layerType) => {
  dialogData.title = title;
  dialogData.isEdit = isEdit;
  dialogData.patternId = list?.patternId || '';
  dialogData.layerType = layerType;
  dialogData.menuName = menuName;
  dialogData.addTableList = tableList; //列表数据
  dialogData.rowList = list; //当前行数据

  //编辑，详情
  if (title !== '新增') {
    getFormData(list);
  } else {
    // 新增默认 是否承包为是
    setTimeout(() => {
      form.value.setValues({ isContractedLand: true });
    }, 0);
  }
  getDictLisType('LAND', 'LandType');
  // 家庭承包 显示
  if (dialogData.menuName !== 'pitAndPond') {
    getDictLisType('CROP_COND', 'cropType');
  }
  await nextTick();
  dialogVisible.value = true;
};
// 选择的图斑数据
const exportMap = e => {
  dialogData.patternId = e[0].properties.id;
};
//编辑查看进入回显数据
const getFormData = e => {
  // 地图

  //表单
  setTimeout(() => {
    form.value.setValues(e);
  }, 0);
};
// 配置表单
const schema = computed(() => {
  return useSchema({ isEdit: dialogData.isEdit, ...selectList, menuName: dialogData.menuName });
});
// 表单验证规则
const rules = computed(() => {
  return useRules({ isEdit: dialogData.isEdit, menuName: dialogData.menuName });
});
const getDictLisType = async (e, i) => {
  let { list } = await dictListHttp({ dictiEnum: e });
  selectList[i] = list;
};
//  确认按钮
const confirmFunc = async () => {
  let { isValid } = await form.value.validate();
  if (isValid && verificationMap()) {
    //验证新增时 地块代码重复
    if (
      dialogData.addTableList.length > 0 &&
      dialogData.title == '新增' &&
      dialogData.addTableList.filter(item => item.landCode == form.value.getValues().landCode).length == 1
    ) {
      ElNotification({
        title: dialogData.menuName !== 'pitAndPond'? '地块代码重复！':'坑塘代码重复！',
        type: 'warning'
      });
      return;
    }
    //验证编辑时 地块代码重复
    if (
      dialogData.addTableList.length > 0 &&
      dialogData.title == '编辑' &&
      dialogData.rowList.landCode !== form.value.getValues().landCode &&
      dialogData.addTableList.filter(item => item.landCode == form.value.getValues().landCode).length == 1
    ) {
      ElNotification({
        title: dialogData.menuName !== 'pitAndPond'? '地块代码重复！':'坑塘代码重复！',
        type: 'warning'
      });
      return;
    }
    let formParam = {
      ...form.value.getValues(),
      dicLandName: selectList.LandType.filter(item => item.code == form.value.getValues().dicLandCode)[0].name,
      dicCropCondName:
        selectList.cropType.length > 0
          ? selectList.cropType.filter(item => item.code == form.value.getValues().dicCropCondCode)[0].name
          : '',
      cerType: dialogData.menuName !== 'pitAndPond' ? 1 : 2,
      sort: dialogData.title == '编辑'|| dialogData.title == '查看' ? dialogData.rowList.sort : dialogData.addTableList.length + 1,
      patternId: dialogData.patternId
    };
    emit('exportObject', unref(formParam));
    cancelClick();
  }
};
// 判断是否图斑信息
const verificationMap = () => {
  if (!dialogData.patternId) {
    ElNotification({
      title: '提示',
      message: '请选择地块',
      type: 'warning'
    });
    return false;
  }
  return true;
};
//  取消按钮
const cancelClick = () => {
  dialogVisible.value = false;
};

defineExpose({
  show
});
</script>
<style scoped></style>

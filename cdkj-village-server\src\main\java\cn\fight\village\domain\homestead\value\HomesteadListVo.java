package cn.fight.village.domain.homestead.value;

import cn.fight.village.domain.common.entity.BaseValue;

/**
 * 宅基地信息列表Vo
 *
 */
public class HomesteadListVo extends BaseValue {
    //宅基地编号
    private String code;

    //面积
    private Double area;

    //是否取证
    private String hasCert;

    //使用情况
    private String usage;

    //姓名
    private String  name;

    //证件类型
    private String  idType;

    //证件号码
    private String  idCode;

    //家庭住址
    private String location;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getHasCert() {
        return hasCert;
    }

    public void setHasCert(String hasCert) {
        this.hasCert = hasCert;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}

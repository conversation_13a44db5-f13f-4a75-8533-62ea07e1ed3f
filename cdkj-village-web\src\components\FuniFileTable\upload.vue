<!--
 * @Author: 古加文 <EMAIL>
 * @Date: 2023-06-05 17:14:02
 * @LastEditors: 古加文 <EMAIL>
 * @LastEditTime: 2023-11-14 17:44:31
 * @FilePath: \funi-paas-cs-web-cli\src\components\FuniFileTable\upload.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    @close="cancel"
    :close-on-press-escape="false"
    title="文件上传"
    width="30%"
    center
  >
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      drag
      :action="uploadBaseUrl + url"
      :data="uploadData"
      :accept="uploadAccept"
      multiple
      :on-error="onError"
      :on-success="onSuccess"
      :before-remove="beforeRemove"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">拖拽文件 或 <em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip">
          {{ uploadAcceptText ? `${uploadAcceptText}` : '' }}
          {{ uploadAcceptText && needNumText ? `,${needNumText}` : needNumText }}
        </div>
      </template>
    </el-upload>
    <!-- <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="confirm">
                    确定
                </el-button>
            </span>
        </template> -->
  </el-dialog>
</template>

<script setup>
import { UploadFilled } from '@element-plus/icons-vue';
import { ref } from 'vue';
import { ElNotification } from 'element-plus';
const props = defineProps({
  url: String,
  type: String,
  removeFile: Function
});

const uploadBaseUrl = window.$utils.getServerBaseApi();

const dialogVisible = ref(false);

const uploadRef = ref();
const uploadData = ref({});
const uploadAccept = ref();
const uploadAcceptText = ref();
const needNumText = ref();

const emit = defineEmits(['on-close']);

const onError = function (error) {};

const onSuccess = function (response, uploadFile, uploadFiles) {
  if (response.success) {
    uploadFile.attachmentInfoId = response.data.attachmentInfoId;
    uploadFile.status = 'success';
  } else {
    ElNotification({
      title: '上传失败',
      message: response.message,
      type: 'error'
    });
    uploadFiles.splice(
      uploadFiles.findIndex(item => item.uid == uploadFile.uid),
      1
    );
  }
};

const show = function (params = {}, accept = '', needNum) {
  uploadData.value = params;
  uploadAcceptText.value = accept ? accept.split(',').join('/') : '';
  needNumText.value = needNum ? `需要${needNum}份` : '';
  uploadAccept.value = accept
    ? accept
        .split(',')
        .map(item => '.' + item)
        .join(',')
    : '';
  dialogVisible.value = true;
};

const cancel = function () {
  emit('on-close');
  uploadRef.value.clearFiles();
  dialogVisible.value = false;
};

const confirm = function () {
  emit('on-close');
  uploadRef.value.clearFiles();
  dialogVisible.value = false;
};

const beforeRemove = async function (uploadFile, uploadFiles) {
  if (props.removeFile) {
    let result = await props.removeFile(uploadFile);
    return result;
  }
  return false;
};

defineExpose({
  show
});
</script>

<style lang="scss" scoped></style>

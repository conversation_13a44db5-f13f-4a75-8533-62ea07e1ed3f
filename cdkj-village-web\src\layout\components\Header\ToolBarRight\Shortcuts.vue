<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-10-13 16:59:53
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-08 18:48:52
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/Header/ToolBarRight/Shortcuts.vue
 * @Description:
 * Copyright (c) 2023 by tao.yang <EMAIL>, All Rights Reserved.
-->
<template>
  <div class="funi-layout-header__tools">
    <!-- 侧边栏菜单控制 -->
    <el-tooltip v-if="isVertical" :content="collapseContent" placement="bottom">
      <span data-toos @click="store.toggleCollapse">
        <funi-icon icon="mdi:dock-left" style="font-size: 16px"></funi-icon>
      </span>
    </el-tooltip>

    <!-- 切换布局 -->
    <el-tooltip content="主题风格" placement="bottom">
      <span data-toos @click="themeConfig">
        <funi-icon icon="tdesign:brush" style="font-size: 16px"></funi-icon>
      </span>
    </el-tooltip>

    <!-- 全屏 -->
    <el-tooltip content="全屏" placement="bottom">
      <span data-toos @click="handleFullscreen">
        <funi-icon icon="fluent-mdl2:full-view" style="font-size: 16px"></funi-icon>
      </span>
    </el-tooltip>
  </div>
</template>
<script setup>
import { useLayoutStore } from '@/layout/useLayoutStore';
import { computed, inject } from 'vue';

defineOptions({ name: 'Shortcuts' });
const openThemeConfig = inject('openThemeConfig');
const themeConfig = () => openThemeConfig();

const store = useLayoutStore();

const isVertical = computed(() => store.layoutMode === 'vertical');
const collapseContent = computed(() => (store.collapse ? '显示左侧菜单' : '隐藏左侧菜单'));

const handleFullscreen = () => {
  if(document.fullscreenElement){
    document.exitFullscreen()
  }
  else{
    document.documentElement.requestFullscreen()
  }
};
</script>
<style scoped>
span[data-toos] + span[data-toos] {
  margin-left: 16px;
}
</style>

DROP FUNCTION IF EXISTS nextval; 
CREATE FUNCTION nextval (sequence_code VARCHAR(50)) 
     RETURNS INTEGER 
     LANGUAGE SQL 
     DETERMINISTIC 
     CONTAINS SQL 
     SQL SECURITY DEFINER 
     COMMENT '获取下一个序列值' 
BEGIN 
		UPDATE table_name
    SET current_value = current_value + increment 
    WHERE seq_code = sequence_code; 
		
    RETURN (
	select current_value as result
	from sequence
	where seq_code = sequence_code
	);
END 
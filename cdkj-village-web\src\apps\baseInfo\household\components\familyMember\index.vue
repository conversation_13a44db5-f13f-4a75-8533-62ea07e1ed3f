<template>
  <div>
    <header class="title">
      <FuniGroupTitle title="人员信息" />
      <el-button v-if="!isDetail" type="primary" @click="_add">新增</el-button>
    </header>
    <funi-curd-v2 ref="refFuniListPage" :columns="cardTab.columns" :data="cardTab.dataList" :pagination="false">
    </funi-curd-v2>
    <AddModal ref="addModalRef" :isDetail="isDetail" @addCallBack="addCallBack" />
  </div>
</template>
<script setup lang="jsx">
import { ref, reactive, watch } from 'vue';
import { ElNotification } from 'element-plus';
import AddModal from './addModal.vue';
const props = defineProps({
  personnelData: {
    type: Array,
    default: []
  },
  isDetail: {}
});
const refFuniListPage = ref();
const addModalRef = ref(null);
const cardTab = reactive({
  dataList: [],
  columns: [
    {
      label: '姓名',
      prop: 'name',
      render: ({ row, index }) => {
        if (props.isDetail) {
          return (
            <el-button type="primary" link onClick={()=>_edit(row,index)}>
              {row.name}
            </el-button>
          );
        } else {
          return row.name;
        }
      }
    },
    {
      label: '性别',
      prop: 'gender'
    },
    { label: '证件类型', prop: 'idType' },
    { label: '证件号码', prop: 'idCode' },
    { label: '联系方式', prop: 'phone' },
    { label: '家庭关系', prop: 'relation' },
    {
      label: '操作',
      prop: 'operList',
      align: 'center',
      fixed: 'right',
      hidden: props.isDetail,
      render: ({ row, index }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => {
                _edit(row, index);
              }}
            >
              编辑
            </el-button>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                _delete(row, index);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ]
});

const subscript = ref();

const addCallBack = res => {
  let { isAdd, data, next } = res;
  if (isAdd) {
    if (cardTab.dataList.find(x => x.name == data.name)) {
      ElNotification({
        title: '已存在此人员信息',
        type: 'warning'
      });
      return;
    }
    cardTab.dataList.push(data);
  } else {
    cardTab.dataList[subscript.value] = data;
  }
  next();
};

const _add = () => {
  addModalRef.value.show();
};
const _edit = (row, index) => {
  subscript.value = index;
  addModalRef.value.show(row);
};
const _delete = (row, index) => {
  cardTab.dataList.splice(index, 1);
};
defineExpose({
  familyMemberRequests: () => cardTab.dataList
});

watch(
  () => props.personnelData,
  () => {
    if (props.personnelData.length > 0) {
      cardTab.dataList = props.personnelData;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

package cn.fight.village.domain.contract.repository;

import cn.fight.village.domain.contract.entity.Contract;
import cn.fight.village.domain.contract.entity.Land;
import cn.fight.village.domain.contract.request.ContractQuery;
import cn.fight.village.domain.contract.value.ContractListVo;
import cn.fight.village.domain.contract.value.HouseHoldContractVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同信息dao层
 */
public interface ContractMapper extends BaseMapper<Contract> {
    /**
     * 合同对象列表查询
     *
     * @param query
     * @return
     */
    List<ContractListVo> getList(ContractQuery query);

    /**
     * 根据户ID查
     *
     * @param householdId
     * @param type
     * @return
     */
    List<HouseHoldContractVo> queryLandByHousehold(@Param("householdId") String householdId, @Param("type") String type);

    /**
     * 流转合同查询列表
     * @param query
     * @return
     */
    List<ContractListVo> getList4Trans(ContractQuery query);

    /**
     * 流转潜水确认
     * @param uuid
     * @param signed
     * @param userId
     * @return
     */
    int protocolSureSign(@Param("uuid") String uuid, @Param("signed") String signed, @Param("userId") String userId);

    /**
     * 根据合同ID获取所有土地ID
     * @param contractId
     * @return
     */
    List<String> selectTransLandNos(String contractId);
}

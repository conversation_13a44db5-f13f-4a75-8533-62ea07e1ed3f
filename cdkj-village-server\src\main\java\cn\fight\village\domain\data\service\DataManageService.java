package cn.fight.village.domain.data.service;

import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.domain.contract.entity.*;
import cn.fight.village.domain.contract.repository.*;
import cn.fight.village.domain.contract.request.ContractRequest;
import cn.fight.village.domain.data.entity.HouseholdExcel;
import cn.fight.village.domain.data.entity.LandExcel;
import cn.fight.village.domain.data.listener.HouseholdListener;
import cn.fight.village.domain.data.listener.LandListener;
import cn.fight.village.domain.doc.entity.Document;
import cn.fight.village.domain.doc.repository.DocumentMapper;
import cn.fight.village.domain.household.entity.Household;
import cn.fight.village.domain.household.entity.HouseholdMember;
import cn.fight.village.domain.household.repository.HouseholdMapper;
import cn.fight.village.domain.household.repository.HouseholdMemberMapper;
import cn.fight.village.domain.household.service.HouseholdService;
import cn.fight.village.domain.household.vo.HouseMember;
import cn.fight.village.domain.land.entity.GisLand;
import cn.fight.village.domain.land.entity.RealLand;
import cn.fight.village.domain.land.repository.GisLandRepository;
import cn.fight.village.domain.land.repository.RealLandRepository;
import cn.fight.village.domain.user.entity.User;
import cn.hutool.core.map.MapUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据处理服务层
 */
@Service
public class DataManageService {

    private Integer lock = 0;

    @Resource
    private HouseholdMemberMapper householdMemberMapper;

    @Resource
    private HouseholdMapper householdMapper;

    @Resource
    private HouseholdService householdService;

    @Resource
    private ContractMapper contractMapper;

    @Resource
    private LandMapper landMapper;

    @Resource
    private UnderMapper underMapper;

    @Resource
    private UpperMapper upperMapper;

    private  Integer memberCount = 0;

    @Resource
    private DocumentMapper documentMapper;

    @Resource
    private UnderMemberMapper underMemberMapper;

    @Resource
    private GisLandRepository gisLandRepository;

    @Resource
    private RealLandRepository realLandRepository;

    private Integer houseCount = 0;

    @Transactional(rollbackFor = Exception.class)
    public String householdParse(MultipartFile file) throws IOException {
        synchronized (this.lock) {
            if (this.lock > 0) {
                //return "重复操作";
            }
            this.lock++;
        }

        //户与成员列表
        //key:序号 val:成员列表
        Map<String, List<HouseholdExcel>> householdMap = new HashMap<>();

        HouseholdListener listener = new HouseholdListener();
        EasyExcel.read(file.getInputStream(), HouseholdExcel.class, listener)
                .sheet(0)
                .headRowNumber(1)
                .doRead();

        List<HouseholdExcel> dataList = listener.getResultList();
        List<HouseholdExcel> householdExcels = new ArrayList<>(); //暂存列表

        if (CollectionUtils.isEmpty(dataList)) {
            return "解析数据为空";
        }

        for (HouseholdExcel householdItem : dataList) {
            //获取序号
            String sort = householdItem.getSort();

            //序号不为空，新的一户
            if (!StringUtils.isEmpty(sort)) {
                householdExcels = new ArrayList<>();
                householdExcels.add(householdItem);
                householdMap.put(householdItem.getIdCode(), householdExcels);
            }
            //序号为空，成员
            else {
                //列表为空，从map获取户列表
                if (CollectionUtils.isEmpty(householdExcels)) {
                    continue;
                }

                householdExcels.add(householdItem);
            }

        }

        this.houseCode = 0; //户号置零
        householdMap.forEach((String code, List<HouseholdExcel> members) -> {
            System.out.println("=========================新的一家庭户=======================");
            this.manageHousehold(members);
            return;
        });

        return "解析完成，共解析:" + dataList.size() + " ,成员：" + this.memberCount + " ,家庭户： " + this.houseCode;
    }

    private Integer houseCode = 1;
    DecimalFormat df = new DecimalFormat("00000");

    private void manageHousehold(List<HouseholdExcel> householdExcels) {
        Date now = new Date();

        if (CollectionUtils.isEmpty(householdExcels)) {
            return;
        }

        String houseId  = CommonUtils.getGuid(); //户ID
        String masterId  = CommonUtils.getGuid(); //户主id

        HouseholdExcel masterExcel = null;
        HouseholdMember master = null; //户主对象

        List<HouseholdMember> householdMembers = new ArrayList<>(); //成员插入列表

        for (HouseholdExcel householdExcel : householdExcels) {
            String relation = householdExcel.getRelation();
            //户主
            if (!StringUtils.isEmpty(relation) && relation.contains("户主")) {
                 master = createMember(
                        1,
                        houseId,
                        masterId,
                        householdExcel,
                        now
                );

                 householdMembers.add(master);

               masterExcel = householdExcel;
            }
            //普通成员
            else {
                String guid = CommonUtils.getGuid();
                System.out.println("guid = " + guid);
                householdMembers.add(
                        createMember(
                                0,
                                houseId,
                                guid,
                                householdExcel,
                                now
                        ));
            }
        }

        //没有户主情况
        if (master == null) {
            master = householdMembers.get(0);
            masterExcel = householdExcels.get(0);

            masterId = master.getUuid();
        }

        //户信息添加
        Household house= new Household();
        house.setUuid(houseId);
        house.setHouseholderId(masterId);
        house.setHouseholdCode("YGC-" + df.format( this.houseCode++));
        house.setLocation(masterExcel.getAddress());
        house.setPoor("否");
        house.setLower("否");
        house.setDeleted(0);
        house.setCreateTime(now);
        house.setCreatorId("数据导入-1批次");

        //数据入库
        //成员信息入库
        if (!CollectionUtils.isEmpty(householdMembers)) {
            for (HouseholdMember householdMember : householdMembers) {
                System.out.println(householdMember);

                try {
                    memberCountAdd(
                            householdMemberMapper.insert(householdMember)
                    );
                } catch (Exception e) {
                    System.out.println("=====================================================数据错误：" + master.getName());
                    //System.out.println("householdMember = " + JSON.toJSONString(householdMember));
                    throw e;
                }
            }
        }

        //户信息入库
        System.out.println(house);
        houseCountAdd(
                householdMapper.insert(house)
        );
    }

    private synchronized void memberCountAdd(int input) {
        this.memberCount += input;
    }

    private synchronized void houseCountAdd(int input) {
        this.houseCode += input;
    }

    private HouseholdMember createMember(
            Integer isMaster, //是否户主
            String houseId,
            String uuid,
            HouseholdExcel householdExcel,
            Date now
    ) {
        HouseholdMember householdMember = new HouseholdMember();
        householdMember.setUuid(uuid);
        householdMember.setHouseId(houseId);
        householdMember.setGender(householdExcel.getGender());
        householdMember.setName(householdExcel.getName());
        householdMember.setIdCode(householdExcel.getIdCode());
        householdMember.setIdType("身份证");
        householdMember.setPhone(householdExcel.getPhone());
        householdMember.setRelation(householdExcel.getRelation());
        householdMember.setHouseholder(isMaster);
        householdMember.setCollMember(householdExcel.getCollMember());
        householdMember.setRemark(householdExcel.getRemark());
        householdMember.setRemarkTime(now);
        householdMember.setCreateTime(now);
        householdMember.setCreatorId("数据导入-1批次");
        householdMember.setDeleted(0);
        householdMember.setPhoneSec("");

        householdMember.setHuko("是");
        householdMember.setVeteran("否");
        householdMember.setDisabled("否");
        return householdMember;
    }

    @Transactional(rollbackFor = Exception.class)
    public String landParse(MultipartFile file) throws Exception {
        if (file == null) {
            return "文件为空";
        }

        LandListener listener = new LandListener();
        EasyExcel.read(file.getInputStream(), LandExcel.class, listener)
                .sheet(0)
                .headRowNumber(2)
                .doRead();

        List<LandExcel> dataList = listener.getResultList();
        if (CollectionUtils.isEmpty(dataList)) {
            return "解析数据为空";
        }

        Map<String, List<LandExcel>> homeLands = new ConcurrentHashMap<>();
        for (LandExcel landExcel : dataList) {
            String houseHolderIdCode = landExcel.getUnderIdNo(); //户主身份证号
            if (StringUtils.isEmpty(houseHolderIdCode)) {
                continue;
            }

            List<LandExcel> landExcels = homeLands.get(houseHolderIdCode);
            if (CollectionUtils.isEmpty(landExcels)) {
                landExcels = new ArrayList<>();
            }
            landExcels.add(landExcel);

            homeLands.put(houseHolderIdCode, landExcels);
        }

        homeLands.forEach((k, v) -> {
            //if (!"510112196903233015".equals(k)) {
                this.homeLandManage(k,v);
            //}
        });
        /*List<LandExcel> landExcels = homeLands.get("510112196903233015");
        this.homeLandManage("510112196903233015",landExcels);*/

        return "成功解析" + dataList.size();
    }

    //@Async
    public void homeLandManage(String houseHolderId,List<LandExcel> lands) {
        if (StringUtils.isEmpty(houseHolderId)) {
            return;
        }

        Date now = new Date();
        String contractId = CommonUtils.getGuid();
        Household household = householdService.getHouseHoldByHouseholderIdCode(houseHolderId);
        if (household == null) {
            household = new Household();
            household.setUuid("unknow household");
        }

        ContractRequest contract = new ContractRequest();
        contract.setUuid(contractId);
        contract.setType("家庭承包");
        contract.setContractNo(this.getContractCode());
        contract.setHouseholdId(household.getUuid());

        //土地
        if (CollectionUtils.isEmpty(lands)) {
            return;
        }
        contract.setRemark(lands.get(0).getGroup()); //组别

        double totalArea = 0d;

        List<Land> landList = new ArrayList<>();
        for (LandExcel land : lands) {
            Land insertLand = new Land();
            BeanUtils.copyProperties(land, insertLand);
            insertLand.setContractId(contractId);
            insertLand.setType("土地");
            insertLand.setCreateTime(now);
            insertLand.setCreatorId("数据导入-1批次");
            insertLand.setDeleted(0);
            insertLand.setUuid(CommonUtils.getGuid());
            landList.add(insertLand);

            totalArea += land.getRightArea();
        }

        //发包方
        List<Upper> upperList = new ArrayList<>();
        Upper upper = new Upper();
        BeanUtils.copyProperties(lands.get(0), upper);
        upperList.add(upper);
        upper.setContractId(contractId);
        upper.setCreateTime(now);
        upper.setCreatorId("数据导入-1批次");
        upper.setDeleted(0);
        upper.setUuid(CommonUtils.getGuid());


        //承包方
        //获取户主
        HouseMember houseHolder = householdMemberMapper.selectByIdCode(houseHolderId);

        List<Under> underList = new ArrayList<>();
        Under under = new Under();
        String underId = CommonUtils.getGuid();
        BeanUtils.copyProperties(lands.get(0), under);
        underList.add(under);
        under.setUpper(lands.get(0).getUnder());
        under.setUnderName(lands.get(0).getUnder());
        under.setUnderLocation(household.getLocation());
        under.setUnderPhone(houseHolder == null ? null : houseHolder.getPhone());
        under.setCreateTime(now);
        under.setCreatorId("数据导入-1批次");
        under.setContractId(contractId);
        under.setDeleted(0);
        under.setRightArea(totalArea);
        under.setUuid(underId);

        contract.setLandList(landList);
        contract.setUpperList(upperList);
        contract.setUnderList(underList);

        //共有人信息
        List<HouseMember> houseMembers = householdMemberMapper.selectHomeMembers(household.getUuid(), 1);
        if (!CollectionUtils.isEmpty(houseMembers)) {
            for (HouseMember houseMember : houseMembers) {
                UnderMembers underMembers = new UnderMembers();
                BeanUtils.copyProperties(houseMember, underMembers);
                underMembers.setUuid(CommonUtils.getGuid());
                underMembers.setUnderId(underId);
                underMembers.setCreateTime(now);
                underMembers.setCreatorId("数据导入-1批次");
                underMembers.setDeleted(0);

                underMemberMapper.insert(underMembers);
            }
        }

        //保存合同
        Contract contractSaver = new Contract();
        BeanUtils.copyProperties(contract,contractSaver);
        contractSaver.setDeleted(0);
        contractSaver.setCreateTime(now);
        contractSaver.setCreatorId("数据导入-1批次");
        contractMapper.insert(contractSaver);

        //保存土地
        for (Land land : landList) {
            landMapper.insert(land);
        }

        upperMapper.insert(upper);

        underMapper.insert(under);
    }


    private String getContractCode() {
        return "YGC-JTCB-" + df.format( this.houseCode++);
    }


    @Transactional
    public String landDocMag() {

        //获取所有合同
        Wrapper<Contract> contractWp = new LambdaQueryWrapper<Contract>()
                .eq(Contract::getDeleted, Contract.IS_NOT_DELETED);
        List<Contract> contracts = contractMapper.selectList(contractWp);
        if (CollectionUtils.isEmpty(contracts)) {
            return "获取合同为空";
        }

        User user = new User();
        user.setUuid("数据导入-1批次");

        String PATH = "/documents/";

        //遍历合同
        for (Contract contract : contracts) {
            String contractUuid = contract.getUuid();
            Contract contractUpdater = new Contract();
            contractUpdater.setUuid(contractUuid);
            String conNo = null;

            Wrapper<Under> underwp = new LambdaQueryWrapper<Under>()
                    .eq(Under::getDeleted, Contract.IS_NOT_DELETED)
                    .eq(Under::getContractId, contractUuid);
            List<Under> unders = underMapper.selectList(underwp);
            if (!CollectionUtils.isEmpty(unders)) {
                Under under = unders.get(0);
                conNo = "YGC-JTCB-" + under.getContractCretNo();
                contractUpdater.setContractNo(conNo);
            }

            //创建档案对象
            Document inserter = new Document();
            inserter.manageCreateInfo(user);
            inserter.setDocNo(conNo);
            inserter.setPath(PATH + conNo);
            inserter.setContractId(contract.getUuid());

            System.out.println(inserter);

            contractMapper.updateById(contractUpdater);

            documentMapper.insert(inserter);
        }

        return "处理数据：" + contracts.size();
    }

    /**
     * 敏感字段加密
     * @return
     */
    public String encode() {
        //家庭成员信息加密
        List<HouseholdMember> householdMembers = householdMemberMapper
                .selectList(new LambdaQueryWrapper<HouseholdMember>()
                        .eq(HouseholdMember::getDeleted, HouseholdMember.IS_NOT_DELETED));

        List<List<HouseholdMember>> memberLists = ListUtils.partition(householdMembers, 500);
        for (List<HouseholdMember> memberList : memberLists) {
            this.asyncManage(memberList);
        }

        //发包方数据加密
        List<Upper> uppers = upperMapper.selectList(new LambdaQueryWrapper<Upper>()
                .eq(Upper::getDeleted, Upper.IS_NOT_DELETED));
        List<List<Upper>> upperLists = ListUtils.partition(uppers, 500);
        for (List<Upper> upperList : upperLists) {
            this.asyncManage4Upper(upperList);
        }

        //承包方
         List<Under> unders = underMapper.selectList(new LambdaQueryWrapper<Under>()
                .eq(Under::getDeleted, Under.IS_NOT_DELETED));
        List<List<Under>> underLists = ListUtils.partition(unders, 500);
        for (List<Under> underList : underLists) {
            this.asyncManage4Under(underList);
        }

        //承包方共有人
        List<UnderMembers> underMembersList = underMemberMapper.selectList(new LambdaQueryWrapper<UnderMembers>()
                .eq(UnderMembers::getDeleted, UnderMembers.IS_NOT_DELETED));
        List<List<UnderMembers>> underMemberLists = ListUtils.partition(underMembersList, 500);
        for (List<UnderMembers> underMemberList : underMemberLists) {
            this.asyncManage4UnderMembers(underMemberList);
        }

        return "执行中";
    }

    public void asyncManage4UnderMembers(List<UnderMembers> underMemberList) {
        for (UnderMembers underMembers : underMemberList) {
            SecureUtils.sensitiveFieldEncrypt(underMembers);
            underMemberMapper.updateById(underMembers);
            System.out.println("数据写入...");
        }
    }

    @Async
    public void asyncManage4Under(List<Under> underList) {
        for (Under under : underList) {
            SecureUtils.sensitiveFieldEncrypt(under);
            underMapper.updateById(under);
            System.out.println("数据写入...");
        }
    }

    @Async
    public void asyncManage(List<HouseholdMember> list) {
        for (HouseholdMember householdMember : list) {
            SecureUtils.sensitiveFieldEncrypt(householdMember);
            householdMemberMapper.updateById(householdMember);
            System.out.println("数据写入...");
        }
    }

    @Async
    public void asyncManage4Upper(List<Upper> list) {
        for (Upper item : list) {
            SecureUtils.sensitiveFieldEncrypt(item);
            upperMapper.updateById(item);
            System.out.println("数据写入...");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String realLandRelation() {
        int pageNum = 1;
        int pageSize = 500;
        boolean hasMore = true;

        while (hasMore) {
            //获取真实土地
            PageHelper.startPage(pageNum, pageSize);

            LambdaQueryWrapper<GisLand> landQuer = new LambdaQueryWrapper<>();
            List<GisLand> gisLands =  gisLandRepository.selectList(landQuer);
            PageInfo<GisLand> gisLandPages = new PageInfo<>(gisLands);

            List<GisLand> list = gisLandPages.getList();

            if (CollectionUtils.isEmpty(list)) {
                hasMore = false;
                continue;
            }

            // 处理当前批次数据
            this.realLandMag(gisLands);

            pageNum++;
            PageHelper.clearPage();
        }

        return "执行完成";
    }

    public void realLandMag(List<GisLand> gisLands) {
        //遍历每一个土地
        for (GisLand gisLand : gisLands) {
            String dkbm = gisLand.getDkbm(); //土地编号

            //生成一个真实土地对象
            String realLandId = CommonUtils.getGuid();
            RealLand realLand = new RealLand();
            realLand.setLandNo(gisLand.getDkbm());
            realLand.setLandType("耕地");
            realLand.setContract("是");
            realLand.setArea(gisLand.getScmjm());
            realLand.setFarming("是");
            realLand.setTransfer("否");
            realLand.setNewCreated(0);
            realLand.setRemark("");
            realLand.setUuid(realLandId);
            realLand.setDeleted(0);
            realLand.setCreatorId("数据导入1批次");

            //通过土地编码获取对应的承包户
            Map<String, Object> houseHold = householdMapper.selectHouseHoldByLandNo(dkbm);
            if (MapUtils.isNotEmpty(houseHold)) {
                //生成承包关联对象
                Map<Object, Object> chenbaoInsert = new HashMap<>();
                chenbaoInsert.put("householdId", MapUtils.getString(houseHold, "uuid"));
                chenbaoInsert.put("householder", MapUtils.getString(houseHold, "name"));
                chenbaoInsert.put("realLandId", realLandId);
                realLandRepository.insertChenbaoRelation(chenbaoInsert); //承包关系入库
            }

            realLandRepository.insert(realLand); //土地入库
        }
    }
}

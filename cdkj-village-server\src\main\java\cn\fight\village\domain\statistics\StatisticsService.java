package cn.fight.village.domain.statistics;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.statistics.entity.StatisticsQuery;
import cn.fight.village.domain.statistics.entity.StatisticsResult;
import cn.fight.village.domain.statistics.repository.StatisticsRepository;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class StatisticsService {

    @Resource
    private StatisticsRepository statisticsRepository;

    public JsonResult villageStatistics(StatisticsQuery query) {
        if (StringUtils.isNotBlank(query.getGroup()) && StringUtils.isNotBlank(query.getHouseholder())) {
            throw new BusinessException("查询参数不正确");
        }

        //流转统计数据
        StatisticsResult statisticsResult = statisticsRepository.transStatistics(query);

        //承包统计数据
        StatisticsResult statisticsResult1 = statisticsRepository.contractStatistics(query);

        if (statisticsResult1 != null) {
            BeanUtils.copyProperties(statisticsResult1, statisticsResult);
        }

        return JsonResult.valueOfObject(statisticsResult);
    }

    public JsonResult getVillageTeams() {
        return JsonResult.valueOfObject(statisticsRepository.getVillageTeams());
    }
}

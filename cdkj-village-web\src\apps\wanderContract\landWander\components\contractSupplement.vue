<template>
  <div class="contract-template">
    <iframe src="./contract2.html" ref="contractRef" @load="onLoad"></iframe>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
const emit = defineEmits(["output"]);

const props = defineProps({
  transProtocolSup: {},
});
const contractRef = ref();

watch(
  () => props.transProtocol,
  () => {
    contractRef.value.contentWindow.setValues(props.transProtocolSup);
  }
);

function onLoad() {
  if (contractRef.value) {
    contractRef.value.contentWindow.setValues(props.transProtocolSup);
    if (props.isDetail) {
      contractRef.value.contentWindow.setDetail();
    }
  }
}

/**
 * 下一步
 */
async function submit() {
  return new Promise(async (resolve, reject) => {
    let values = await contractRef.value.contentWindow.getValues();
    if (!values) {
      return Promise.reject();
    }
    emit("output", values, () => {
      resolve();
    });
  });
}

function print(){
  contractRef.value.contentWindow.print()
}

defineExpose({
  submit,
  print
});
</script>

<style lang="scss" scoped>
.contract-template {
  height: calc(100vh - 276px);
  iframe {
    width: 100%;
    height: 100%;
    border: 0;
  }
}
</style>

package cn.fight.village.domain.contract.value;

import cn.fight.village.domain.common.entity.BaseValue;
import cn.fight.village.domain.contract.entity.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同请求对象
 *
 */
public class ContractValue extends BaseValue {
    //类型
    private String type;

    //合同编号
    private String contractNo;

    //备注
    private String remark;

    //所属项目
    private String project;

    //是否完成签署
    private String signed;

    //家庭户ID
    private String householdId;

    //土地列表
    private List<Land> landList = new ArrayList<>();

    //发包方信息
    private List<Upper> upperList = new ArrayList<>();

    //承包方信息
    private List<Under> underList = new ArrayList<>();

    private TransProtocol transProtocol;

    private TransProtocolSup transProtocolSup;

    public String getSigned() {
        return signed;
    }

    public void setSigned(String signed) {
        this.signed = signed;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public TransProtocol getTransProtocol() {
        return transProtocol;
    }

    public void setTransProtocol(TransProtocol transProtocol) {
        this.transProtocol = transProtocol;
    }

    public TransProtocolSup getTransProtocolSup() {
        return transProtocolSup;
    }

    public void setTransProtocolSup(TransProtocolSup transProtocolSup) {
        this.transProtocolSup = transProtocolSup;
    }

    public String getHouseholdId() {
        return householdId;
    }

    public void setHouseholdId(String householdId) {
        this.householdId = householdId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<Land> getLandList() {
        return landList;
    }

    public void setLandList(List<Land> landList) {
        this.landList = landList;
    }

    public List<Upper> getUpperList() {
        return upperList;
    }

    public void setUpperList(List<Upper> upperList) {
        this.upperList = upperList;
    }

    public List<Under> getUnderList() {
        return underList;
    }

    public void setUnderList(List<Under> underList) {
        this.underList = underList;
    }
}

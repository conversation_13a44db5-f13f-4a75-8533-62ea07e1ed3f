package cn.fight.village.domain.land.entity;

import cn.fight.village.domain.common.entity.BaseValue;
import lombok.Data;

/**
 * 地理土地值对象
 */
@Data
public class RealLandValue extends BaseValue {
    private String uuid;
    private String landType;
    private String contract;
    private Double area;
    private String farming;
    private String transfer;
    private String newCreated;
    private String landName;

    //地块代码，关联GIS土地
    public String landNo;

    //真实土地对象
    private RealLand land;

    /**
     * 关联Gis土地
     */
    public GisLand gisLand;

    //承包家庭ID
    private String householdId;

    //承包户户主
    private String householder;
}

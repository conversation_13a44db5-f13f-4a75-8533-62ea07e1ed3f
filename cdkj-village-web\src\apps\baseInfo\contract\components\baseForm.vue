<template>
  <div>
    <landInfo
      ref="landInfoRef"
      :isDetail="isDetail"
      :landData="landDataTable"
      @dataListCallBack="dataListCallBack"
    />
    <funiGroupTitle title="发包方信息"></funiGroupTitle>
    <funiForm v-bind="upperFormConfig" ref="refFormUpper" :col="2" />
    <funiGroupTitle title="承包方信息"></funiGroupTitle>
    <funiForm v-bind="underFormConfig" ref="refFormUnder" :col="2" />
    <ownerInfoModal ref="ownerInfoRef" :isDetail="isDetail" @exportObject="setCollection" />
  </div>
</template>

<script setup lang="jsx">
import { reactive, ref } from 'vue';
import { contractManage, contractInfo } from '@/apps/api/contract.js';
import { useRoute } from 'vue-router';
import landInfo from './landInfo/index.vue';
import person from './person/index.vue';
import ownerInfoModal from '@/apps/baseInfo/homestead/components/ownerInfo/addModal.vue';
import { ElNotification } from 'element-plus';

const props = defineProps({
  isDetail: {}
});
const { id } = useRoute().query;
const landDataTable = ref([]);
const ownerInfoRef = ref();
const personRef =ref()
if (id) {
  contractInfo({ uuid: id }).then(res => {
    refFormUpper.value.setValues(res.upperList[0]);
    refFormUnder.value.setValues(res.underList[0]);
    landDataTable.value = res.landList
    householdId.value = res.householdId
  });
}

const dicEnum = {
  cardType: [
    {
      label: '居民身份证',
      value: '居民身份证'
    },
    {
      label: '军官证',
      value: '军官证'
    },
    {
      label: '行政、企事业单位机构代码证或法人代码证',
      value: '行政、企事业单位机构代码证或法人代码证'
    },
    {
      label: '户口簿',
      value: '户口簿'
    },
    {
      label: '护照',
      value: '护照'
    },
    {
      label: '其他证件',
      value: '其他证件'
    }
  ]
};

const landInfoRef = ref();
const refFormUpper = ref();
const refFormUnder = ref();
const householdId =ref()
/**
 * 发包方
 */
const upperFormConfig = reactive({
  schema: [
    {
      prop: 'upperNo',
      label: '发包方代码',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'upper',
      label: '发包方名称',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'upperName',
      label: '发包方负责人姓名',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'upperPhone',
      label: '发包方负责人联系电话',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'upperIdType',
      label: '发包方证件类型',
      component: !props.isDetail ? 'FuniSelect' : null,
      props: {
        options: dicEnum.cardType
      }
    },
    {
      prop: 'upperIdNo',
      label: '发包方证件号码',
      component: !props.isDetail ? 'el-input' : null,
      hidden: ({ item, formModel }) => {
        return formModel.hasCert;
      },
      props: { placeholder: '请输入' }
    },
    {
      prop: 'remark',
      label: '备注',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    }
  ],
  rules: !props.isDetail
    ? {
        upperNo: [{ required: true, message: '请输入' }],
        upper: [{ required: true, message: '请输入' }],
        upperName: [{ required: true, message: '请输入' }],
        upperPhone: [{ required: true, message: '请输入' }],
        upperIdType: [{ required: true, message: '请输入' }],
        upperIdNo: [{ required: true, message: '请输入' }]
      }
    : undefined
});

/**
 * 承包方
 */
const underFormConfig = reactive({
  schema: [
    {
      prop: 'underNo',
      label: '承包方代码',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'underName',
      label: '承包方（代表）名称',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' },
      on: {
        click: () => {
          ownerInfoRef.value.show();
        }
      }
    },
    {
      prop: 'underIdType',
      label: '承包方（代表）证件类型'
    },
    {
      prop: 'underIdNo',
      label: '承包方（代表）证件号码'
    },
    {
      prop: 'underLocation',
      label: '承包方地址'
    },
    {
      prop: 'underPhone',
      label: '承包方（代表）联系电话'
    },
    {
      prop: 'underMembersList',
      label: '共有人信息',
      colProps: { span: 24 },
      component:( {item ,formModel})=>{
        return (<person isDetail={props.isDetail} ref={(e)=>{personRef.value = e}} householdId={householdId.value}></person>)
      }
    },
    {
      prop: 'contractType',
      label: '承包方式'
    },
    {
      prop: 'usage',
      label: '土地承包用途',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'contractCretNo',
      label: '承包经营权证书号',
      component: !props.isDetail ? 'el-input' : null,
      props: { placeholder: '请输入' }
    },
    {
      prop: 'rightArea',
      label: '确权（合同）总面积（亩）',
      component: !props.isDetail ? 'funi-input-number' : null
    },
    {
      prop: 'remark',
      label: '备注',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    }
  ],
  rules: !props.isDetail
    ? {
        underNo: [{ required: true, message: '请输入' }],
        underName: [{ required: true, message: '请输入' }],
        usage: [{ required: true, message: '请输入' }],
        contractCretNo: [{ required: true, message: '请输入' }],
        rightArea: [{ required: true, message: '请输入' }]
      }
    : undefined
});

const setCollection = e => {
  let row = e.find(x=>x.householder == 1)
  householdId.value =row.householdId
  refFormUnder.value.setValues({
    underName:row.name,
    underIdType:row.idType,
    underIdNo:row.idCode,
    underLocation:row.location,
    underPhone:row.phone,
    contractType:"家庭承包"
  })
};

/**
 * 提交
 */
async function submit() {
  let landData = await landInfoRef.value.getData();
  if (!landData.length) {
    ElNotification({
      title: '请添加地块信息',
      type: 'warning'
    });
    return Promise.reject();
  }
  let resUpper = await refFormUpper.value.validate();
  let resUnder = await refFormUnder.value.validate();
  let arr= personRef.value.getData()
  if (resUpper.isValid && resUnder.isValid) {
    resUnder.values.underMembersList = arr
    //编辑
    if (id) {
      await contractManage({ uuid:id,type:"家庭承包",householdId:householdId.value,landList:landData,contractNo:resUnder.values.contractCretNo,upperList:[resUpper.values], underList:[resUnder.values] });
    }
    //新增
    else {
      await contractManage({ type:"家庭承包",householdId:householdId.value,landList:landData,contractNo:resUnder.values.contractCretNo,upperList:[resUpper.values], underList:[resUnder.values] });
    }
    return Promise.resolve();
  }
  return Promise.reject();
}

defineExpose({
  submit
});
</script>

<style lang="scss" scoped></style>

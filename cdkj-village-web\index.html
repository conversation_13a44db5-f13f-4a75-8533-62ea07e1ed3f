<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>乡村资源智慧管理系统</title>
    <style>
      /* 解决自动填充输入框内容(例如：账号、密码)后，输入框背景色变篮 */
      input:-internal-autofill-previewed,
      input:-internal-autofill-selected {
        -webkit-text-fill-color: var(--el-input-text-color);
        transition: background-color 5000s ease-out 0.5s;
      }

      #loading-mask {
        position: fixed;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background: #fff;
        user-select: none;
        z-index: 9999;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      #loading-mask img {
        width: 196px;
        height: 196px;
      }
    </style>
  </head>

  <body>
    <div id="loading-mask"><img /></div>
    <script type="text/javascript">
      document.querySelector('#loading-mask img').src = `./staticapp/loading.gif`;
    </script>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>

export const apiUrl = {
  queryFamilyInfoList: '/lfpra/familyInfoList/queryFamilyInfoList',
  queryFamilyInfoListExport: '/lfpra/familyInfoList/queryFamilyInfoListExport',
  dictList: '/lfpra/common/dictList',
  familyInfo: '/lfpra/familyInfo/info',
  querySiteInfoList: '/lfpra/siteInfoList/querySiteInfoList',
  queryUnderInfoList: '/lfpra/contractInfoList/queryPatternList',
  // queryForestInfoList: '/lfpra/contractorInfoList/querySwagContractorList',
  queryFileInfoList: '/lfpra/fileInfoList/queryFileInfoList',
  delete: '/lfpra/familyInfo/delete',
  getFiveLevel: '/lfpra/common/getFiveLevel',
  familyInfoListExcel: '/lfpra/contractorInfoList/queryFamilyContractorList',
  newFamilyInfo: '/lfpra/familyInfo/new',
  queryFileInfoListExport: '/lfpra/fileInfoList/queryFileInfoListExport',
  importFamilyInfoListExcel: '/lfpra/familyInfoList/importFamilyInfoListExcel',

};
// 列表
export const queryFamilyInfoList = params => {
  return $http.post(apiUrl.queryFamilyInfoList, params);
};
// 下拉字典
export const dictList = params => {
  return $http.post(apiUrl.dictList, params);
};
// 点击户信息详情
export const familyInfoApi = params => {
  return $http.fetch(apiUrl.familyInfo, params);
};

// 宅基地信息
export const querySiteInfoListApi = params => {
  return $http.post(apiUrl.querySiteInfoList, params);
};
// 承包地信息
export const queryUnderInfoListApi = params => {
  return $http.post(apiUrl.queryUnderInfoList, params);
};

// 电子档案信息
export const queryFileInfoListApi = params => {
  return $http.post(apiUrl.queryFileInfoList, params);
};
// 删除
export const deleteApi = params => {
  return $http.fetch(apiUrl.delete, params);
};

// 五级区划下拉数据
export const getFiveLevelApi = params => {
  return $http.fetch(apiUrl.getFiveLevel, params);
};
// 导出
export const familyInfoListExcelApi = params => {
  return $http.fetch(apiUrl.familyInfoListExcel, params);
};

// 新增
export const newFamilyInfoApi = params => {
  return $http.post(apiUrl.newFamilyInfo, params);
};
// 文件夹下载
export const queryFileInfoListExportApi = params => {
  return $http.post(apiUrl.queryFileInfoListExport, params);
};
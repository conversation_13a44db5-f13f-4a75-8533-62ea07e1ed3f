import router from '@/router/index.js';

export default {
  beforeEachHandler: to => {
    // 路由未携带token和c_id，直接跳转
    if (!(to.query.token && to.query.c_id)) return true;

    const token = sessionStorage.getItem('token');
    const c_id = sessionStorage.getItem('c_id');
    if (to.query.token === token && to.query.c_id === c_id) return false;

    if (to.path !== '/') {
      const newDestination = $utils.clone(to, true);
      delete newDestination.query.token;
      delete newDestination.query.c_id;
      delete newDestination.query.app_n;
      return router.resolve(newDestination);
    }
    return true;
  }
};

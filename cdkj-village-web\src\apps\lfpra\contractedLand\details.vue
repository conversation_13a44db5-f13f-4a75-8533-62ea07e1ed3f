<!-- 详情 -->
<template>
  <div>
    <funi-detail :bizName="bizName" :showHead="true" :auditButtons="buttons" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import BaseDetails from './component/baseDetails.vue';
import WorkInfo from '@/apps/lfpra/common/components/workInfo/index.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
const type = ref(route.query.type);
const id = ref(route.query.id);
const principalSn = ref(route.query.principalSn);
const detail = ref({});

const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    hideStatusBar: false,
    statusName:'家庭承包地数据状态',
    status:'--',
    serialName: '承包经营权证书号',
    hideStatusName: true,
    no: route.query.no || '--'
  };
  return obj;
});

const steps = computed(() => {
  let arr = [
    {
      title: '地块信息',
      preservable: false,
      type: BaseDetails,
      props: {
        id: id.value,
        isEdit: !['info'].includes(route.query.type)
      },
    },
    {
      title: '工作信息',
      type: WorkInfo,
      props: {
        id: principalSn.value
      },
    }
  ];
  return arr;
});
</script>

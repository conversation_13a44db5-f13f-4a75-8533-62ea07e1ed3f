<template>
  <div>
    <funiDialog :destroy-on-close="true" v-model="modalConfig.show" v-bind="modalConfig" :onConfirm="onConfirm">
      <funiForm v-bind="formConfig" ref="refForm" :col="2" />
      <template #footer>
        <span class="dialog-footer" v-if="!isDetail">
          <el-button @click="modalConfig.show = false">取消</el-button>
          <el-button type="primary" :disabled="loading" @click="onConfirm">确定</el-button>
        </span>
        <span v-else>
          <el-button @click="modalConfig.show = false">关闭</el-button>
        </span>
      </template>
    </funiDialog>
  </div>
</template>

<script setup lang="jsx">
import { reactive, ref, nextTick, unref, onMounted, computed, watch } from 'vue';
const emit = defineEmits(['updated', 'addCallBack']);
const props = defineProps({
  isDetail: {}
});

const loading = ref(false);
const refForm = ref(null);
const modalConfig = reactive({
  show: false,
  title: ''
});
let isAdd = true; // 判断是新增还是编辑

/**
 * 自定义下拉
 */
const enumeration = reactive({
  GENDER: [], // 性别
  CARD_TYPE: [], // 证件类型
  RELATION: [], // 与户主关系
  MEM_REMARK: [] // 人员备注
});

const dicEnum = {
  isBoolean: [
    {
      label: '是',
      value: '是'
    },
    {
      label: '否',
      value: '否'
    }
  ],
  sex: [
    {
      label: '男',
      value: '男'
    },
    {
      label: '女',
      value: '女'
    }
  ],
  cardType: [
    {
      label: '居民身份证',
      value: '居民身份证'
    },
    {
      label: '军官证',
      value: '军官证'
    },
    {
      label: '行政、企事业单位机构代码证或法人代码证',
      value: '行政、企事业单位机构代码证或法人代码证'
    },
    {
      label: '户口簿',
      value: '户口簿'
    },
    {
      label: '护照',
      value: '护照'
    },
    {
      label: '其他证件',
      value: '其他证件'
    }
  ],
  relation: [
    {
      label: '户主',
      value: '户主'
    },
    {
      label: '配偶',
      value: '配偶'
    },
    {
      label: '子',
      value: '子'
    },
    {
      label: '女',
      value: '女'
    },
    {
      label: '孙子、孙女或外孙子、外孙女',
      value: '孙子、孙女或外孙子、外孙女'
    },
    {
      label: '父母',
      value: '父母'
    },
    {
      label: '祖父、祖母或外祖父、外祖母',
      value: '祖父、祖母或外祖父、外祖母'
    },
    {
      label: '兄弟姐妹',
      value: '兄弟姐妹'
    },
    {
      label: '其他',
      value: '其他'
    }
  ]
};

onMounted(() => {});

const formConfig = computed(() => {
  let arr = [
    {
      prop: 'name',
      label: '姓名',
      component: 'el-input',
      props: { placeholder: '请输入姓名' }
    },
    {
      prop: 'gender',
      label: '性别',
      component: 'FuniSelect',
      props: {
        options: dicEnum.sex
      }
    },
    {
      prop: 'idType',
      label: '证件类型',
      component: 'FuniSelect',
      props: {
        options: dicEnum.cardType
      }
    },
    {
      prop: 'idCode',
      label: '证件号码',
      component: 'el-input',
      props: { placeholder: '请输入证件号码' }
    },
    {
      prop: 'phone',
      label: '联系方式1',
      component: 'el-input',
      props: { placeholder: '请输入座机或手机号码' }
    },
    {
      prop: 'phoneSec',
      label: '联系方式2',
      component: 'el-input',
      props: { placeholder: '请输入座机或手机号码' }
    },
    {
      prop: 'relation',
      label: '家庭关系',
      component: 'FuniSelect',
      props: {
        options: dicEnum.relation
      }
    },
    {
      prop: 'huko',
      label: '是否户籍人员',
      component: 'FuniSelect',
      props: {
        options: dicEnum.isBoolean
      }
    },
    {
      prop: 'collMember',
      label: '是否集体组织成员',
      component: 'FuniSelect',
      props: {
        options: dicEnum.isBoolean
      }
    },
    {
      prop: 'collGroup',
      label: '组别',
      component: 'el-input',
      props: { placeholder: '请输入组别，如1组' },
      hidden: ({ item, formModel }) => {
        return formModel.collMember == '否';
      }
    },
    {
      prop: 'disabled',
      label: '是否残疾人',
      component: 'FuniSelect',
      props: {
        options: dicEnum.isBoolean
      }
    },
    {
      prop: 'veteran',
      label: '是否退伍军人',
      component: 'FuniSelect',
      props: {
        options: dicEnum.isBoolean
      }
    },
    {
      prop: 'remark',
      label: '人员备注',
      component: 'el-input',
      props: {
        placeholder: '请输入'
      }
    },
    {
      prop: 'remarkTime',
      label: '备注时间',
      component: 'el-date-picker',
      props: { type: 'datetime', valueFormat: 'YYYY-MM-DD hh:mm:ss', style: 'width:100%', placeholder: '请选择' }
    },
    {
      prop: 'remarkReason',
      label: '备注原因',
      component: 'el-input',
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    },
    {
      prop: 'other',
      label: '其它备注',
      component: 'el-input',
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    }
  ];
  if (props.isDetail) {
    arr = arr.map(x => ({
      ...x,
      component: null
    }));
  }
  let detail = [];

  return {
    schema: arr,
    rules: props.isDetail
      ? []
      : {
          name: [{ required: true, message: '请输入', trigger: 'change' }],
          gender: [{ required: true, message: '请选择' }],
          idType: [{ required: true, message: '请选择' }],
          idCode: [{ required: true, message: '请输入证件号码' }],
          phone: [
            {
              validator: (rule, value, callback) => {
                var reg_tel = /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/;
                if (value === '' || value === null || value === undefined || value.length == 0) {
                  callback('请输入座机或手机号码');
                } else if (!reg_tel.test(value)) {
                  callback('号码格式不对');
                } else {
                  callback();
                }
              },
              required: true,
              trigger: 'change'
            }
          ],
          phoneSec: [
            {
              validator: (rule, value, callback) => {
                var reg_tel = /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/;
                if (value === '' || value === null || value === undefined || value.length == 0) {
                  callback('请输入座机或手机号码');
                } else if (!reg_tel.test(value)) {
                  callback('号码格式不对');
                } else {
                  callback();
                }
              },
              required: true,
              trigger: 'change'
            }
          ],
          relation: [{ required: true, message: '请选择' }],
          collMember: [{ required: true, message: '请选择' }],
          huko: [{ required: true, message: '请选择' }],
          veteran: [{ required: true, message: '请选择' }]
        }
  };
});

const show = async item => {
  if (item) {
    isAdd = false;
    modalConfig.title = '编辑人员信息';
  } else {
    isAdd = true;
    modalConfig.title = '新增人员信息';
    item = {};
  }
  if (props.isDetail) {
    modalConfig.title = '查看人员信息';
  }
  modalConfig.show = true;
  nextTick(() => {
    unref(refForm).resetFields();
    unref(refForm).setValues(item);
  });
};
const onConfirm = async () => {
  let res = await refForm.value.validateField();
  if (res.isValid) {
    if (res.values.relation == '户主') {
      res.values.householder = 1;
    } else {
      res.values.householder = 0;
    }
    emit('addCallBack', {
      isAdd,
      data: res.values,
      next: () => {
        modalConfig.show = false;
      }
    });
  }
};
defineExpose({
  show
});
</script>

<style lang="scss" scoped></style>

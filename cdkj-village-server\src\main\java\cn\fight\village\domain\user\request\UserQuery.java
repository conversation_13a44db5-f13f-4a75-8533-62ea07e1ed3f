package cn.fight.village.domain.user.request;

import cn.fight.village.domain.common.entity.BaseQuery;

/**
 * 用户查询
 */
public class UserQuery extends BaseQuery {
    //用户名
    private String username;
    //账号
    private String account;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }
}

import { useAppStore } from '@/stores/useAppStore';
import { watchEffect } from 'vue';

export const useWatermark = () => {
  const appStore = useAppStore();
  const watermarkId = 'watermark-body';
  let watermarkObserver = null;

  /**
   * 创建水印图片url
   */
  const createWaterMark = text => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const offsetX = Math.ceil(ctx.measureText(text).width * 2.5);
    canvas.setAttribute('width', String(offsetX));
    canvas.setAttribute('height', String(offsetX / 2 + 5));
    ctx.rotate(-Math.PI / 6);
    ctx.font = '14px 微软雅黑';
    ctx.fillStyle = 'rgba(128, 128, 128, 0.1)';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, 0, offsetX / 2 + 5);
    const base64Url = canvas.toDataURL('image/png');
    return base64Url;
  };

  const observeWatermark = (targetId, style) => {
    const callback = () => {
      const _globalWatermark = document.querySelector(`#${targetId}`);
      // 当样式或者水印元素dom节点有改动时会重新加载页面
      if ((_globalWatermark && _globalWatermark.getAttribute('style') !== style) || !_globalWatermark) {
        window.location.reload();
      }
    };
    watermarkObserver = new MutationObserver(callback);
    watermarkObserver.observe(document.body, {
      attributes: true,
      subtree: true,
      childList: true
    });
  };

  const removeWatermark = () => {
    // 移除水印观察者
    if (watermarkObserver) {
      watermarkObserver.disconnect();
      watermarkObserver = null;
    }
    // 移除水印
    const target = document.getElementById(watermarkId);
    if (target) {
      document.body.removeChild(target);
    }
  };

  const setWatermark = text => {
    // 移除旧的水印
    removeWatermark();

    // 创建新的水印
    const watermark = document.createElement('div');
    watermark.id = watermarkId;
    const url = createWaterMark(text);
    const style = `position: fixed; top: 0; left: 0; bottom: 0; right: 0; z-index: 999999; background-repeat: repeat; pointer-events: none; background-image: url('${url}')`;
    watermark.setAttribute('style', style);
    document.body.appendChild(watermark);

    observeWatermark(watermarkId, style);
  };

  watchEffect(() => {
    if (appStore.user?.username) {
      console.debug('system name - ', appStore.user);
      const phoneNumber = appStore.user?.phoneNumber?.slice(-4) || '';
      const username = appStore.user?.username || '';
      const now = $utils.toDateString(new Date(), 'yyyy-MM-dd');
      const watermarkText = [username, phoneNumber, now].filter(i => !!i).join(' ');
      setWatermark(watermarkText);
    } else {
      removeWatermark();
    }
  });
};

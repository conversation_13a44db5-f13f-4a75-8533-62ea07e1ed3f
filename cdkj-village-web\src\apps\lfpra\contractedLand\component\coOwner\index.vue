<!-- 共有人信息 -->
<template>
  <div class="owner_info">
      <funi-curd-v2
        :data="tableData"
        :columns="conColumns"
        :pagination="false"
        :loading="loading"
        :stripe="false"
        border
      >
      </funi-curd-v2>
      <el-button style="margin-top: 15px;" v-if="props.isEdit" type="primary" @click="addFanc">新增</el-button>
  </div>
  <ownerTable ref="ownerTableModal" @exportObject="setCollection"/>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref,onMounted, watchEffect } from 'vue';
import { ElNotification } from 'element-plus';
import ownerTable from './ownerTable.vue';
import Hyperlink from '@/apps/lfpra/common/components/hyperlinkTable/index.vue';

const props = defineProps({
  tableData:{type:Array,default:()=>[]},//共有人信息列表数据 回显
  isEdit:{type:Boolean,default:true},//控制新增按钮 查看不显示
  familyId:{type:String,default:''}//通过家庭id查询所有家庭成员
})
const emit = defineEmits(['exData'])
const ownerTableModal = ref(null)
 const tableData = ref([]); // 表格数据
const loading = ref(false)
const conColumns = computed(() => {
  return [
    {
      label: '姓名',
      prop: 'memberName'
    },
    {
      label: '性别',
      prop: 'dicGenderName'
    },
    {
      label: '证件类型',
      prop: 'dicCardTypeName'
    },
    {
      label: '证件号码',
      prop: 'cerCertificateNo'
    },
    {
      label: '家庭关系',
      prop: 'dicRelationName'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      hidden:!props.isEdit,
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {
          DEL: (
            <el-popconfirm
              title="确定移除当前项？"
              width="220"
              onConfirm={() => {
                removeFunc(row,index);
              }}
            >
              {{
                reference: () => <Hyperlink  text={'移除'}></Hyperlink>
              }}
            </el-popconfirm>
          )
        };
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {operationBtn.DEL}
          </div>
        );
      }
    }
  ];
});
watchEffect(()=>{
  // 编辑/查看进入 回显权属信息列表
  console.log(props.tableData,'props.tableData-----');
  if(props.tableData.length > 0){
    tableData.value = props.tableData
  }else{
    tableData.value = []
  }
})

// 打开表格
const addFanc =()=>{
  let list = tableData.value.length>0 ?tableData.value.map(item=>item.id):[]
  ownerTableModal.value.show(props.familyId,list)
}
// 选择后的表格数据
const setCollection = e=>{
  e.forEach(item => {
    tableData.value.push(item)
  });

  emit('exData',unref(tableData.value))
}
// table表格 操作移除
const removeFunc = async (row,index)=>{

  tableData.value.splice(index, 1)

  emit('exData', unref(tableData.value))
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
}
</script>
<style scoped lang="less">
.owner_info{
  width: 100%;
  .owner_info_add{
    width: 100%;
    padding: 30px 0;
    display: flex;
    justify-content: center;
    border: 1px dashed var(--el-color-primary);
    border-radius: 8px;
    cursor: pointer;
    span{
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }
}
</style>

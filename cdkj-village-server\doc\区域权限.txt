create table region (
   id                   SERIAL               not null,
   code                 VARC<PERSON>R(32)          null,
   parent_code          VARCHAR(32)          null,
   name                 VARCHAR(125)         null,
   rate                 integer           null,
   status               integer           null,
   constraint PK_REGION primary key (id)
);

comment on table region is
'区域信息表';

comment on column region.id is
'主键';

comment on column region.code is
'编码';

comment on column region.parent_code is
'父节点';

comment on column region.name is
'名称';

comment on column region.rate is
'等级，0省，1市，2,县，3镇，4村';

comment on column region.status is
'是否有效，1有效，0无效';


create table rlams_user_region (
   id                   SERIAL               not null,
   user_id              VARCHAR(64)          null,
   region_code          VARCHAR(64)          null,
   right_level          integer           null,
   constraint PK_RLAMS_USER_REGION primary key (id)
);

comment on table rlams_user_region is
'人员区域表';

comment on column rlams_user_region.user_id is
'人员id';

comment on column rlams_user_region.region_code is
'区域code';

comment on column rlams_user_region.right_level is
'权限等级 0省，1市，2县，3镇，4村';
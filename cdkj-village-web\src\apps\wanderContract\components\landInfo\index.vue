<template>
  <div>
    <header class="title">
      <FuniGroupTitle title="地块信息" />
      <el-button v-if="!isDetail" type="primary" @click="_add">新增</el-button>
    </header>
    <FuniCurdV2 ref="refFuniListPage" :columns="cardTab.columns" :data="cardTab.dataList" :pagination="false">
    </FuniCurdV2>
    <AddModal ref="addModalRef" :isDetail="isDetail" @addCallBack="addCallBack" />
  </div>
</template>
<script setup lang="jsx">
import { ref, reactive, watch } from 'vue';
import { ElNotification } from 'element-plus';
import AddModal from './addModal.vue';
const props = defineProps({
  landData: {
    type: Array,
    default: []
  },
  isDetail: {}
});
const refFuniListPage = ref();
const addModalRef = ref(null);
const cardTab = reactive({
  dataList: [],
  columns: [
    {
      label: '地块代码',
      prop: 'landNo',
      render: ({ row, index }) => {
        if (props.isDetail) {
          return (
            <el-button type="primary" link onClick={()=>_edit(row,index)}>
              {row.landNo}
            </el-button>
          );
        } else {
          return row.landNo;
        }
      }
    },
    {
      label: '地块面积(亩)',
      prop: 'area'
    },
    { label: '土地类型', prop: 'landType' },
    { label: '种植情况', prop: 'farming' },
    { label: '是否承包', prop: 'contract' },
    { label: '是否流转', prop: 'transfer' },
    {
      label: '操作',
      prop: 'operList',
      align: 'center',
      fixed: 'right',
      hidden: props.isDetail,
      render: ({ row, index }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => {
                _edit(row, index);
              }}
            >
              编辑
            </el-button>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                _delete(row, index);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ]
});

const subscript = ref();

const addCallBack = res => {
  let { isAdd, data, next } = res;
  if (isAdd) {
    if (cardTab.dataList.find(x => x.landNo == data.landNo)) {
      ElNotification({
        title: '已存在此地块编号',
        type: 'warning'
      });
      return;
    }
    cardTab.dataList.push(data);
  } else {
    cardTab.dataList[subscript.value] = data;
  }
  next();
};

const _add = () => {
  addModalRef.value.show();
};
const _edit = (row, index) => {
  subscript.value = index;
  addModalRef.value.show(row);
};
const _delete = (row, index) => {
  cardTab.dataList.splice(index, 1);
};
defineExpose({
  getData: () => cardTab.dataList
});

watch(
  () => props.landData,
  () => {
    if (props.landData.length > 0) {
      cardTab.dataList = [...props.landData];
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

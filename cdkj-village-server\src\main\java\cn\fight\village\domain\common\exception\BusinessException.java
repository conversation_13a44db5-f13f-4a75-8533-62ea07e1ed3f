package cn.fight.village.domain.common.exception;

/**
 * 系统统一业务异常
 */
public class BusinessException extends RuntimeException {
    //异常消息
    private String message;

    //异常编码
    private Integer code = 400  ;

    public BusinessException(Integer code,String message) {
        super(message);
        this.message = message;
        this.code = code;
    }

    public BusinessException(String message) {
        super(message);
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}

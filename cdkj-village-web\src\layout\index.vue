<template>
  <section class="funi-layout">
    <Header
      v-if="!headerHidden"
      :system-name="appStore.systemName"
      :userMenuOption="userMenuOption"
      :pubUser="pubUser"
      :logoPath="platformLogo"
      class="flex-shrink-0"
    ></Header>
    <section class="flex-1 flex overflow-hidden">
      <Aside v-if="!asideHidden && layoutStore.layoutMode === 'vertical'"></Aside>
      <section class="flex-1 flex-col items-stretch overflow-hidden" v-loading="loading" >
        <div class="flex-shrink-0">
          <MultiTab
            v-show="!tabHidden"
            ref="multiTab"
            :asideHidden="asideHidden"
            @remove-tab="handleRemoveTab"
          ></MultiTab>
          <!-- <Breadcrumb v-if="tabHidden && !isDefaultPage" :isDefaultPage="isDefaultPage"></Breadcrumb> -->
        </div>
        <Main ref="main" class="flex-1 overflow-hidden"></Main>
      </section>
    </section>
  </section>
  <Theme ref="theme"></Theme>
</template>

<script setup>
import { unref, ref, computed, provide } from 'vue';
import Aside from './components/Aside/index.vue';
import Header from './components/Header/index.vue';
import MultiTab from './components/MultiTab/index.vue';
import Main from './components/Main/index.vue';
import Breadcrumb from './components/Breadcrumb.vue';
import Theme from './components/theme/index.vue';
import { useAppStore } from '@/stores/useAppStore';
import { useLayoutStore } from './useLayoutStore';
import { useRouter } from 'vue-router';
import useThemeConfigStore from '@/layout/components/theme/hooks/setTheme.js';

const themeConfigStore = useThemeConfigStore();
const props = defineProps({
  headerHidden: Boolean,
  asideHidden: Boolean,
  multiTabHidden: Boolean,
  breadcrumbHidden: Boolean,
  pubUser: Boolean,

  /** 用户中心菜单配置 */
  menuList: { type: Array, default: () => [] },
  /** 用户中心左侧自定义按钮 */
  otherLinks: { type: Array, default: () => [] }
});

const appStore = useAppStore();
const loading = appStore.loading;
const layoutStore = useLayoutStore();
const main = ref();
const multiTab = ref();
const platformLogo = computed(() => appStore.platformConfig.platformLogo);
const router = useRouter();
const theme = ref();

provide('multiTab', multiTab);


const tabHidden = computed(() => {
  if (props.multiTabHidden === true) return true;
  return !themeConfigStore.themeConfig.openTabs;
});

const userMenuOption = computed(() => {
  return {
    menuList: props.menuList,
    otherLinks: props.otherLinks
  };
});

const breadcrumbEnable = computed(() => {
  const route = unref(router.currentRoute);
  if (!!route.meta && $utils.isBoolean(route.meta.breadcrumbHidden)) {
    return !route.meta.breadcrumbHidden;
  }
  return !props.breadcrumbHidden;
});

function handleRemoveTab(tabName) {
  unref(main).removeCache(tabName);
}

/**
 * @description 打开主题配置抽屉
 *
 * **/
 const openThemeConfig = () => {
  unref(theme).show();
};

provide('openThemeConfig', openThemeConfig);
</script>

<style src="./styles/index.scss" lang="scss" scoped></style>
<style src="./styles/root.scss" lang="scss"></style>
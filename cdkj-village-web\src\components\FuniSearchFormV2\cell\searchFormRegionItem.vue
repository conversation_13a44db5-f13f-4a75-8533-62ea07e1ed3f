<template>
  <el-cascader style="width: 100%" placeholder="请输入" :props="cascaderProps" />
</template>

<script setup>
defineOptions({
  name: 'SearchFormRegionItem'
});

const props = defineProps({
  attribute: { type: Object, default: () => ({}) }
});

const cascaderProps = {
  lazy: true,
  value: 'code',
  label: 'name',
  checkStrictly: true,
  lazyLoad(node, resolve) {
    if (node.level === 0) {
      $http
        .post('/csccs/region/findRootRegion')
        .then(res => resolve(res))
        .catch(err => resolve([]));
    } else {
      $http
        .post('/csccs/region/findSonRegionTreeNoRestriction', { businessConfigCode: node.value })
        .then(res => {
          resolve((res || []).map(item => ({ ...item, leaf: node.level === 4 })));
        })
        .catch(err => resolve([]));
    }
  }
};
</script>

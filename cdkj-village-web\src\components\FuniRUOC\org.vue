<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-07-17 17:20:15
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-13 23:53:06
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOC/org.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div
    :style="{
      '--mode-border-radius': mode === 'multiple' ? 'var(--el-checkbox-border-radius)' : '50%'
    }"
  >
    <div style="height: 35px">
      <el-input v-model="orgName" @input="search" placeholder="请搜索管理主体"></el-input>
    </div>
    <div class="orgList" v-loading="loading">
      <el-tree
        ref="tree"
        :data="orgList"
        :props="defaultProps"
        show-checkbox
        node-key="id"
        :expand-on-click-node="false"
        :check-on-click-node="true"
        check-strictly
        @check="nodeClick"
        :filter-node-method="filterNode"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <span style="line-height: 0"> </span>
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed, nextTick, watch } from 'vue';

const orgList = ref([]);
const orgName = ref(void 0);
const timer = ref(null);
const loading = ref(false);
const defaultCheckedKeys = ref([]);
const tree = ref(null);
const allOrgList = ref([]);
const allOrgListTiled = ref([]);
const selectedOrgId = ref([]);
const selectedOrg = ref([]);

const props = defineProps({
  request: {
    type: Object,
    default: () => {
      return {
        api: '/csccs/orgList/orgTree',
        method: 'fetch',
        param: {}
      };
    }
  },
  defaultProps: {
    type: Object,
    default: () => {
      return {
        id: 'id',
        name: 'name',
        children: 'children'
      };
    }
  },
  searchName: {
    type: String,
    default: 'keyword'
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'multiple'
  }
});

const orgProps = computed(() => {
  return Object.assign(
    {
      id: 'id',
      name: 'name',
      children: 'children'
    },
    props.defaultProps
  );
});

const emits = defineEmits(['update:modelValue']);
const defaultProps = computed(() => {
  return {
    children: orgProps.value.children,
    label: orgProps.value.name
  };
});
onMounted(() => {
  getOrgList();
});

const search = async () => {
  await nextTick();
  clearTimeout(timer.value);
  timer.value = setTimeout(() => {
    clearTimeout(timer.value);
    tree.value.filter(orgName.value);
  }, 600);
};
const getOrgList = async () => {
  loading.value = true;
  let { list } = await $http[props.request.method](props.request.api, {
    ...props.request.param
  }).finally(() => {
    loading.value = false;
  });

  list = list.map(item => {
    return {
      ...item,
      checked: false
    };
  });
  orgList.value = list;
  if (orgName.value === '' || orgName.value === void 0 || orgName.value === null) {
    allOrgList.value = list;
    allOrgListTiled.value = getAllNode(list);
  }
  await nextTick();
  setDefaultSelected();
};
const nodeClick = e => {
  let allNode = getAllNode(orgList.value);
  allNode.forEach(item => {
    for (let i = 0; i < selectedOrgId.value.length; i++) {
      if (selectedOrgId.value[i] === item[orgProps.value.id]) {
        selectedOrgId.value.splice(i, 1);
        i--;
      }
    }
  });
  let nodes = [];
  if (props.mode !== 'multiple') {
    nodes = [e[orgProps.value.id]];
    selectedOrgId.value = [];
  } else {
    nodes = tree.value.getCheckedNodes().map(item => {
      return item[orgProps.value.id];
    });
  }

  selectedOrgId.value.push(...nodes);
  emitFunc();
};
const setDefaultSelected = () => {
  defaultCheckedKeys.value = [];
  let allNode = getAllNode(orgList.value);
  allNode.forEach(item => {
    if (selectedOrgId.value.indexOf(item[orgProps.value.id]) > -1) {
      defaultCheckedKeys.value.push(item[orgProps.value.id]);
    }
  });
  tree.value.setCheckedKeys(defaultCheckedKeys.value);
};

const emitFunc = () => {
  let arr = [];
  selectedOrgId.value.forEach(item => {
    for (let i = 0; i < allOrgListTiled.value.length; i++) {
      if (item === allOrgListTiled.value[i][orgProps.value.id]) {
        arr.push({
          id: allOrgListTiled.value[i][orgProps.value.id],
          name: allOrgListTiled.value[i][orgProps.value.name],
          type: 'org'
        });
      }
    }
  });
  emits('update:modelValue', JSON.parse(JSON.stringify(arr)));
};

const getAllNode = org_list => {
  let list = [];
  const recursion = arr => {
    for (let i = 0; i < arr.length; i++) {
      list.push(arr[i]);
      if (arr[i][orgProps.value.children] && arr[i][orgProps.value.children].length > 0) {
        recursion(arr[i][orgProps.value.children]);
      }
    }
  };
  recursion(org_list);
  return list;
};
const setValue = newVal => {
  if (props.mode !== 'multiple') {
    selectedOrgId.value = newVal && newVal.length ? [newVal[newVal.length - 1].id] : [];
  } else {
    selectedOrgId.value = newVal.map(item => item.id);
  }
  setDefaultSelected();
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.orgName.includes(value);
};

watch(
  () => props.modelValue,
  newVal => {
    setValue(newVal);
    setDefaultSelected();
  },
  {
    deep: true
  }
);
defineExpose({
  setValue
});
</script>

<style scoped>
.orgList {
  width: 100%;
  height: calc(var(--tabPaneHeight) - 35px);
  overflow-y: auto;
}

:deep(.el-checkbox) {
  order: 3;
  position: absolute;
  right: 0;
}

.custom-tree-node {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
}

:deep(.el-tree-node__content) {
  position: relative;
}
:deep(.el-checkbox__inner) {
  border-radius: var(--mode-border-radius);
}
</style>

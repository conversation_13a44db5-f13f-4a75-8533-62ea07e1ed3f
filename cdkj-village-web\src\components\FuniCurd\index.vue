<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2022-12-13 14:50:56
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-09-26 14:56:30
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniCurd/index.vue
 * @Description:
 * Copyright (c) 2022 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <funi-teleport to=".layout-content__wrap" :disabled="!teleported">
    <div :class="['funi-curd__wrap', { teleported }]" v-loading="loading">
      <!-- search-form -->
      <div class="funi-curd__search" v-if="isShowSearch">
        <funi-search-form-v3
          v-if="useSearchV2 && env.useSearchV2"
          v-bind="searchConfig"
          @search="handleSearch"
          @reset="() => handleSearch({})"
        >
          <template v-for="(_, slot) in $slots" #[slot]="params">
            <slot :name="slot" v-bind="params || {}" />
          </template>
        </funi-search-form-v3>
        <funi-search-form
          v-else
          v-bind="searchConfig"
          @search="handleSearch"
          :colNumber="props.colNumber"
          @reset="() => handleSearch({})"
        >
          <template v-for="slotName in searchFormSlots" #[slotName]="params">
            <slot :name="slotName" v-bind="params"></slot>
          </template>
        </funi-search-form>
      </div>
      <!-- table -->
      <div :class="['card funi-curd']" style="padding: 0px 8px 10px">
        <!-- 表格头部 操作按钮 -->
        <!-- <FuniActions v-if="fixedButtons" v-bind="actionsProps || {}">
          <div class="funi-curd__fixed-button-group">
            <slot name="buttonGroup"></slot>
          </div>
        </FuniActions> -->
        <div class="funi-curd__header">
          <div v-if="$slots.header" class="header-custom">
            <slot name="header"></slot>
          </div>
          <div class="header-button-group">
            <slot name="buttonGroup"></slot>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="funi-curd__table">
          <el-table
            ref="curdRef"
            v-bind="$attrs"
            border
            :data="visibleData"
            :rowKey="rowKey"
            :stripe="stripe"
            :scrollbar-always-on="scrollbarAlwaysOn"
            table-layout="auto"
            v-draggable="draggableOptions"
            style="height: 100%"
            show-overflow-tooltip
            :cell-style="cellStyle"
            :cell-class-name="cellClassName"
            @row-click="handleRowClick"
            @row-dblclick="handleRowDoubleClick"
          >
            <template #default>
              <el-table-column v-for="column in computedColumns" :key="column.prop" v-bind="column">
                <template v-if="column.slots.header" #header>
                  <slot :name="column.slots.header"></slot>
                </template>
                <template #default="scope">
                  <slot v-if="column.slots.default" :name="column.slots.default" v-bind="scope" />
                  <component v-else-if="column.render" :is="contentRender(column.render, scope)" />
                  <template v-else-if="!['selection', 'index', 'expand'].includes(column.type)">
                    {{ defaultRenderCell(scope) }}
                  </template>
                </template>
              </el-table-column>
            </template>

            <template #empty>
              <slot name="empty"></slot>
            </template>

            <template #append>
              <slot name="append"></slot>
            </template>
          </el-table>
        </div>
        <!-- 分页 -->
        <Pagination
          ref="pageRef"
          class="funi-curd__pagination"
          v-if="pagination"
          style="margin-top: 10px"
          :total="dataTotal"
          :pageSizes="pageSizes"
          @pageChange="doRequest"
        >
          <template #default="params">
            <slot name="pagination_extra" v-bind="params"></slot>
          </template>
        </Pagination>
      </div>
    </div>
  </funi-teleport>
</template>

<script lang="jsx" setup>
import Pagination from './Pagination.vue';
import { computed, isVNode, onActivated, ref, unref, watch } from 'vue';
import env from '@/utils/env';
import { useDraggable } from './useDraggable';

defineOptions({
  name: 'FuniCurd',
  inheritAttrs: false
});

const props = defineProps({
  isShowSearch: { type: Boolean, default: false },
  searchConfig: { type: Object, default: () => ({}) },
  data: { type: Array, default: () => [] },
  lodaData: Function,
  rowKey: { type: [String, Function], default: 'id' },
  columns: { type: Array, default: () => [] },
  pagination: { type: Boolean, default: true },
  defaultPage: { type: Object, default: () => ({ pageSize: 10, pageNo: 1 }) },
  fixedButtons: Boolean,
  pageSizes: Array,
  actionsProps: Object,
  rowSelection: {
    type: [String, Boolean],
    default: 'click',
    validator: val => !val || ['click', 'dblclick'].includes(val)
  },
  loading: Boolean,
  teleported: Boolean,
  /** 是用4.0模式高级查询 */
  useSearchV2: { type: Boolean, default: true },
  colNumber: { type: Number, default: 4 },
  stripe: { type: Boolean, default: false },
  scrollbarAlwaysOn: { type: Boolean, default: true },
  draggable: { type: Boolean, default: false },
  reloadOnActive: Boolean
});

// Emits
const emit = defineEmits([
  'get-curd',
  'beforeRequest',
  'afterRequest',
  'requestError',
  'row-click',
  'row-dblclick',
  'draggableEnd'
]);

const curdRef = ref();
const pageRef = ref();
const tableData = ref([]);
const dataTotal = ref(0);
const visibleData = ref([]);
const searchParams = ref({});
const currentRowKey = ref('');
const currentRow = ref(null);
let skipReloadCount = 1;

const { draggableOptions, draggableColumn } = useDraggable(props, emit);

const computedColumns = computed(() => {
  const columns = props.columns || [];
  if (props.draggable && !columns.some(column => column.prop.includes('draggable_'))) {
    columns.unshift(draggableColumn);
  }
  return columns.map(column => {
    if (column.type === 'selection') return Object.assign({}, column, { width: 40, slots: {} });

    if (column.type === 'radio') {
      return Object.assign({}, column, {
        width: 40,
        slots: {},
        type: 'radio',
        render: ({ row, index }) => (
          <el-radio
            onClick={e => e.preventDefault()}
            modelValue={rowSelected(row)}
            label={true}
            disabled={!rowSelectable(row)}
          >
            {' '}
          </el-radio>
        )
      });
    }

    const style = {};
    column.maxWidth && (style.maxWidth = `${column.maxWidth}px`);

    return Object.assign({ slots: {}, showOverflowTooltip: true, style }, column);
  });
});

const searchFormSlots = computed(() => {
  if (!props.isShowSearch || !props.searchConfig.schema) return [];

  const slotNames = props.searchConfig.schema.map(item => Object.values(item.slots || {})).flat();
  return Array.from(new Set(slotNames));
});

watch(
  () => props.data,
  newData => {
    if (!props.lodaData) {
      reload();
    }
  },
  { deep: true }
);

//watch curdRef
watch(curdRef, (newValue, oldValue) => {
  if (!oldValue && !!newValue) {
    emit('get-curd', unref(curdRef));
    !props.pagination && doRequest(props.defaultPage);
  }
});

onActivated(() => {
  if (skipReloadCount === 0) {
    const { refreshOnActive, resetPage = false } = history.state;
    (props.reloadOnActive || refreshOnActive) && reload({ resetPage });
  }
  skipReloadCount = Math.max(0, skipReloadCount - 1);
});

function contentRender(render, scope) {
  const content = render({ row: unref(scope.row), index: scope.$index });
  return isVNode(content) ? content : <span>{content || '--'}</span>;
}

function defaultRenderCell({ row, column, $index }) {
  const property = column.property;
  const value = property && getProp(row, property).value;
  if (column && column.formatter) {
    return column.formatter(row, column, value, $index) || '--';
  }
  return $utils.isNil(value) ? '--' : value.toString();
}

const getProp = (obj, path, defaultValue) => {
  return {
    get value() {
      return $utils.get(obj, path, defaultValue);
    },
    set value(val) {
      $utils.set(obj, path, val);
    }
  };
};

async function doRequest(page) {
  try {
    emit('beforeRequest');
    let list = props.data || [];
    let total;
    if (!!props.lodaData && $utils.isFunction(props.lodaData)) {
      const remoteData = await props.lodaData({ ...page, pageIndex: page.pageNo }, unref(searchParams));
      list = remoteData.list || [];
      total = remoteData.total;
    }

    tableData.value = list || [];

    if (!props.pagination) {
      visibleData.value = unref(tableData);
    } else if ($utils.isNil(total)) {
      const start = (page.pageNo - 1) * page.pageSize;
      const offset = page.pageSize;
      visibleData.value = unref(tableData).slice(start, start + offset);
      dataTotal.value = unref(tableData).length;
    } else {
      visibleData.value = unref(tableData);
      dataTotal.value = total;
    }
    setCurrentRowByKey(unref(currentRowKey));
    emit('afterRequest', list);
  } catch (error) {
    emit('requestError', error);
    console.error('doRequest - ', error);
  }
}

function reload({ resetPage = true } = {}) {
  if (!props.pagination) {
    doRequest(props.defaultPage);
  } else if (resetPage) {
    resetCurrentRow();
    pageRef.value.resetPageIndex();
  } else {
    doRequest({ pageNo: pageRef.value.currentPage, pageSize: pageRef.value.pageSize });
  }
}

function handleSearch(params) {
  searchParams.value = params;
  reload();
}

function handleRowClick(row, column, event) {
  if (!rowSelectable(row)) return;

  if (props.rowSelection === 'click') {
    toggleSelection(row);
    setCurrentRow(row);
  }
  emit('row-click', { column, row, selection: unref(curdRef).getSelectionRows(), currentRow: currentRow.value });
}

function handleRowDoubleClick(row, column, event) {
  if (!rowSelectable(row)) return;

  if (props.rowSelection === 'dblclick') {
    toggleSelection(row);
    setCurrentRow(row);
  }
  emit('row-dblclick', { column, row, selection: unref(curdRef).getSelectionRows(), currentRow: currentRow.value });
}

function rowSelectable(row) {
  if (!row) return false;

  const selectionColumn = unref(computedColumns).find(column => ['selection', 'radio'].includes(column.type));
  if (!!selectionColumn) {
    const selectable = selectionColumn.selectable;
    if ($utils.isBoolean(selectable)) return selectable;
    if ($utils.isFunction(selectable)) {
      const rowKey = props.rowKey || 'id';
      const rowIndex = unref(visibleData).findIndex(item => item[rowKey] === row[rowKey]);
      return selectable(row, rowIndex);
    }
  }
  return true;
}

function columnSelectable(column) {
  return column.rowClick !== false;
}

function toggleSelection(row) {
  // 复选
  unref(curdRef).toggleRowSelection(row);
}

/**
 * @description: 设置当前行
 */
function setCurrentRow(row) {
  if (!row || !props.rowKey) return;

  currentRowKey.value = row[props.rowKey];
  currentRow.value = row;
}

/**
 * @description: 通过RowKey设置当前行
 */
function setCurrentRowByKey(key) {
  if (!key || !props.rowKey) return;
  setCurrentRow(unref(visibleData).find(item => item[props.rowKey] === key));
}

function rowSelected(row) {
  return !!unref(currentRowKey) && !!row ? unref(currentRowKey) === row[props.rowKey] : false;
}

function resetCurrentRow() {
  currentRowKey.value = '';
  currentRow.value = undefined;
}

function cellStyle({ row, column, rowIndex, columnIndex }) {
  return computedColumns.value.find(item => item.prop === column.property)?.style || {};
}
function cellClassName({ row, column, rowIndex, columnIndex }) {
  return computedColumns.value.find(item => item.prop === column.property)?.width ? '' : 'auto-width-cell';
}

defineExpose({
  ref: curdRef,
  reload,
  doRequest,
  tableData,
  visibleData,
  currentRow,
  resetCurrentRow,
  setCurrentRow,
  setCurrentRowByKey,
  toggleSelection
});
</script>

<style lang="less" scoped>
.funi-curd__fixed-button-group {
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex-wrap: nowrap;
  align-items: center;
}

.funi-curd {
  display: flex;
  flex-direction: column;

  &__header {
    flex-shrink: 0;
    display: flex;
    // margin-bottom: 15px;
    padding-left: 12px;

    .header-custom {
      flex-grow: 1;
    }

    .header-button-group {
      flex-shrink: 0;
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      flex-wrap: nowrap;
    }
  }

  &__table {
    flex-grow: 1;
    overflow-y: auto;

    :deep(.el-popper) {
      max-width: 50vw;
    }
  }

  &__pagination {
    flex-shrink: 0;
  }

  .draggable-handle {
    cursor: grab;
  }
}
</style>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as *;

:deep(.funi-curd__table) {
  .funi-curd-column__hidden,
  .funi-curd-column__hidden > .cell {
    display: none;
  }
  .funi-curd-column__selection_header > .cell {
    display: none;
  }
  @include b(table) {
    @include m(border) {
      &::before {
        width: var(--funi-curd-border-around-width);
      }

      &::after {
        width: var(--funi-curd-border-around-width);
      }

      .#{$namespace}-table__cell {
        border-right: var(--funi-curd-border-right) !important;
        padding: 8px 2px 8px 0;

        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: keep-all;
        }
      }
      .auto-width-cell .cell {
        width: 100% !important;
      }

      .wrap-cell .cell {
        white-space: normal !important;
        word-break: break-all !important;
      }
    }

    th.#{$namespace}-table__cell {
      @include set-css-var-value('table-header-bg-color', rgba(248, 248, 249, 1));
      background-color: getCssVar('table', 'header-bg-color') !important;
    }

    @include e(border-left-patch) {
      width: 0px;
    }
  }
}
</style>
<style lang="scss" scoped>
.funi-curd__wrap {
  display: flex;
  flex-direction: column;

  .funi-curd__search {
    flex-shrink: 0;
  }

  .funi-curd {
    flex-grow: 1;
  }
}

.funi-curd__wrap.teleported {
  height: 100%;
}
</style>

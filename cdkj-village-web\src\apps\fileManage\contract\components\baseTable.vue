<template>
  <div>
    <funiGroupTitle :title="title"></funiGroupTitle>
    <el-row class="table-box">
      <el-col :span="span" class="table-box-title">
        <div v-for="(item, index) in columns" :key="'label' + index">
          {{ item.label }}
        </div>
      </el-col>
      <el-col :span="colSpan" class="table-box-content">
        <div v-for="(item, key) in columns" :key="key">
          {{ tableData[item.prop] || '--' }}
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="jsx">
import { computed, ref, watch,onActivated } from 'vue';
const props = defineProps({
  title: {},
  columns: {},
  data: { type: Array, default: () => [] },
  lodaData: Function,
  span: { type: Number, default: 4 }
});

const emit = defineEmits(['beforeRequest', 'requestError', 'afterRequest']);

const searchParams = ref([]);
const tableData = ref({});
const colSpan = computed(() => 24 - props.span);

const reload = () => {
  doRequest();
};

const doRequest = async () => {
  try {
    emit('beforeRequest');
    let list = props.data || {};
    if (!!props.lodaData && $utils.isFunction(props.lodaData)) {
      // 查询参数
      const queryParams = { ...searchParams.value };
      const remoteData = await props.lodaData(queryParams);
      list = remoteData || {};
    }
    tableData.value = list || {};
    emit('afterRequest', list);
  } catch (error) {
    emit('requestError', error);
    console.error('doRequest - ', error);
  }
};
onActivated(()=>{
  doRequest();
})
doRequest();
watch(
  () => props.data,
  () => !props.lodaData && reload(),
  { deep: true }
);
</script>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: bold;
  color: var(--multi-tab-text-color-active);
  border: 1px solid #e8e8e8;
  border-bottom: none;
  padding: 12px 16px;
  // background-color: #fff5f5;
}
.table-box {
  border: 1px solid #e8e8e8;
  font-size: 13px;
  .table-box-title {
    border-right: 1px solid #e8e8e8;
  }
  .table-box-title,
  .table-box-content {
    & > div {
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>

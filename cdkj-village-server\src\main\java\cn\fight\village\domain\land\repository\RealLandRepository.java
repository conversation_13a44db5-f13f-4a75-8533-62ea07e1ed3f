package cn.fight.village.domain.land.repository;

import cn.fight.village.domain.contract.entity.Land;
import cn.fight.village.domain.land.entity.RealLand;
import cn.fight.village.domain.land.entity.RealLandQuery;
import cn.fight.village.domain.land.entity.RealLandRequest;
import cn.fight.village.domain.land.entity.RealLandValue;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * 地理土地数据层
 */
public interface RealLandRepository extends BaseMapper<RealLand> {
    /**
     * 获取土地序列号
     * @return
     */
    Integer getRealLandNoSec();

    /**
     * 查询地块列表
     * @param query
     * @return
     */
    List<RealLandValue> selectRealLandList(RealLandQuery query);

    /**
     * 根据合同查询土地列表
     * @param uuid
     * @return
     */
    List<Land> selectLandsByProtocol(String uuid);

    /**
     * 插入家庭承包信息
     * @param chenbaoInsert
     * @return
     */
    int insertChenbaoRelation(Map<Object, Object> chenbaoInsert);

    /**
     * 获取土地承包家庭信息
     * @param landId 真实土地ID
     * @return
     */
    Map<String,Object> getChenbaoInfo(String landId);

    /**
     * 根据真实土地ID删除土地承包关系
     * @param landUuid
     * @return
     */
    int deleteChenbaoRel(String landUuid);
}

<template>
  <div class="funi_search_item">
    <el-form
      ref="form"
      :model="formModel"
      :rules="formRules"
      class="demo-ruleForm"
      :inline="true"
    >
      <el-form-item label="" prop="column">
        <el-select
          v-model="formModel.column"
          placeholder="请选择"
          style="width: 134px"
          :teleported="false"
          placement="bottom-start"
        >
          <el-option
            v-for="item in list"
            :key="item.column"
            :label="item.name"
            :value="item.column"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="operate">
        <el-select
          v-model="formModel.operate"
          placeholder="请选择"
          style="width: 134px"
          :teleported="false"
        >
          <el-option
            v-for="item in operateList"
            :key="item.value"
            :label="item.key"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="value">
        <component
          :is="componentObj[currentItem.type] || ElInput"
          v-model="formModel.value"
          style="width: 220px"
          v-bind="currentItem.props || {}"
        ></component>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup>
import { computed, reactive, ref } from 'vue';
import { ElInput, ElSelect } from 'element-plus';
//props
const props = defineProps({
  //下拉框
  list: { type: Array, default: () => [] }
});
//emit
defineExpose({ getFormValue, resetForm });
//data
const form = ref('');
const formModel = reactive({
  column: '',
  operate: '',
  value: ''
});
const formRules = reactive({
  column: [{ validator: check, trigger: 'blur' }],
  operate: [{ validator: check, trigger: 'blur' }],
  value: [{ validator: check, trigger: 'blur' }]
});
const componentObj = ref({
  'el-select': ElSelect,
  'el-input': ElInput
});
//computed
//当前选中列
const currentItem = computed(() => {
  return props.list.find(x => x.column == formModel.column) || {};
});
//操作下拉
const operateList = computed(() => {
  return (
    props.list.find(x => x.column == formModel.column)?.operateOptions || []
  );
});

//function
function check(rule, value, callback) {
  if ((formModel.column || formModel.operate || formModel.value) && !value) {
    callback(new Error());
  } else {
    callback();
  }
}
//查询
function getFormValue() {
  return new Promise((resolve, reject) => {
    form.value.validate((ispass, err) => {
      if (ispass && formModel.column) {
        resolve(formModel);
      } else {
        resolve(undefined);
      }
    });
  });
}
//重置
function resetForm() {
  form.value.resetFields();
}
</script>

<style lang="scss" scoped>
.funi_search_item {
  :deep(.el-form-item) {
    margin-bottom: 0;
  }
}
</style>

package cn.fight.village.domain.statistics.repository;

import cn.fight.village.domain.statistics.entity.StatisticsQuery;
import cn.fight.village.domain.statistics.entity.StatisticsResult;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StatisticsRepository {

    /**
     * 村组统计
     * @param query
     * @return
     */
    StatisticsResult transStatistics(StatisticsQuery query);

    /**
     * 承包统计
     * @param query
     * @return
     */
    StatisticsResult contractStatistics(StatisticsQuery query);

    /**
     * 获取村组
     * @return
     */
    List<String> getVillageTeams();
}

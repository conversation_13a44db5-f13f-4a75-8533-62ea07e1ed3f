export const apiUrl = {
  queryList: '/lfpra/contractInfoList/queryFamilyContractList',
  delete: '/lfpra/contractInfo/deleterContract',
  queryExport:'/lfpra/contractInfoList/queryFamilyContractListExport',
  importSiteInfo:'/lfpra/contractInfo/importFamilyContractList',
  info:'/lfpra/contractInfo/infoContract',
  new:'/lfpra/contractInfo/newContract',
  getFamilyMembers:'/lfpra/familyInfo/getFamilyMembers',//通过成员家庭id获取所有家庭成员 共有人信息
  getOtherUnderinfoPatternIds:'/lfpra/contractInfo/getOtherUnderinfoPatternIds',//其他地块(非当前承包对应的地块)使用的图斑ids

};

// 获取列表
export const queryListHttp = params => {
  return $http.post(apiUrl.queryList, params);
};

// 删除
export const deleteHttp = params => {
  return $http.fetch(apiUrl.delete, params);
};
// 查询详情
export const infoHttp = params => {
  return $http.post(`${apiUrl.info}?contractId=${params.contractId}`);
};
//新增/编辑保存
export const newHttp = params => {
  return $http.post(apiUrl.new, params);
};

//查询家庭所有人
export const getFamilyMembersHttp = params => {
  return $http.fetch(apiUrl.getFamilyMembers,params);
}
//其他地块(非当前承包对应的地块)使用的图斑ids
export const getOtherUnderinfoPatternIdsHttp = params => {
  return $http.fetch(apiUrl.getOtherUnderinfoPatternIds,params);
}


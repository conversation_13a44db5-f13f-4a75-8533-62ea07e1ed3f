package cn.fight.village.domain.contract.api;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.contract.service.RegionService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("region")
public class RegionApi {
    @Resource
    private RegionService regionService;

    /**
     * 获取区域
     * 默认获取所有区域
     *
     * @param regionCode
     * @return
     */
    @GetMapping("query")
    public JsonResult getRegions(String regionCode) {
        return regionService.getRegions(regionCode);
    }
}

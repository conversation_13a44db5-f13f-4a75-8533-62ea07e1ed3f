package cn.fight.village.domain.doc.value;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseValue;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 要件列表查询
 *
 */
public class DocumentListVo extends BaseValue {
    //合同编号
    private String contractNo;
    private String contractId;

    //发包方名称
    private String upper;
    private String upperName;
    @Sensitive
    private String upperIdNo;

    //承包方
    private String under;
    private String  underName;
    @Sensitive
    private String underIdNo;

    //住址
    @Sensitive
    private String location;

    //流水号
    private String docNo;

    //合同路径
    private String path;

    //文件列表
    private List<String> documents;

    public List<String> getDocuments() {
        return documents;
    }

    public void setDocuments(List<String> documents) {
        this.documents = documents;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getUpperName() {
        return upperName;
    }

    public void setUpperName(String upperName) {
        this.upperName = upperName;
    }

    public String getUpperIdNo() {
        return upperIdNo;
    }

    public void setUpperIdNo(String upperIdNo) {
        this.upperIdNo = upperIdNo;
    }

    public String getUnderIdNo() {
        return underIdNo;
    }

    public void setUnderIdNo(String underIdNo) {
        this.underIdNo = underIdNo;
    }

    public String getUnder() {
        return under;
    }

    public void setUnder(String under) {
        this.under = under;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getUpper() {
        return upper;
    }

    public void setUpper(String upper) {
        this.upper = upper;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getUnderName() {
        return underName;
    }

    public void setUnderName(String underName) {
        this.underName = underName;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}

import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import DefineOptions from 'unplugin-vue-define-options/vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { resolve } from 'path';
// 兼容CommonJS模块
import nodeResolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import { mars3dPlugin } from "vite-plugin-mars3d"

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  console.log('env', env);
  return {
    base: "./",
    plugins: [
      nodeResolve(),
      commonjs(),
      vue(),
      vueJsx(),
      DefineOptions(),
      createSvgIconsPlugin({
        // Specify the icon folder to be cached
        iconDirs: [
          resolve(process.cwd(), 'src/components/FuniFormEngine/common/icons/svg'),
          resolve(process.cwd(), 'src/assets/icons')
        ],
        // Specify symbolId format
        symbolId: 'icon-[dir]-[name]'
      }),
      mars3dPlugin()
    ],
    esbuild: {
      // 移除console、debugger
      drop: ['console', 'debugger'],
      // 移除注释
      legalComments: 'none'
    },
    build: {
      outDir: 'output/village-web',
      sourcemap: false,
      // minify: 'terser',
      // terserOptions: {
      //   output: {
      //     comments: false // 去掉注释
      //   },
      //   compress: {
      //     drop_console: true,
      //     drop_debugger: true
      //   }
      // },
      commonjsOptions: {
        exclude: ['lib/vuedraggable/lib/vuedraggable.umd.js,'],
        include: []
      },
      rollupOptions: {
        output: {
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia'],
            tinymce: ['tinymce', '@tinymce/tinymce-vue'],
            office: ['@vue-office/docx', '@vue-office/excel'],
            'element-plus': ['element-plus', '@element-plus/icons-vue']
          }
        }
      }
    },
    server: {
      host: true,
      port: 8000,
      proxy: {
        '/villagetest': {
          target: 'http://**************:8080',
          changeOrigin: true,
          // rewrite: path => path.replace(/^\/api/, ''),
          headers: {
            Connection: 'keep-alive'
          }
        },
        '/documents': {
          target: 'http://**************:8080',
          changeOrigin: true,
          // rewrite: path => path.replace(/^\/api/, ''),
          headers: {
            Connection: 'keep-alive'
          }
        },
        '/geoserver': {
          target: 'http://**************:8081',
          changeOrigin: true,
          headers: {
            Connection: 'keep-alive'
          }
        },
      }
    },
    optimizeDeps: {
      include: ['@/../lib/vuedraggable/lib/vuedraggable.umd.js']
    },
    resolve: {
      // Vite路径别名配置
      alias: { '@': path.resolve('./src') }
    }
  };
});

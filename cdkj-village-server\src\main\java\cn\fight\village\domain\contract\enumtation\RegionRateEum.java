package cn.fight.village.domain.contract.enumtation;

/**
 * 区域等级
 */
public enum RegionRateEum {
    //Province City County Town Village;
    PROVINCE("省",0),
    CITY("市",1),
    COUNTY("县",2),
    TOWN("镇",3),
    VILLAGE("村",4),
    ;

    String name;
    Integer rate;

    RegionRateEum(String name, Integer rate) {
        this.name = name;
        this.rate = rate;
    }

    public Integer getRate() {
        return rate;
    }

    public String getName() {
        return name;
    }
}

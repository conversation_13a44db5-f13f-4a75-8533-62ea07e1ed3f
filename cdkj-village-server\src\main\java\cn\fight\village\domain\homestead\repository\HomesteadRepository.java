package cn.fight.village.domain.homestead.repository;

import cn.fight.village.domain.homestead.entity.Homestead;
import cn.fight.village.domain.homestead.request.HomesteadQuery;
import cn.fight.village.domain.homestead.value.HomesteadListVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 宅基地信息dao
 *
 */
public interface HomesteadRepository extends BaseMapper<Homestead> {

    /**
     * 宅基地信息分页查询
     *
     * @param query
     * @return
     */
    List<HomesteadListVo> pageQuery(HomesteadQuery query);
}

<template>
  <div class="page-box">
    <div class="detail-info-box" :class="{ show: isShowDetail }">
      <funiForm v-bind="formConfigDetail" ref="formConfigDetailRef" :col="1" />
    </div>
    <div class="statistic-box">
      <statistic :mapRef="funiOlMapRef"></statistic>
    </div>
    <div class="map-box">
      <funiOlMap
        ref="funiOlMapRef"
        :layers="layers"
        :showDraw="true"
        :drawTool="['add', 'del']"
        geomDataType="wkt"
        :legendListFunc="legendListFunc"
        @layerClick="layerClick"
        @drawend="drawend"
      ></funiOlMap>
    </div>

    <!-- 地块新增弹框 -->
    <funi-dialog v-model="addLandDialogVisible" title="新增地块" size="large">
      <funiForm v-bind="formConfig" ref="formConfigRef" :col="2">
        <template #householder="{ item, formModel }">
          <el-input disabled :value="formModel.householder">
            <template #append>
              <el-button type="primary" @click="showHouseHolderModal">选择</el-button>
            </template>
          </el-input>
        </template>
      </funiForm>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancelAdd">取消</el-button>
          <el-button type="primary" @click="handleConfirmAdd"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
    <householderModal ref="householderModalRef" @exportObject="getHouseHolder"></householderModal>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { add, del, info } from "@/apps/api/land.js";
import householderModal from "@/apps/wanderContract/components/householder/index.vue";
import statistic from "../components/statistic/index.vue";

const formConfigDetailRef = ref();
const formConfigRef = ref();
const funiOlMapRef = ref();
const householderModalRef = ref();
const isShowDetail = ref(false);

const layers = ref([
  {
    id: "layer",
    name: "图层管理",
    children: [
      {
        id: "img_ygc",
        name: "航拍图",
        type: "tile",
        url: "/geoserver/village/gwc/service/wmts",
        layer: "village:img_ygc",
        matrixSet: "EPSG:4326",
        layerParams:{
          zIndex:0,
        }
      },
      {
        id: "lyr_dk",
        name: "流转图层",
        type: "wms",
        isDrawLayer: true,
        checked: true,
        showInTree: false,
        url: "/geoserver/village/wms", //http://118.24.186.160:8081/geoserver/village/wms
        layer: "village:lyr_dk_test",
        layerParams:{
          zIndex:2,
        }
      },
      {
        id: "lyr_cbd",
        name: "承包地",
        type: "wms",
        url: "/geoserver/village/wms",
        layer: "village:lyr_cbd_test",
        layerParams:{
          zIndex:1,
        }
      },
      {
        id: "lyr_fgtd",
        name: "复耕土地",
        type: "wms",
        url: "/geoserver/village/wms",
        layer: "village:lyr_fgtd_test",
        layerParams:{
          zIndex:3,
        }
      },
      {
        id: "lyr_fgtd",
        name: "坑塘水面",
        type: "wms",
        url: "/geoserver/village/wms",
        layer: "village:lyr_ktsm_test",
        layerParams:{
          zIndex:3,
        }
      },
      {
        id: "lyr_zd",
        name: "宗地",
        type: "wms",
        url: "/geoserver/village/wms",
        layer: "village:lyr_zd_test",
        layerParams:{
          zIndex:3,
        }
      },
    ],
  },
]);
const dicEnum = {
  isBoolean: [
    {
      label: "是",
      value: "是",
    },
    {
      label: "否",
      value: "否",
    },
  ],
  landType: [
    {
      label: "宅基地",
      value: "宅基地",
    },
    {
      label: "耕地",
      value: "耕地",
    },
    {
      label: "水域",
      value: "水域",
    },
    {
      label: "其他",
      value: "其他",
    },
  ],
};

const formConfig = reactive({
  schema: [
    {
      prop: "dkmc",
      label: "土地名称",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "landType",
      label: "土地类型",
      component: "FuniSelect",
      props: {
        options: dicEnum.landType,
      },
    },
    {
      prop: "householder",
      label: "户主",
      slots: {
        default: "householder",
      },
    },
    {
      prop: "contract",
      label: "是否承包",
      component: "FuniSelect",
      props: {
        options: dicEnum.isBoolean,
      },
    },
    {
      prop: "farming",
      label: "是否种植",
      component: "FuniSelect",
      props: {
        options: dicEnum.isBoolean,
      },
    },
    // {
    //   prop: "transfer",
    //   label: "是否流转",
    //   component: "FuniSelect",
    //   props: {
    //     options: dicEnum.isBoolean,
    //   },
    // },
    {
      prop: "dkdz",
      label: "东至",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "dkxz",
      label: "西至",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "dknz",
      label: "南至",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "dkbz",
      label: "北至",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "scmj",
      label: "实测面积(m²)",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "scmjm",
      label: "实测面积(亩)",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "sczb",
      label: "所属组别",
      component: "el-input",
      props: { placeholder: "请输入" },
    },
    {
      prop: "remark",
      label: "备注",
      component: "el-input",
      colProps: { span: 24 },
      props: { type: "textarea", placeholder: "请输入" },
    },
  ],
  rules: {
    dkmc: [{ required: true, message: "请输入" }],
    landType: [{ required: true, message: "请输入" }],
    householder: [{ required: true, message: "请输入" }],
    contract: [{ required: true, message: "请输入" }],
    farming: [{ required: true, message: "请输入" }],
    area: [{ required: true, message: "请输入" }],
    transfer: [{ required: true, message: "请输入" }],
    dkdz: [{ required: true, message: "请输入" }],
    dkxz: [{ required: true, message: "请输入" }],
    dknz: [{ required: true, message: "请输入" }],
    dkbz: [{ required: true, message: "请输入" }],
    scmj: [{ required: true, message: "请输入" }],
    sczb: [{ required: true, message: "请输入" }],
    scmjm: [{ required: true, message: "请输入" }],
  },
});

const formConfigDetail = computed(() => {
  return {
    schema: formConfig.schema.map((x) => ({ label: x.label, prop: x.prop })),
  };
});

function showHouseHolderModal() {
  householderModalRef.value.show();
}

function getHouseHolder(arr) {
  formConfigRef.value.setValues({
    householdId: arr[0].uuid,
    householder: arr[0].householder,
  });
}
// 弹框控制
const addLandDialogVisible = ref(false);

function drawend(data) {
  if (!data) {
    isShowDetail.value = false;
    funiOlMapRef.value.closeMessage(false);
  } else if (data.type == "VECTOR_ADD") {
    addLandDialogVisible.value = true;
    nextTick(() => {
      formConfigRef.value.setValues({
        scmj: data.farlandInfoRequests[0].farlandAcreage,
        scmjm: data.farlandInfoRequests[0].farlandAcreage / 666.67,
        geomStr: data.farlandInfoRequests[0].geom,
      });
    });
  } else if (data.type == "VECTOR_DELETE") {
    ElMessageBox.confirm("确定删除此地块？", "删除提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      del({ landNo: data.oldFeatures[0].dkbm, sure: 1 }).then((res) => {
        ElMessage.success("地块删除成功");
        funiOlMapRef.value.closeMessage(false);
        let drawLayer = funiOlMapRef.value.getLayersInstance().filter((x) => x.layer.isDrawLayer == true);
        drawLayer.forEach((item) => {
          item.layerInstance.getSource().refresh();
        });
      });
    });
  }
}
// 确认新增
const handleConfirmAdd = async () => {
  // 表单验证
  let res = await formConfigRef.value.validate();
  if (!res.isValid) return;
  let values = res.values;
  await add({
    householdId: values.householdId,
    householder: values.householder,
    land: {
      landType: values.landType,
      contract: values.contract,
      farming: values.farming,
      transfer: values.transfer,
      area: values.scmjm,
      remark: values.remark,
    },
    gisLand: {
      dkmc: values.dkmc,
      dkdz: values.dkdz,
      dkxz: values.dkxz,
      dknz: values.dknz,
      dkbz: values.dkbz,
      scmj: values.scmj,
      scmjm: values.scmjm,
      remark: values.remark,
      geomStr: values.geomStr,
    },
  });
  addLandDialogVisible.value = false;
  funiOlMapRef.value.closeMessage(false);
  let drawLayer = funiOlMapRef.value.getLayersInstance().filter((x) => x.layer.isDrawLayer == true);
  drawLayer.forEach((item) => {
    item.layerInstance.getSource().refresh();
  });
  ElMessage.success("地块新增成功！");
};

// 取消新增
const handleCancelAdd = () => {
  addLandDialogVisible.value = false;
};

function legendListFunc(legendList, layersInstance) {
  return [];
}

function layerClick(data) {
  isShowDetail.value = true;
  info({ landNo: data.dkbm }, { loading: true }).then((res) => {
    formConfigDetailRef.value.setValues({
      ...res,
      ...res.land,
      ...res.gisLand,
    });
  });
}
</script>

<style lang="scss" scoped>
.page-box {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  position: relative;
  .btn-box {
    padding: 10px;
  }
  .map-box {
    flex: 1;
  }
  .detail-info-box {
    position: absolute;
    right: 10px;
    background: #fff;
    z-index: 1;
    width: 300px;
    box-shadow: 0 0 4px #989898;
    padding: 0 10px;
    top: 70px;
    transition: all 0.3s;
    transform: translateX(110%);
  }
  .statistic-box{
    position: absolute;
    z-index: 1;
    top:10px;
    left:100px;
  }
  .show {
    transform: translateX(0);
  }
}
</style>

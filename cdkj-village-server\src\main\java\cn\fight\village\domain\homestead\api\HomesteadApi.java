package cn.fight.village.domain.homestead.api;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.homestead.request.HomesteadQuery;
import cn.fight.village.domain.homestead.request.HomesteadRequest;
import cn.fight.village.domain.homestead.service.HomesteadService;
import cn.fight.village.domain.user.entity.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 宅基地信息接口层
 *
 */
@Controller
@RequestMapping("homestead")
public class HomesteadApi {

    @Resource
    private HomesteadService homesteadService;

    /**
     * 添加宅基地信息
     *
     */
    @ResponseBody
    @PostMapping("add")
    public JsonResult addHomestead(@RequestBody HomesteadRequest homesteadRequest, @UserInfo User user) {
        return homesteadService.addHomesteadInfo(homesteadRequest,user);
    }

    /**
     * 宅基地信息列表查询
     *
     * @param query
     * @return
     */
    @ResponseBody
    @PostMapping("list")
    public JsonResult listQuery(@RequestBody HomesteadQuery query) {
        return homesteadService.listQuery(query);
    }

    /**
     * 获取宅基地信息
     *
     * @param uuid
     * @return
     */
    @ResponseBody
    @GetMapping("info")
    public JsonResult info(String uuid) {
        CommonUtils.notNull(uuid,"uuid不能为空");
        return homesteadService.infoQuery(uuid);
    }

    /**
     * 删除宅基地信息
     *
     * @param request
     * @param user
     * @return
     */
    @ResponseBody
    @PostMapping("remove")
    public JsonResult remove(@RequestBody HomesteadRequest request,@UserInfo User user) {
        return homesteadService.remove(request,user);
    }

    /**
     * 修改宅基地信息
     *
     * @param request
     * @param user
     * @return
     */
    @ResponseBody
    @PostMapping("update")
    public JsonResult update(@RequestBody HomesteadRequest request,@UserInfo User user) {
        return homesteadService.update(request,user);
    }

    /**
     * 根据户ID-获取宅基地信息
     *
     * @param houseHoldId
     * @return
     */
    @ResponseBody
    @GetMapping("queryByHousehold")
    public JsonResult queryByHouseHold(String houseHoldId) {
        return homesteadService.queryByHouseHold(houseHoldId);
    }
}

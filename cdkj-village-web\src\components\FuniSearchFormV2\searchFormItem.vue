<template>
  <div class="search-form-item-container">
    <el-form-item :prop="prop.column" style="margin-right: 16px">
      <el-select
        placeholder="请先选择筛选条件"
        :model-value="model.column"
        style="width: 160px"
        @change="handleColumnChange"
      >
        <el-option v-for="item in queryFields" :key="item.column" :label="item.name" :value="item.column" />
      </el-select>
    </el-form-item>
    <el-form-item :prop="prop.operate" style="margin-right: 16px">
      <el-select :model-value="model.operate" style="width: 100px" @change="handleOperateChange">
        <el-option v-for="item in operateOptions" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </el-form-item>
    <el-form-item
      ref="valueItem"
      :prop="prop.value"
      :rules="{ required: !!model.column, trigger: 'blur', validator: genValidator({ model, fieldItem }) }"
      style="margin-right: 16px; flex-grow: 1; min-width: 240px"
    >
      <component v-if="!!fieldItem" :is="getRender({ model, fieldItem })" />
      <el-input v-else style="width: 100%" placeholder="请输入"></el-input>
    </el-form-item>
    <el-form-item style="width: 116px">
      <el-button link type="primary" @click="emit('newField')">
        <funi-icon icon="ep:circle-plus" width="16px" />
        <span>&nbsp;更多筛选</span>
      </el-button>
      <el-button v-show="!!modelValue.key && modelValue.key !== 1" link type="primary" @click="emit('deleteField')">
        <funi-icon icon="ep:remove" width="16px" />
      </el-button>
    </el-form-item>
  </div>
</template>
<script setup>
import { computed, ref, watch } from 'vue';

import { cellRender } from './cell-render.jsx';

const emit = defineEmits(['update:modelValue', 'newField', 'deleteField']);

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  queryFields: {
    type: Array,
    required: true,
    default: () => []
  },
  prop: {
    type: Object,
    required: true,
    default: () => ({ column: 'column', operate: 'operate', value: 'value' })
  }
});

const model = ref({ column: '', operate: '', value: '' });
const valueItem = ref();

const operateOptions = computed(() => {
  if (!!fieldItem.value) {
    return fieldItem.value.operateOptions.reduce((acc, cur) => {
      const has = acc.find(item => item.value === cur.value);
      if (!has) {
        acc.push(cur);
      }
      return acc;
    }, []);
  }
  return [];
});

const fieldItem = computed(() => {
  const item = props.queryFields.find(item => item.column === model.value.column);
  if (!!item && item.type === 'NUMBER' && model.value.operate === 'BETWEEN') {
    item.type = 'NUMBER_RANGE';
  }
  return item;
});

watch(
  () => props.modelValue,
  val => (model.value = val),
  { immediate: true, deep: true }
);

const getRender = ({ model, fieldItem }) => {
  let renderName = '';
  if (fieldItem.type.toUpperCase() === 'SLOT') {
    renderName = fieldItem.attribute?.key || fieldItem.column;
  } else {
    renderName = fieldItem.type.toUpperCase();
  }
  const render = cellRender.get(renderName);
  return !!render ? render({ model, fieldItem }) : '';
};

const handleColumnChange = val => {
  model.value.column = val;
  model.value.operate = operateOptions.value[0].value;
  valueItem.value.resetField();
  emit('update:modelValue', model.value);
};

const handleOperateChange = val => {
  model.value.operate = val;
  emit('update:modelValue', model.value);
};

const genValidator = ({ model, fieldItem }) => {
  return (rule, value, callback) => {
    if (!fieldItem) {
      callback();
      return;
    }

    if ($utils.isNaN(value) || $utils.isUndefined(value) || $utils.isNull(value) || $utils.isEmpty(value)) {
      callback(new Error('不能为空'));
      return;
    }

    if (fieldItem.type.toUpperCase() === 'NUMBER_RANGE') {
      value.filter(i => $utils.isNumber(i)).length === 2 ? callback() : callback(new Error('不能为空'));
    }
    callback();
  };
};
</script>

<style lang="scss" scoped>
.search-form-item-container {
  display: flex;
  flex-direction: row;
}
</style>

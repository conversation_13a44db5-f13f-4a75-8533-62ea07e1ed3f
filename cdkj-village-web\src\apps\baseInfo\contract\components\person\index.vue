<template>
  <div v-if="householdId">
    <funi-curd-v2
      :data="cardTab.dataList"
      :columns="cardTab.columns"
      :pagination="false"
      :loading="loading"
      :stripe="false"
    >
    </funi-curd-v2>
    <el-button v-if="!isDetail" type="primary" @click="_add" style="margin-top: 12px;">新增</el-button>
    <AddModal ref="addModalRef" :isDetail="isDetail" @exportObject="setCollection" />
  </div>
  <div v-else>--</div>
</template>
<script setup lang="jsx">
import { ref, reactive, watch } from 'vue';
import { ElNotification } from 'element-plus';
import AddModal from './addModal.vue';
const props = defineProps({
  modelValue: {
    type: Array,
    default: []
  },
  householdId:{},
  isDetail: {}
});
const addModalRef = ref(null);
const cardTab = reactive({
  dataList: [],
  columns: [
      {
        label: '姓名',
        prop: 'name'
      },
      {
        label: '性别',
        prop: 'gender'
      },
      {
        label: '证件类型',
        prop: 'idType'
      },
      {
        label: '证件号码',
        prop: 'idCode'
      },
      {
        label: '家庭关系',
        prop: 'relation'
      },
    {
      label: '操作',
      prop: 'operList',
      align: 'center',
      fixed: 'right',
      width: 120,
      hidden: props.isDetail,
      render: ({ row, index }) => {
        return (
          <div>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                _delete(row, index);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ]
});

const setCollection = e => {
  cardTab.dataList = e;
};

const _add = () => {
  addModalRef.value.show(props.householdId,cardTab.dataList);
};

const _delete = (row, index) => {
  cardTab.dataList.splice(index, 1);
};
defineExpose({
  getData: () => cardTab.dataList
});

watch(
  () => props.modelValue,
  () => {
    if (props.modelValue.length > 0) {
      cardTab.dataList = props.modelValue;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.owner_info {
  width: 100%;
  .owner_info_add {
    width: 100%;
    padding: 30px 0;
    display: flex;
    justify-content: center;
    border: 1px dashed var(--el-color-primary);
    border-radius: 8px;
    cursor: pointer;
    span {
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }
}
</style>

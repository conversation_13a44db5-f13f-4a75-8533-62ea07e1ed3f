package cn.fight.village.domain.household.query;

import cn.fight.village.domain.common.entity.BaseQuery;

/**
 * 户信息列表查询对象
 *
 */
public class HouseholdListQuery extends BaseQuery {
    private String name;

    private String householdCode;

    private String poor;

    private String lower;

    private String idCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHouseholdCode() {
        return householdCode;
    }

    public void setHouseholdCode(String householdCode) {
        this.householdCode = householdCode;
    }

    public String getPoor() {
        return poor;
    }

    public void setPoor(String poor) {
        this.poor = poor;
    }

    public String getLower() {
        return lower;
    }

    public void setLower(String lower) {
        this.lower = lower;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }
}

package cn.fight.village.domain.contract.entity;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.ArrayList;
import java.util.List;

/**
 * 土地流转协议内容
 */
@TableName("public.rlams_land_protocol")
public class TransProtocol extends BaseEntity {
    /**合同id**/
    private String contractId;
    
    /**甲方**/
    @TableField("party_a")
    private String partyA;
    
    /**甲方身份证号**/
    @Sensitive
    @TableField("a_id")
    private String aId;
    
    /**联系地址**/
    @TableField("a_location")
    @Sensitive
    private String aLocation;
    
    /**联系电话**/
    @TableField("a_phone")
    @Sensitive
    private String aPhone;
    
    /**经营主体类型（1-自然人，2-农村承包经营户，3-农民专业合作社，4-家庭农场，-5-农村集体经济组织，6-公司，7-其它））*/
    @TableField("a_type")
    private String aType;
    
    /**其他类型**/
    @TableField("a_type_other")
    private String aTypeOther;

    /**乙方**/
    @TableField("party_b")
    private String partyB;

    /**社会信用代码**/
    @Sensitive
    @TableField("b_id")
    private String bId;

    /**代表人**/
    @TableField("b_represe")
    private String bReprese;

    /**代表人身份证号**/
    @Sensitive
    @TableField("b_rep_id")
    private String bRepId;

    /**联系地址**/
    @Sensitive
    @TableField("b_location")
    private String bLocation;

    /**联系电话**/
    @Sensitive
    @TableField("b_phone")
    private String bPhone;

    /**经营主体类型（1-自然人，2-农村承包经营户，3-农民专业合作社，4-家庭农场，-5-农村集体经济组织，6-公司，7-其它））*/
    @TableField("b_type")
    private String bType;

    /**其他类型说明**/
    @TableField("b_type_other")
    private String bTypeOther;

    /**合同总面积**/
    private Double totalArea;

    /**附属建筑或资产描述**/
    private String subject;

    /**附属建筑或资产处置方式*/
    private String subjectDeal;

    /**青苗补偿内容*/
    private String cropsPay;

    /**用途**/
    private String usage;

    /**租赁开始时间**/
    private String startTime;

    /**租赁结束时间**/
    private String endTime;

    /**交付时间**/
    private String deliverTime;

    /**租金标准**/
    private String rent;

    /**租金大写**/
    private String rentChn;

    /**租金调整方案**/
    private String rentUpdate;

    /**支付方式（1前，2后）**/
    private String rentPay;

    /**甲方账号名**/
    @Sensitive
    @TableField("a_account_name")
    private String aAccountName;

    /**甲方账号**/
    @Sensitive
    @TableField("a_account_code")
    private String aAccountCode;

    /**开户行**/
    @TableField("a_account_bank")
    private String aAccountBank;

    /**甲方签订时间**/
    @TableField("a_sign_time")
    private String aSignTime;

    /**其他内容（json格式字符串）**/
    private String textObject;

    @TableField(exist = false)
    private List<TransProtocolLand> lands = new ArrayList<>();

    public String getSubjectDeal() {
        return subjectDeal;
    }

    public void setSubjectDeal(String subjectDeal) {
        this.subjectDeal = subjectDeal;
    }

    public String getCropsPay() {
        return cropsPay;
    }

    public void setCropsPay(String cropsPay) {
        this.cropsPay = cropsPay;
    }

    public String getPartyA() {
        return partyA;
    }

    public void setPartyA(String partyA) {
        this.partyA = partyA;
    }

    public String getaId() {
        return aId;
    }

    public void setaId(String aId) {
        this.aId = aId;
    }

    public String getaLocation() {
        return aLocation;
    }

    public void setaLocation(String aLocation) {
        this.aLocation = aLocation;
    }

    public String getaPhone() {
        return aPhone;
    }

    public void setaPhone(String aPhone) {
        this.aPhone = aPhone;
    }

    public String getaType() {
        return aType;
    }

    public void setaType(String aType) {
        this.aType = aType;
    }

    public String getaTypeOther() {
        return aTypeOther;
    }

    public void setaTypeOther(String aTypeOther) {
        this.aTypeOther = aTypeOther;
    }

    public String getbReprese() {
        return bReprese;
    }

    public void setbReprese(String bReprese) {
        this.bReprese = bReprese;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getPartyB() {
        return partyB;
    }

    public void setPartyB(String partyB) {
        this.partyB = partyB;
    }

    public String getbId() {
        return bId;
    }

    public void setbId(String bId) {
        this.bId = bId;
    }

    public String getbRepId() {
        return bRepId;
    }

    public void setbRepId(String bRepId) {
        this.bRepId = bRepId;
    }

    public String getbLocation() {
        return bLocation;
    }

    public void setbLocation(String bLocation) {
        this.bLocation = bLocation;
    }

    public String getbPhone() {
        return bPhone;
    }

    public void setbPhone(String bPhone) {
        this.bPhone = bPhone;
    }

    public String getbType() {
        return bType;
    }

    public void setbType(String bType) {
        this.bType = bType;
    }

    public String getbTypeOther() {
        return bTypeOther;
    }

    public void setbTypeOther(String bTypeOther) {
        this.bTypeOther = bTypeOther;
    }

    public Double getTotalArea() {
        return totalArea;
    }

    public void setTotalArea(Double totalArea) {
        this.totalArea = totalArea;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDeliverTime() {
        return deliverTime;
    }

    public void setDeliverTime(String deliverTime) {
        this.deliverTime = deliverTime;
    }

    public String getRent() {
        return rent;
    }

    public void setRent(String rent) {
        this.rent = rent;
    }

    public String getRentChn() {
        return rentChn;
    }

    public void setRentChn(String rentChn) {
        this.rentChn = rentChn;
    }

    public String getRentUpdate() {
        return rentUpdate;
    }

    public void setRentUpdate(String rentUpdate) {
        this.rentUpdate = rentUpdate;
    }

    public String getRentPay() {
        return rentPay;
    }

    public void setRentPay(String rentPay) {
        this.rentPay = rentPay;
    }

    public String getaAccountName() {
        return aAccountName;
    }

    public void setaAccountName(String aAccountName) {
        this.aAccountName = aAccountName;
    }

    public String getaAccountCode() {
        return aAccountCode;
    }

    public void setaAccountCode(String aAccountCode) {
        this.aAccountCode = aAccountCode;
    }

    public String getaAccountBank() {
        return aAccountBank;
    }

    public void setaAccountBank(String aAccountBank) {
        this.aAccountBank = aAccountBank;
    }

    public String getaSignTime() {
        return aSignTime;
    }

    public void setaSignTime(String aSignTime) {
        this.aSignTime = aSignTime;
    }

    public String getTextObject() {
        return textObject;
    }

    public void setTextObject(String textObject) {
        this.textObject = textObject;
    }

    public List<TransProtocolLand> getLands() {
        return lands;
    }

    public void setLands(List<TransProtocolLand> lands) {
        this.lands = lands;
    }
}

{"name": "funi-pass-cs-web-cli", "version": "0.0.0", "scripts": {"dev": "vite", "build": "node --max_old_space_size=8196 ./node_modules/vite/bin/vite.js build"}, "type": "module", "dependencies": {"@element-plus/icons-vue": "2.3.1", "@tinymce/tinymce-vue": "^4.0.5", "@turf/turf": "^7.2.0", "@vue-office/docx": "^1.3.1", "@vue-office/excel": "^1.4.5", "@vue/compiler-sfc": "3.2.47", "@vue/repl": "1.3.2", "@vueup/vue-quill": "1.1.1", "@vueuse/core": "^9.6.0", "ace-builds": "1.18.0", "animate.css": "^4.1.1", "axios": "^1.8.1", "clipboard": "2.0.11", "core-js": "3.6.5", "element-plus": "2.8.7", "fast-glob": "3.2.12", "file-saver": "2.0.5", "html2canvas": "^1.4.1", "js-base64": "3.7.5", "lodash-es": "^4.17.21", "lz-string": "^1.5.0", "mars3d": "^3.9.1", "mars3d-cesium": "^1.127.0", "mitt": "3.0.0", "ol": "8.2", "pinia": "^2.0.27", "pinia-plugin-persist": "^1.0.0", "proj4": "^2.15.0", "sass": "~1.79.0", "screenfull": "^6.0.2", "sm-crypto": "^0.3.13", "snowflake-id": "^1.1.0", "sortablejs": "1.14.0", "terraformer-wkt-parser": "^1.2.1", "theme-colors": "^0.1.0", "tinymce": "^5.10.2", "vditor": "^3.9.0", "viewerjs": "^1.11.7", "vue": "3.5.12", "vue-color-kit": "^1.0.6", "vue-i18n": "^9.2.2", "vue-request": "^2.0.0-rc.4", "vue-router": "^4.2.2", "vxe-table": "^4.4.6", "vxe-table-plugin-element": "^3.0.8", "x2js": "^3.4.4", "xe-utils": "^3.5.11", "xss": "^1.0.14"}, "devDependencies": {"@iconify/vue": "^4.0.2", "@rollup/plugin-commonjs": "28.0.1", "@rollup/plugin-node-resolve": "15.3.0", "@rushstack/eslint-patch": "^1.2.0", "@types/lodash": "^4.14.191", "@vitejs/plugin-vue": "5.1.1", "@vitejs/plugin-vue-jsx": "4.0.0", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.28.0", "eslint-plugin-vue": "^9.8.0", "globby": "^13.1.3", "less": "^4.1.3", "prettier": "^2.8.0", "rimraf": "^4.1.2", "rollup": "4.25.0", "terser": "^5.16.0", "unplugin-vue-define-options": "^1.0.0", "vite": "5.4.11", "vite-plugin-mars3d": "^4.2.2", "vite-plugin-svg-icons": "2.0.1"}, "engines": {"node": ">= 16", "pnpm": "< 8"}}
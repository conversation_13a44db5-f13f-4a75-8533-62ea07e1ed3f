package cn.fight.village.domain.data.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import lombok.Data;

@Data
public class HouseholdExcel {

    @ExcelProperty("序号")
    private String sort;

    @ExcelProperty("家庭人员姓名")
    private String name;

    @ExcelProperty("性别")
    private String gender;

    @ExcelProperty("民族")
    private String nation;

    @ExcelProperty("与户主的关系")
    private String relation;

    @ExcelProperty("电话号码")
    private String phone;

    @ExcelProperty("身份证号")
    private String idCode;

    @ExcelProperty("户籍地址")
    private String address;

    @ExcelProperty("确认时间")
    private String sureTime;

    @ExcelProperty("是否集体组织成员")
    private String collMember;

    @ExcelProperty("备注")
    private String remark;

    public String toString() {
        return JSON.toJSONString(this);
    }
}

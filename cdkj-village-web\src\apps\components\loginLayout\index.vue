<template>
  <div class="login_layout_component">
    <div class="login-thumbnails">
      <funi-image style="width: 100vw; height: 100vh" fit="fill" :src="systemLoginBg">
        <template #error> </template>
        <template #placeholder>
          <span></span>
        </template>
      </funi-image>
    </div>
    <div class="center_view">
      <div class="login_box">
        <div class="left">
          <funi-image :src="loginImg" fit="fill" class="leftImg">
            <template #error> </template>
            <template #placeholder>
              <span></span>
            </template>
          </funi-image>
        </div>
        <div class="right">
          <div class="top_logo">
            <funi-image :src="loginLogo" fit="contain" class="logo" alt="logo">
              <template #error> </template>
              <template #placeholder>
                <span></span>
              </template>
            </funi-image>
            <div class="platform_name" :style="{ fontSize: '28px' }">
              {{ platformName }}
            </div>
          </div>
          <div class="form_container">
            <slot></slot>
          </div>
          <!-- <div class="copyright">{{ techSupport }}</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const systemLoginBg = ref('./staticapp/login_bg.png');
const loginLogo = ref('./staticapp/logo.png');
const platformName = ref('乡村资源智慧管理系统');
const loginImg = ref('./staticapp/login_box.png');
const techSupport = ref('CopyRight@2024-2025 四川灵熹未来科技有限公司');
</script>

<style lang="scss" scoped>
.login_layout_component {
  display: flex;
  height: 100vh;
  justify-content: center;
  align-items: center;
  background-size: 100% 100%;

  .login-thumbnails {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    transition: all 0.7s;
  }

  .center_view {
    .top_logo {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: center;
      .logo {
        height: 48px;
        width: 48px;
        vertical-align: top;
      }

      .platform_name {
        margin-left: 8px;
        font-weight: 500;
        color: #004e9c;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .login_entry {
      font-weight: 400;
      color: #004e9c;
      text-align: center;
      margin: 12px 0;
    }
  }

  .login_box {
    width: 900px;
    min-height: 580px;
    background: #ffffff;
    box-shadow: 0 0 10px #00000026;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    display: flex;
    .left {
      width: 50%;
      overflow: hidden;

      .leftImg {
        height: 100%;
        width: 100%;

        transition: transform 2s;

        &:hover {
          transform: scale(1.2);
        }
      }
    }

    .right {
      width: 50%;
      padding: 20px 50px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      position: relative;
      justify-content: center;

      .form_container {
        margin-top: 30px;
        // flex: 1;
      }

      .copyright {
        margin-top: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #a7abb2;
        line-height: 20px;
        text-align: center;
      }
    }
  }
}
</style>

export function useColumnsOptions({ seeDateils = () => {}, deleteClick = () => {}, _edit = () => {} }) {
  return [
    {
      label: '户编号',
      prop: 'memberCode',
      render: ({ row, index }) => {
        return (<el-button type="primary" link  onClick={()=>seeDateils} >{row.memberCode}</el-button>);
      }
    },
    {
      label: '户主姓名',
      prop: 'memberName'
    },
    { label: '户主性别', prop: 'dicGenderName' },
    { label: '户主证件类型', prop: 'dicCardTypeName' },
    { label: '户主证件号码', prop: 'cerCertificateNo' },
    { label: '户主联系方式', prop: 'phone' },
    { label: '家庭住址', prop: 'address' },
    {
      label: '是否贫困户',
      prop: 'isPoverty',
      render: ({ row, index }) => {
        return <div>{row.isPoverty ? '是' : '否'}</div>;
      }
    },
    {
      label: '是否低保户',
      prop: 'isAllowance',
      render: ({ row, index }) => {
        return <div>{row.isAllowance ? '是' : '否'}</div>;
      }
    },
    { label: '核查时间', prop: 'createTime' },
    {
      label: '操作',
      prop: 'operList',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => {
                _edit(row);
              }}
            >
              编辑
            </el-button>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                deleteClick(row);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ];
}

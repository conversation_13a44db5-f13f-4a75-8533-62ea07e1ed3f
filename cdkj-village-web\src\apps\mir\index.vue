<template>
  <div class="page-mir">
    <div class="header-box">
      <div class="time">{{ currentTime }}</div>
      <div class="title">乡村一张图系统</div>
    </div>
    <div id="mars3dContainer" class="mars3d-container"></div>
    <div class="left">
      <wBox title="乡村介绍">
        <div class="empty">暂无数据</div>
      </wBox>
      <wBox title="乡村介绍">
        <div class="empty">暂无数据</div>
      </wBox>
      <wBox title="乡村介绍">
        <div class="empty">暂无数据</div>
      </wBox>
    </div>
    <div class="right">
      <wBox title="乡村介绍">
        <div class="empty">暂无数据</div>
      </wBox>
      <wBox title="乡村介绍">
        <div class="empty">暂无数据</div>
      </wBox>
      <wBox title="乡村介绍">
        <div class="empty">暂无数据</div>
      </wBox>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import wBox from "./component/wBox.vue";
import * as mars3d from "mars3d";

const Cesium = mars3d.Cesium;

const currentTime =ref()
function startTimer(){
const now = new Date();

const year = now.getFullYear();  
const month = String(now.getMonth() + 1).padStart(2, '0'); // 补零  
const day = String(now.getDate()).padStart(2, '0');  
const hours = String(now.getHours()).padStart(2, '0');  
const minutes = String(now.getMinutes()).padStart(2, '0');  
const seconds = String(now.getSeconds()).padStart(2, '0');  

const formattedDateTime = `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;  
  currentTime.value = formattedDateTime
  setTimeout(()=>{
      startTimer()
  },1000)
}
onMounted(() => {
  const map = new mars3d.Map("mars3dContainer", {
    basemaps: [{ name: "天地图", type: "tdt", layer: "img_d", show: true }],
  });
  startTimer()
});

</script>

<style lang="scss" scoped>
.page-mir {
  height: 100%;
  .header-box {
    width: 100%;
    height: 80px;
    position: absolute;
    display: flex;
    background-image: url(@/assets/mir/header-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    justify-content: space-between;
    z-index: 1;
    .time {
      color: #bdbdbd;
      padding: 8px 0 0 20px;
    }
    .title {
      font-family: luo;
      position: absolute;
      left: 50%;
      transform: translate(-50%);
      font-weight: 400;
      font-size: 30px;
      line-height: 70px;
      letter-spacing: 0.1rem;
      color: #fff;
      background: linear-gradient(1deg, #d4e7ef, #f9feff);
      -webkit-background-clip: text;
      background-clip: text;
    }
  }
  .left {
    position: absolute;
    left: 0;
    top: 82px;
  }
  .right {
    position: absolute;
    top: 82px;
    right: 0;
  }
  .empty {
    height: 200px;
    display: flex;
    color: #eff6ff;
    justify-content: center;
    align-items: center;
    background-color: #47e8fe45;
  }
}
</style>

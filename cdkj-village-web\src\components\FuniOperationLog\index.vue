<!--办件记录-->
<template>
  <div>
    <funi-curd :columns="columns" :data="curdData" :loading="loading" :pagination="false" />
  </div>
</template>

<script setup>
import { watch, ref } from "vue"

/**
 * 默认接口
 */
const listApi = "/bpmn/taskCenterApi/queryBusinessUserRecordInfoList";

defineOptions({
  name: 'FuniOperationLog',
  inheritAttrs: false
});

const props = defineProps({
  data: { type: Array, default: () => [] },
  api: { type: String, default: '' },
  params: {
    type: Object, default: () => ({}),
  }
});

watch(
  () => props.data,
  newData => {
    if (props.data) {
      curdData.value = newData;
    }
  },
  { deep: true }
);

const curdData = ref([])
const loading = ref(false)

if (props.data.length === 0) {
  loading.value = true;
  $http.post(props.api || listApi, props.params)
    .then(res => {
      curdData.value = res.list;
    })
    .finally(() => {
      loading.value = false;
    });
}

const columns = ref([
  {
    label: '处理时间',
    prop: 'executeTime'
  },
  {
    label: '处理结果',
    prop: 'content',
    render: ({ row, index }) => {
      return row.content === '启动业务' ? "启动业务" : (row.activityName + row.content);
    }
  },
  {
    label: '处理人员',
    prop: 'operator',
    render: ({ row, index }) => {
      return row.operator ? row.operator.operatorName : "--";
    }
  },
  {
    label: '处理意见',
    prop: 'opinion'
  },
]);

</script>

<style lang="scss" scoped></style>

<template>
  <div v-if="!prepared"></div>
  <Teleport v-else :disabled="disabled || deactivated" v-bind="$attrs">
    <slot></slot>
  </Teleport>
</template>

<script setup>
import { nextTick, onActivated, onDeactivated, onMounted, ref } from 'vue';

defineOptions({
  name: 'FuniTeleport',
  inheritAttrs: false
});

defineProps({
  disabled: Boolean
});

const deactivated = ref();
const prepared = ref();

onMounted(() => {
  nextTick(() => (prepared.value = true));
});
onActivated(() => (deactivated.value = false));
onDeactivated(() => (deactivated.value = true));
</script>

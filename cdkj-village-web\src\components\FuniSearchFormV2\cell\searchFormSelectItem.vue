<template>
  <el-select :multiple="attribute.multi" style="width: 100%" placeholder="请选择">
    <el-option v-for="item in options" :key="item.value" :label="item.key" :value="item.value" />
  </el-select>
</template>

<script setup>
import { ref, watchEffect } from 'vue';

defineOptions({
  name: 'SearchFormSelectItem'
});

const props = defineProps({
  attribute: { type: Object, default: () => ({}) }
});

const options = ref([]);

watchEffect(() => {
  $http
    .fetch(props.attribute.url)
    .then(res => {
      if (res && res.list && res.list.length > 0) {
        options.value = res.list;
      }
    })
    .catch(err => {
      options.value = [];
    });
});
</script>

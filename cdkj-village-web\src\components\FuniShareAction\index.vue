<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-01-04 17:40:43
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-06-28 17:33:13
 * @FilePath: \funi-paas-csccs-ui\src\components\FuniShareAction\index.vue
 * @Description: 分享弹窗
 * Copyright (c) 2023 by 郑佳 <EMAIL>, All Rights Reserved. 
-->
<template>
  <div class="share-action">
    <slot>
      <el-link type="primary"
        @click="show">分享</el-link>
    </slot>
    <ShareDialog ref="shareDialogRef"
      :schema="schema"
      :rules="rules"
      @ok="handleOk" />
    <ShareSuccess ref="shareSuccessRef"
      @close="onClose" />
  </div>
</template>
<script setup>
import { ElOption, ElSelect } from 'element-plus';
import { ref, h, nextTick, computed } from 'vue';
import ShareDialog from './ShareDialog.vue';
import ShareSuccess from './ShareSuccess.vue';
const shareDialogRef = ref(undefined);
const shareSuccessRef = ref(undefined);
const shareType = ref(undefined);
const timeValid = ref(false);
const props = defineProps({
  /**
   * 分享名称
   */
  shareName: {
    type: String,
    default: '文章分享'
  },
  /**
   * 真实地址
   */
  serviceUrl: {
    type: String,
    default: ''
  }
});

const schema = computed(() => {
  let list = [
    {
      label: '分享类型',
      component: () => {
        return h(
          ElSelect,
          {
            placeholder: '请选择分享类型',
            style: {
              width: '100%'
            }
          },
          {
            default: () => [
              h(ElOption, {
                key: 'EXTRACTION_CODE',
                label: '提取码',
                value: 'EXTRACTION_CODE'
              }),
              h(ElOption, {
                key: 'SMS_VALIDATE',
                label: '手机、短信',
                value: 'SMS_VALIDATE'
              }),
              h(ElOption, {
                key: 'THIRD_PARTY_INTEGRATION',
                label: '第三方集成',
                value: 'THIRD_PARTY_INTEGRATION'
              }),
              h(ElOption, {
                key: 'SECRET_FREE',
                label: '免密',
                value: 'SECRET_FREE'
              })
            ]
          }
        );
      },
      on: {
        change: e => {
          let values = shareDialogRef.value.formApi.getValues();
          shareType.value = e;
          nextTick(() => {
            shareDialogRef.value.formApi.setValues(values);
          });
        }
      },
      prop: 'shareType'
    },
    {
      label: '是否校验有效期',
      component: 'ElSwitch',
      props: {
        'active-value': true,
        'inactive-value': false
      },
      prop: 'timeValid',
      on: {
        change: val => {
          timeValid.value = val;
        }
      }
    },
    {
      label: '描述',
      component: 'ElInput',
      prop: 'description',
      props: {
        placeholder: '请输入描述',
        type: 'textarea',
        rows: 5
      }
    }
  ];
  if (shareType.value === 'EXTRACTION_CODE' || shareType.value === 'SMS_VALIDATE') {
    list.splice(1, 0, {
      label: '鉴权因子',
      component: 'ElInput',
      prop: 'shareFactor',
      props: {
        placeholder: '请输入鉴权因子'
      }
    });
  } else if (shareType.value === 'THIRD_PARTY_INTEGRATION') {
    list.splice(1, 0, {
      label: '第三方客户端ID',
      component: 'ElInput',
      prop: 'shareFactor',
      props: {
        placeholder: '请输入鉴权因子'
      }
    });
  }
  if (timeValid.value === true) {
    list.splice(list.length - 1, 0, {
      label: '有效期',
      component: 'el-date-picker',
      prop: 'start-end',
      props: {
        teleported: false,
        'start-placeholder': '请选择开始时间',
        'end-placeholder': '请选择开始时间',
        type: 'datetimerange',
        format: 'YYYY-MM-DD HH:mm:ss',
        'value-format': 'YYYY-MM-DD HH:mm:ss',
        'default-time': [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]
      }
    });
  }
  return list;
});

const rules = computed(() => {
  return {
    shareType: [{ required: true, message: '必填', trigger: 'change' }],
    shareFactor: [
      shareType.value === 'SMS_VALIDATE'
        ? {
          required: true,
          validator: $utils.Validate.validatePhone,
          trigger: 'change'
        }
        : { required: true, message: '必填', trigger: 'change' }
    ],
    // use: [{ required: true, message: '必填', trigger: 'change' }],
    'start-end': [{ required: true, message: '必填', trigger: 'change' }]
  };
});

function show () {
  shareType.value = undefined;
  timeValid.value = false;
  shareDialogRef.value.show('发起分享');
}

function handleOk ({ values, next }) {
  let timeObj = {};
  if (values.timeValid) {
    const [effectTime, invalidTime] = values['start-end'];
    timeObj = { effectTime, invalidTime };
  }
  if (shareType.value === 'SECRET_FREE') {
    delete values['shareFactor'];
  }
  delete values['start-end'];
  const { shareName, serviceUrl } = props;
  let url = '/csccs/share/behavior/fastShare';
  let param = { ...values, ...timeObj, timeValid: values.timeValid || false, shareName, serviceUrl };
  window.$http.post(url, param).then(res => {
    shareSuccessRef.value.show(res, next);
  });
}

function onClose () { }

defineExpose({ show });
</script>
<style lang="scss" scoped>
.share-action {
  .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
  }
  .message-box {
    width: 500px;
  }
}
</style>

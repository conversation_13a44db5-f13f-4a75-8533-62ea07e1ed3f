<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="cardTab" @headBtnClick="headBtnClick" />
    <funi-dialog v-model="isShow" title="合同内容" size="large">
      <component :is="contractType" ref="contractRef" v-bind="contractModalInfo" :isDetail="true"></component>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isShow = false">取消</el-button>
          <el-button type="primary" @click="toPrint"> 打印 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import contractTemplate from "./landWander/components/contractTemplate.vue";
import contractSupplement from "./landWander/components/contractSupplement.vue";
import { contractList, contractRemove, contractInfo, sureSign } from "@/apps/api/contract.js";
const router = useRouter();
const listPage = ref();
const isShow = ref();
const contractType = ref();
const contractModalInfo = ref();
const contractRef = ref();
/**
 * 列表配置
 */
const cardTab = reactive([
  {
    curdOption: {
      reloadOnActive: true,
      lodaData: (pages, parmas) => {
        return contractList({ ...pages, ...parmas, type: "土地流转" });
      },
      searchConfig: {
        schema: [
          {
            prop: "upperName",
            label: "出租方",
            component: "el-input",
          },
          {
            prop: "contractNo",
            label: "承包合同号",
            component: "el-input",
          },
          {
            prop: "underName",
            label: "承租方",
            component: "el-input",
          },
          {
            prop: "project",
            label: "归属项目",
            component: "el-input",
          },
          {
            prop: "signed",
            label: "是否完成签署",
            component: "funi-select",
            props: {
              options: [
                {
                  value: "1",
                  label: "是",
                },
                {
                  value: "2",
                  label: "否",
                },
              ],
            },
          },
        ],
      },
      btns: [{ key: "add", label: "新增" }],
      columns: [
        {
          label: "合同号",
          prop: "contractNo",
          render: ({ row, index }) => {
            return (
              <el-button type="primary" link onClick={() => detail(row)}>
                {row.contractNo}
              </el-button>
            );
          },
        },
        {
          label: "地确权（合同）总面积（亩）",
          prop: "rightArea",
        },
        { label: "出租方", prop: "upperName" },
        { label: "承租方", prop: "underName" },
        { label: "承租地址", prop: "underLocation" },
        { label: "项目归属", prop: "project" },
        { label: "签约状态", prop: "signed" },
        { label: "承包方（代表）名称", prop: "underName" },
        {
          label: "操作",
          prop: "opt",
          align: "center",
          fixed: "right",
          render: ({ row, index }) => {
            let arr = [];
            if (row.signed == "未完成签约") {
              arr.push(
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    sign(row);
                  }}
                >
                  完成签约
                </el-button>
              );
            }
            arr.push(
              <el-button
                type="primary"
                link
                onClick={() => {
                  showContractModal(row, "transProtocol");
                }}
              >
                出租协议
              </el-button>
            );
            arr.push(
              <el-button
                type="primary"
                link
                onClick={() => {
                  showContractModal(row, "transProtocolSup");
                }}
              >
                补充协议
              </el-button>
            );
            return arr;
          },
        },
      ],
    },
  },
]);

/**
 *
 * @param row 展示合同协议弹框
 * @param type
 */
function showContractModal(row, type) {
  if (type == "transProtocol") {
    contractType.value = contractTemplate;
  } else {
    contractType.value = contractSupplement;
  }
  contractInfo({ uuid: row.uuid }, { isLoading: true }).then((res) => {
    contractModalInfo.value = res;
    isShow.value = true;
  });
}

function toPrint() {
  contractRef.value.print();
}

/**
 * 编辑
 * @param row 数据行
 */
function edit(row) {
  router.push({
    name: "WanderContractAdd",
    query: {
      id: row.uuid,
      title: "土地流转管理编辑",
      bizName: "编辑",
      tab: `土地流转管理-${row.contractCretNo}-编辑`,
    },
  });
}
/**
 * 删除
 * @param row 数据行
 */
function del(row) {
  contractRemove({ uuid: row.uuid }).then((res) => {
    listPage.value.reload();
  });
}
/**
 * 详情
 * @param row 数据行
 */
function detail(row) {
  router.push({
    name: "WanderContractDetail",
    query: {
      id: row.uuid,
      contractNo: row.contractNo,
      title: "土地流转管理详情",
      bizName: "详情",
      tab: `土地流转管理-${row.contractNo}-详情`,
    },
  });
}
/**
 * 按钮点击事件
 */
function headBtnClick(res) {
  switch (res) {
    case "add":
      router.push({
        name: "WanderContractAdd",
        query: {
          title: "土地流转管理新增",
          bizName: "新建",
          tab: "土地流转管理-新增",
        },
      });
      break;
    default:
      break;
  }
}

/**
 * 确认签约
 */
function sign(row) {
  ElMessageBox.confirm("确认签约完成？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    sureSign({ uuid: row.uuid, signed: "1" }).then((res) => {
      listPage.value.reload();
    });
  });
}
</script>

<style lang="scss" scoped></style>

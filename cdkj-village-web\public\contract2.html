<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>农村土地经营权出租合同补充协议</title>
  <style>
    body,
    div,
    dl,
    dt,
    dd,
    ul,
    ol,
    li,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    pre,
    code,
    form,
    fieldset,
    legend,
    input,
    button,
    textarea,
    p,
    blockquote,
    th,
    td {
      margin: 0;
      padding: 0;
    }

    .container {
      width: 640px;
      margin: 0 auto;
      font-size: 18px;
      font-family: "宋体";
      line-height: 2;
    }

    .title {
      font-size: 40px;
      padding: 50px 0;
      text-align: center;
      line-height: 1.5;
    }

    .section {
      font-size: 22px;
    }

    p {
      margin: 5px 0;
      text-indent: 2em;
      text-align: justify;
      margin: 4px 0;
    }

    .bold {
      font-weight: bold;
      font-family: "microsoft yahei";
    }

    .line {
      display: flex;
      align-items: center;
    }

    .line input[type="text"] {
      flex: 1;
    }

    input[type="text"] {
      height: 20px;
      border-bottom: 1px solid #000 !important;
      border-right: none;
      border-left: none;
      border-top: none;
      vertical-align: text-top;
      color: #000;
      outline: none;
    }

    input[type="number"] {
      height: 20px;
      border-bottom: 1px solid #000 !important;
      border-right: none;
      border-left: none;
      border-top: none;
      vertical-align: text-top;
      color: #000;
      outline: none;
    }

    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    
    input[type="radio"] {
      appearance: none;
      -webkit-appearance: none;
      width: 14px;
      height: 14px;
      cursor: pointer;
      border: 1px solid #676565;
      position: relative;

    }

    /* 用伪元素模拟选中状态 */
    input[type="radio"]:checked::before {
      content: '✓';
      position: absolute;
      left: 2px;
      top: -6px;
      font-size: 16px;
    }

    input[type="checkbox"] {
      appearance: none;
      -webkit-appearance: none;
      width: 14px;
      height: 14px;
      cursor: pointer;
      border: 1px solid #676565;
      position: relative;

    }

    /* 用伪元素模拟选中状态 */
    input[type="checkbox"]:checked::before {
      content: '✓';
      position: absolute;
      left: 2px;
      top: -6px;
      font-size: 16px;
    }

    label input {
      margin-right: 2px;
    }

    input,
    button,
    textarea,
    select {
      font-family: inherit;
      font-size: inherit;
      font-weight: inherit;
      background: transparent;
    }

    .textarea {
      border: 1px solid #ddd;
      border: 1px solid #ddd;
      min-height: 74px;
      padding: 0 10px;
    }

    .date-input {
      width: 2.4em;
      text-align: center;
    }

    table {
      border-collapse: collapse;
      table-layout: fixed;
      width: 100%;
    }

    th,
    td {
      border: 1px solid;
      text-align: center;
    }

    p .required {
      color: #f00 !important;
      border-bottom: 1px solid #f00 !important;
    }

    .underlined-text {
      border-bottom: 1px solid;
      text-underline-offset: 4px;
      text-decoration-thickness: 0px;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1 class="title">农村土地经营权出租合同补充协议</h1>

    <div class="content">
      <p class="line">出租方（以下简称甲方）：<input type="text" id="partyA" required /></p>
      <p class="line">法定代表人：<input type="text" id="paId" required /></p>

      <p style="padding-top: 20px;"></p>
      <p class="line">承租方（以下简称乙方）：<input type="text" id="partyB" required /></p>
      <p class="line">法定代表人：<input type="text" id="pbReprese" required /></p>
      <p class="line">身份证号码：<input type="text" id="pbId" required /></p>

      <p style="padding-top: 20px;">
        鉴于：甲乙双方通过平等、自愿协商，于
        <input type="number" name="signDate" class="date-input" required />年
        <input type="number" name="signDate" class="date-input" required />月
        <input type="number" name="signDate" class="date-input" required />日
        签订了《农村土地经营权出租合同》(以下简称“原合同”)，约定租赁期限至
        <input type="number" name="appointTime" class="date-input" required />年
        <input type="number" name="appointTime" class="date-input" required />月
        <input type="number" name="appointTime" class="date-input" required />日
        （第二轮土地承包到期之日）止。为贯彻落实农村土地承包政策，稳定土地经营权流转关系，保障双方合法权益，经双方充分协商，在尊重农民意愿的基础上，就承包延期后土地续租事宜达成如下补充协议，以资双方共同遵守：
      </p>
      <p>
        一、双方协商一致同意，在原合同约定的租赁期限（
        <input type="number" name="startTime" class="date-input" required />年
        <input type="number" name="startTime" class="date-input" required />月
        <input type="number" name="startTime" class="date-input" required />日至
        <input type="number" name="endTime" class="date-input" required />年
        <input type="number" name="endTime" class="date-input" required />月
        <input type="number" name="endTime" class="date-input" required />日）
        届满后，原合同中甲方位于<input type="text" id="location" />的<input type="text" id="area"
          style="width:5em" />亩土地承包经营权延期后，且未发生耕地非农化、耕地非粮化的情况下，原合同期至
        <input type="number" name="upTime" class="date-input" required />年
        <input type="number" name="upTime" class="date-input" required />月
        <input type="number" name="upTime" class="date-input" required />日，
        双方配合办理土地流转审批手续，并另签合同。租金标准、支付时间等本补充协议未另行约定的内容仍按原合同的约定执行。
      </p>
      <p>
        二、本补充协议与原合同具有同等法律效力，本协议与原合同不一致的，以本协议为准。
      </p>
      <p>
        三、本补充协议经双方签字盖章后生效。协议一式肆份，均为正本，甲方、乙方、农村集体经济组织、镇政府，各执壹份，每份均具同等效力。
      </p>

      <p style="padding-top:60px"></p>
      <div style="display: flex;">
        <div style="flex:1">
          <div style="height:60px;text-indent: 2em;">甲方(盖章)：</div>
          <p style="height:60px;">法定代表人：</p>
          <p>地址：</p>
          <p>联系电话：</p>
          <p>时间：<input type="number" class="date-input" name="paSignTime" />年 <input type="number" class="date-input"
              name="paSignTime" />月<input type="number" class="date-input" name="paSignTime" />日</p>
        </div>
        <div style="flex:1">
          <div style="height:60px;display: flex;">
            <div style="flex-shrink: 0;text-indent: 2em;">乙方(盖章)：</div>
          </div>
          <p style="height:60px;">法定代表人：：</p>
          <p>地址：</p>
          <p>联系电话：</p>
          <p>时间：<input type="number" class="date-input" name="pbSignTime" />年 <input type="number" class="date-input"
              name="pbSignTime" />月<input type="number" name="pbSignTime" class="date-input" />日</p>
        </div>
      </div>
    </div>
  </div>
</body>
<script>
  /**
* 初始化
*/
  function init() {
    document.querySelectorAll(`input`).forEach(item => {
      //必填
      if (item.required) {
        item.addEventListener("input", () => {
          if (!item.value) {
            item.classList.add("required")
          }
          else {
            item.classList.remove("required")
          }
        })
      }
    })
  }
  init();

  /**
   * 获取表格值
  */
  function getTableData(tableId) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach(row => {
      const rowData = [];
      const cells = row.querySelectorAll('td, th');

      cells.forEach(cell => {
        rowData.push(cell.textContent.trim());
      });

      data.push(rowData);
    });

    return data;
  }

  /**
   * 设置默认值
  */
  function setValues(obj) {
    for (let key in obj) {
      let value = obj[key];
      //土地
      if (key == "lands") {
        const tbody = document.getElementById("landTableBody");
        let htmlString = ""
        value.forEach((item, index) => {
          htmlString += `
          <tr>
            <td>${(index + 1)}</td>
            <td contenteditable="true"></td>
            <td>${item.dkmc}</td>
            <td>${item.landNo}</td>
            <td>${item.dkdz}</td>
            <td>${item.dknz}</td>
            <td>${item.dkxz}</td>
            <td>${item.dkbz}</td>
            <td>${item.area}</td>
            <td contenteditable="true"></td>
            <td>${item.landType}</td>
            <td contenteditable="true"></td>
            <td>${item.remark}</td>
          </tr>`;
        })
        tbody.innerHTML = htmlString;
      }
      else if (key == "textObject") {
        for (let i in value) {
          let val = value[i];
          let el = document.getElementsByName(`textObject.${i}`)
          el.forEach(item => {
            if (item.type == 'radio' && item.value == val) {
              item.checked = true
            }
            else if (item.type == 'checkbox' && val.includes(item.value)) {
              item.checked = true
            }
            else if (item.nodeName == 'INPUT') {
              item.value = val
            }
            else {
              item.textContent = val
            }
          })
        }
      }
      else {
        let el = document.querySelector(`[id=${key}]`)
        if (el) {
          if (el.nodeName == 'INPUT') {
            el.value = value
          }
          else {
            el.textContent = value
          }
        }
        else {
          el = document.querySelectorAll(`[name=${key}]`);
          el.forEach((item, index) => {
            if (item.type == 'radio' || item.type == 'checkbox') {
              if ((value || '').split(",").includes(item.value)) {
                item.checked = true
              }
            }
            else if (item.nodeName == 'INPUT') {//日期
              item.value = (value || '').split("-")[index] || ''
            }
          })
        }

      }
    }
  }

   /**
   * 获取表单值
  */
  function getValues() {
    return new Promise((resolve, reject) => {

      let obj = {}
      let isPass = true;
      document.querySelectorAll("body [id]").forEach(item => {
        if (!isPass) {
          return;
        }
        if (item.required && !item.value) {
          item.classList.add("required")
          item.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest"
          });
          isPass = false;
        }
        obj[item.id] = item.value || item.textContent
      })

      document.querySelectorAll("body [name]").forEach(item => {
        if(!item.name) return;
        let key = item.name
        let arr = item.name.split(".")
        let _obj = obj;
        if(arr.length>1){
          obj[arr[0]] = {}
          _obj = obj[arr[0]]
          key = arr[1]
        }
       
        if (item.type == 'radio') {
          if(item.checked){
              _obj[key] = item.value
          }
        }
        else if (item.type == 'checkbox') {
          if(item.checked){
            _obj[key] = (_obj[key] ? _obj[key] + "," : "") + item.value
          }
        }
        else {//日期
          _obj[key] = (_obj[key]?_obj[key]+"-" : '')+ item.value
        }
      })
      console.log(obj)
      isPass ? resolve(obj) : reject()
    })
  }


  /**
   * 金额转换
  */
  function amountToChinese(num) {
    var str1 = '零壹贰叁肆伍陆柒捌玖';  //0-9所对应的汉字
    var str2 = '万仟佰拾亿仟佰拾万仟佰拾元角分'; //数字位所对应的汉字
    var str3;    //从原num值中取出的值
    var str4;    //数字的字符串形式
    var str5 = '';  //人民币大写金额形式
    var i;    //循环变量
    var j;    //num的值乘以100的字符串长度
    var ch1;    //数字的汉语读法
    var ch2;    //数字位的汉字读法
    var nzero = 0;  //用来计算连续的零值是几个

    num = Math.abs(num).toFixed(2);  //将num取绝对值并四舍五入取2位小数
    str4 = (num * 100).toFixed(0).toString();  //将num乘100并转换成字符串形式
    j = str4.length;      //找出最高位

    str2 = str2.substr(15 - j);

    //循环取出每一位需要转换的值
    for (i = 0; i < j; i++) {
      str3 = str4.substr(i, 1);   //取出需转换的某一位的值
      if (i != (j - 3) && i != (j - 7) && i != (j - 11) && i != (j - 15)) {    //当所取位数不为元、万、亿、万亿上的数字时
        if (str3 == '0') {
          ch1 = '';
          ch2 = '';
          nzero = nzero + 1;
        }
        else {
          if (str3 != '0' && nzero != 0) {
            ch1 = '零' + str1.substr(str3 * 1, 1);
            ch2 = str2.substr(i, 1);
            nzero = 0;
          }
          else {
            ch1 = str1.substr(str3 * 1, 1);
            ch2 = str2.substr(i, 1);
            nzero = 0;
          }
        }
      }
      else { //该位是万亿，亿，万，元位等关键位
        if (str3 != '0' && nzero != 0) {
          ch1 = "零" + str1.substr(str3 * 1, 1);
          ch2 = str2.substr(i, 1);
          nzero = 0;
        }
        else {
          if (str3 != '0' && nzero == 0) {
            ch1 = str1.substr(str3 * 1, 1);
            ch2 = str2.substr(i, 1);
            nzero = 0;
          }
          else {
            if (str3 == '0' && nzero >= 3) {
              ch1 = '';
              ch2 = '';
              nzero = nzero + 1;
            }
            else {
              if (j >= 11) {
                ch1 = '';
                nzero = nzero + 1;
              }
              else {
                ch1 = '';
                ch2 = str2.substr(i, 1);
                nzero = nzero + 1;
              }
            }
          }
        }
      }
      if (i == (j - 11) || i == (j - 3)) {  //如果该位是亿位或元位，则必须写上
        ch2 = str2.substr(i, 1);
      }
      str5 = str5 + ch1 + ch2;

      if (i == j - 1 && str3 == '0') {   //最后一位（分）为0时，加上“整”
        str5 = str5 + '整';
      }
    }
    if (num == 0) {
      str5 = '零元整';
    }
    return str5;
  }
</script>

</html>
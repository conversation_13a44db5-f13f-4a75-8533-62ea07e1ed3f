package cn.fight.village.domain.common.util;

import cn.fight.village.domain.common.constant.BusinessConstant;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.user.entity.User;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageInfo;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 工具方法
 */
public class CommonUtils {
    private CommonUtils() {}

    /**
     * 获取uuid
     *
     * @return
     */
    public synchronized static String getGuid() {
        return IdUtil.randomUUID();
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static Date currentDate() {
        return new Date();
    }

    /**
     * 获取分页对象
     *
     * @param dataList
     * @param <T>
     * @return
     */
    public static <T> PageInfo<T>  getPageInfo(List<T> dataList) {
        return new PageInfo<T>(dataList);
    }

    /**
     * 获取空分页对象
     *
     * @return
     */
    public static PageInfo<?> emptyPageInfo() {
        return new PageInfo<>(Collections.emptyList());
    }

    /**
     * 检查是否为管理员
     *
     * @param user
     */
    public static void checkIsAdmin(User user) {
        if (user == null || !BusinessConstant.USER_TYPE_ADMIN.equals(user.getUserType()))
            throw new BusinessException("权限不足");
    }

    /**
     * 断言字符不能为空
     *
     * @param input
     * @param message
     */
    public static void notNull(String input,String message) {
        if (StringUtils.isEmpty(input)) {
           throw new BusinessException(StringUtils.isNotBlank(message) ? message : "不允许传入空字符");
        }
    }
}

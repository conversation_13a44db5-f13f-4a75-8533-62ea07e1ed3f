<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-05-10 14:53:54
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-12 15:05:04
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniAuditButtomBtn\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="funi-audit-buttom-btn"
    :style="auditButtons && auditButtons.length > 0 ? { padding: '0px 12px',height:'50px' } : {}">
    <el-button v-for="item in auditButtons"
      :key="item.key"
      type="primary"
      @click="onAuditClick(item.action)">{{
      item.key
    }}</el-button>
  </div>
</template>

<script setup>
const props = defineProps({
  auditButtons: {
    type: Array,
    default: () => {
      const btns = [
        {
          key: '回退',
          action: 'BACK'
        },
        {
          key: '退件',
          action: 'RETURN'
        },
        {
          key: '不予受理',
          action: 'NOT_ACCEPT'
        },
        {
          key: '同意',
          action: 'COMMIT'
        }
      ];
      return btns;
    }
  }
});

const emit = defineEmits(['auditClick']);
function onAuditClick (action) {
  emit('auditClick', action);
}
</script>

<style lang="less" scoped>
.funi-audit-buttom-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>

import { ElMenuItem, ElSubMenu } from 'element-plus';
import { Icon } from '@iconify/vue';

export const useRenderMenuItem = () => {
  const renderMenuTitle = meta => {
    const { title = 'Please set title', icon } = meta;
    return (
      <>
        {!!icon && <Icon icon={icon}></Icon>}
        <span class="v-menu__title">{title}</span>
      </>
    );
  };

  const renderMenuItem = menus => {
    return menus.filter(x=>x.hidden !== true).map(menu => {
      if (menu.hidden) return null;

      const id = menu.name || menu.path;
      const meta = menu.meta;

      if (!!menu.children && !!menu.children.length && !menu.hideChildren) {
        return (
          <ElSubMenu index={id}>
            {{
              title: () => renderMenuTitle(meta),
              default: () => renderMenuItem(menu.children)
            }}
          </ElSubMenu>
        );
      } else {
        return (
          <ElMenuItem index={id}>
            {{
              default: () => renderMenuTitle(meta)
            }}
          </ElMenuItem>
        );
      }
    });
  };
  return { renderMenuItem };
};

<template>
  <div>
    <funi-list-page-v2
      ref="listPage"
      :cardTab="cardTab"
      @headBtnClick="headBtnClick"
    />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { list } from '@/apps/api/fileManage.js';

const router = useRouter();
const listPage = ref();
/**
 * 列表配置
 */
const cardTab = reactive([
  {
    curdOption: {
      reloadOnActive: true,
      lodaData: (pages, parmas) => {
        return list({ ...pages, ...parmas, contractType: '家庭承包' });
      },
      searchConfig: {
        schema: [
          {
            prop: 'contractNo',
            label: '合同编号',
            component: 'el-input'
          },
          {
            prop: 'upper',
            label: '承包方',
            component: 'el-input'
          },
          {
            prop: 'location',
            label: '家庭地址',
            component: 'el-input'
          },
          {
            prop: 'idCode',
            label: '证件号',
            component: 'el-input'
          },
          {
            prop: 'upper',
            label: '发包方',
            component: 'el-input'
          }
        ]
      },
      columns: [
        {
          label: '档案流水号',
          prop: 'docNo',
          render: ({ row, index }) => {
            return (
              <el-button type="primary" link onClick={() => detail(row)}>
                {row.docNo}
              </el-button>
            );
          }
        },
        {
          label: '合同编号',
          prop: 'contractNo',
        },
        {
          label: '发包方',
          prop: 'upper'
        },
        { label: '发包方负责人', prop: 'upperName' },
        { label: '承包方', prop: 'under' },
        { label: '承包方负责人', prop: 'underName' },
        { label: '承包方地址', prop: 'location' },
        {
          label: '操作',
          prop: 'opt',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            return (
              <div>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    detail(row);
                  }}
                >
                  详情
                </el-button>
              </div>
            );
          }
        }
      ]
    }
  }
]);

/**
 * 详情
 * @param row 数据行
 */
function detail(row) {
  router.push({
    name: 'FileManageContractDetail',
    query: {
      id: row.contractId,
      title: '承包地合同',
      bizName: '详情',
      tab: `承包地合同-${row.contractNo}-详情`
    }
  });
}
/**
 * 按钮点击事件
 */
function headBtnClick(res) {
  switch (res) {
    case 'add':
      router.push({
        name: 'ContractAdd',
        query: {
          title: '家庭承包信息新增',
          bizName: '新建',
          tab: '家庭承包信息-新增'
        }
      });
      break;
    default:
      break;
  }
}
</script>

<style lang="scss" scoped></style>

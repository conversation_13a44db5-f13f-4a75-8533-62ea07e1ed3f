<template>
  <div>
    <funiDialog :destroy-on-close="true" v-model="modalConfig.show" v-bind="modalConfig" :onConfirm="onConfirm">
      <funiForm v-bind="formConfig" ref="refForm" :col="2" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="modalConfig.show = false">取消</el-button>
          <el-button type="primary" :disabled="loading" @click="onConfirm">确定</el-button>
        </span>
      </template>
    </funiDialog>
  </div>
</template>

<script setup lang="jsx">
import { reactive, ref, nextTick, unref, onMounted, computed, watch } from 'vue';
import { dictList } from '../../api/index';
import NumInput from '@/apps/lfpra/common/components/numInput/index.vue';
const emit = defineEmits(['updated', 'addCallBack']);
const props = defineProps({
  isExamine: {
    type: <PERSON>olean,
    default: true
  }
});
let isExamine = ref(false);
const loading = ref(false);
const refForm = ref(null);
const modalConfig = reactive({
  show: false,
  title: ''
});
const isNew = ref(true); // 判断是新增还是编辑
const group = ref(true);

const enumeration = reactive({
  GENDER: [], // 性别
  CARD_TYPE: [], // 证件类型
  RELATION: [], // 与户主关系
  MEM_REMARK: [] // 人员备注
});
const isJudgment = ref([
  {
    label: '是',
    value: true
  },
  {
    label: '否',
    value: false
  }
]);

onMounted(() => {
  // CARD_TYPE:证件类型  GENDER:成员性别  RELATION:与户主关系 MEM_REMARK:人员备注
  let typeArr = ['GENDER', 'CARD_TYPE', 'RELATION', 'MEM_REMARK'];

  typeArr.forEach((type, index) => {
    dictList({ dictiEnum: type }).then(({ list }) => {
      let data = list.map(item => {
        return {
          label: item.name,
          value: item.code
        };
      });
      enumeration[type] = data;
    });
  });
});
const cityCodeChange = res => {
  group.value = !res;
};
const formConfig = computed(() => {
  let arr = [
    {
      prop: 'memberName',
      label: '姓名',
      component: 'el-input',
      props: { placeholder: '请输入姓名' }
    },
    {
      prop: 'dicGenderCode',
      label: '性别',
      component: 'FuniSelect',
      props: {
        options: enumeration.GENDER || [],
        style: 'width:100%',
        placeholder: '请选择'
      }
    },
    {
      prop: 'dicCardTypeCode',
      label: '证件类型',
      component: 'FuniSelect',
      props: {
        options: enumeration.CARD_TYPE || [],
        style: 'width:100%',
        placeholder: '请选择'
      }
    },
    {
      prop: 'cerCertificateNo',
      label: '证件号码',
      component: 'el-input',
      props: { placeholder: '请输入证件号码' }
    },
    {
      prop: 'phone',
      label: '联系方式1',
      component: 'el-input',
      props: { placeholder: '请输入座机或手机号码' }
    },
    {
      prop: 'otherPhone',
      label: '联系方式2',
      component: () => <NumInput />,
      props: { placeholder: '请输入座机或手机号码' }
    },
    {
      prop: 'dicRelationCode',
      label: '家庭关系',
      component: 'FuniSelect',
      props: {
        options: enumeration.RELATION || [],
        style: 'width:100%',
        placeholder: '请选择'
      }
    },
    {
      prop: 'isRegistered',
      label: '是否户籍人员',
      component: 'FuniSelect',
      props: {
        options: isJudgment.value,
        style: 'width:100%',
        placeholder: '请选择'
      }
    },
    {
      prop: 'isCollecMember',
      label: '是否集体组织成员',
      component: 'FuniSelect',
      props: {
        options: isJudgment.value,
        style: 'width:100%',
        placeholder: '请选择'
      },
      on: { change: cityCodeChange }
    },
    {
      prop: 'memberGroup',
      hidden: group.value,
      label: '组别',
      component: 'el-input',
      props: { placeholder: '请输入组别，如1组' }
    },
    {
      prop: 'isDisability',
      label: '是否残疾人',
      component: 'FuniSelect',
      props: {
        options: isJudgment.value,
        style: 'width:100%',
        placeholder: '请选择'
      }
    },
    {
      prop: 'isVeteran',
      label: '是否退伍军人',
      component: 'FuniSelect',
      props: {
        options: isJudgment.value,
        style: 'width:100%',
        placeholder: '请选择'
      }
    },
    {
      prop: 'dicMemRemarkCode',
      label: '人员备注',
      component: 'FuniSelect',
      props: {
        options: enumeration.MEM_REMARK,
        style: 'width:100%',
        placeholder: '请选择'
      }
    },
    {
      prop: 'remarkTime',
      label: '备注时间',
      component: 'el-date-picker',
      props: { type: 'date', valueFormat: 'YYYY-MM-DD', style: 'width:100%', placeholder: '请选择' }
    },
    {
      prop: 'remarkReason',
      label: '备注原因',
      component: 'el-input',
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    },
    {
      prop: 'remark',
      label: '其它备注',
      component: 'el-input',
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    }
  ];
  let detail = [];
  if (isExamine.value) {
    arr.map(item => {
      let obj = {
        label: item.label,
        prop: item.prop,
        colProps: item.colProps
      };
      //用于多选框 赋值
      if (['dicGenderCode', 'dicCardTypeCode', 'dicRelationCode', 'dicMemRemarkCode'].includes(item.prop)) {
        obj.prop = item.prop.replace('Code', 'Name');
      }
      if (['isCollecMember', 'isDisability', 'isRegistered', 'isVeteran'].includes(item.prop)) {
        obj.prop = item.prop + 'Name';
      }
      detail.push(obj);
    });
  }
  return {
    schema: !isExamine.value ? arr : detail,
    rules: {
      memberName: [{ required: true, message: '请输入姓名', trigger: 'change' }],
      dicGenderCode: [{ required: true, message: '请选择' }],
      dicCardTypeCode: [{ required: true, message: '请选择' }],
      cerCertificateNo: [{ required: true, message: '请输入证件号码' }],
      phone: [
        {
          validator: (rule, value, callback) => {
            var reg_tel = /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/;
            if (value === '' || value === null || value === undefined || value.length == 0) {
              callback('请输入座机或手机号码');
            } else if (!reg_tel.test(value)) {
              callback('号码格式不对');
            } else {
              callback();
            }
          },
          required: true,
          trigger: 'change'
        }
      ],
      dicRelationCode: [{ required: true, message: '请选择' }],
      isRegistered: [{ required: true, message: '请选择' }],
      isCollecMember: [{ required: true, message: '请选择' }],
      memberGroup: [{ required: !group.value, message: '请选择' }],
      isDisability: [{ required: true, message: '请选择' }],
      isVeteran: [{ required: true, message: '请选择' }]
    }
  };
});

const show = async (item, i) => {
  console.log(item, 'item-----');
  if (item) {
    isNew.value = false;
    modalConfig.title = '编辑人员信息';
    group.value = !item.isCollecMember;
  } else {
    isNew.value = true;
    modalConfig.title = '新增人员信息';
    item = {};
  }
  if (i) {
    isExamine.value = i;
    modalConfig.title = '查看人员信息';
  }
  modalConfig.show = true;
  nextTick(() => {
    unref(refForm).resetFields();
    unref(refForm).setValues(item);
  });
};
const onConfirm = async () => {
  let valid = await refForm.value.validateField();
  let value = refForm.value.getValues();
  if (valid.isValid) {
    let obj = {};
    // 性别
    let dicGender = enumeration.GENDER.find(item => item.value === value.dicGenderCode);
    // 证件类型
    let dicCard = enumeration.CARD_TYPE.find(item => item.value === value.dicCardTypeCode);
    // 家庭关系
    let dicRelation = enumeration.RELATION.find(item => item.value === value.dicRelationCode);
    obj = {
      dicGenderName: dicGender.label,
      dicCardTypeName: dicCard.label,
      dicRelationName: dicRelation.label
    };
    emit('addCallBack', { isNew: isNew.value, data: { ...value, ...obj } });
    modalConfig.show = false;
  }
};
defineExpose({
  show
});

// watch(
//   () => props.isExamine,
//   () => {
//     isExamine.value = props.isExamine;
//     if (!isExamine.value) {
//       modalConfig.title = '查看人员信息';
//     }
//   }
// );
</script>

<style lang="scss" scoped></style>

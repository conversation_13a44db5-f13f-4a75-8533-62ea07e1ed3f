package cn.fight.village.domain.common.api;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.util.SecureUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

/**
 * 应用许可证
 */
@Controller
@RequestMapping("licence")
public class LicenceApi {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 获取应用软件许可证请求码
     * @return
     */
    @ResponseBody
    @GetMapping("getLicenceRequestCode")
    public JsonResult getLicenceReqCode() {
        return JsonResult.successMessage("您的应用许可证请求码为：" +  SecureUtils.getLicenceRequest());
    }

    /**
     * 激活软件许可证
     * @param
     * @return
     * @throws IOException
     */
    @ResponseBody
    @PostMapping("activeLicence")
    public JsonResult activeLicence(@RequestBody Map<String,Object> params) throws IOException {
        if (CollectionUtils.isEmpty(params)) {
            return JsonResult.failMessage("缺失必要参数");
        }

        String licenceCode = (String) params.get("licenceCode");
        String result = SecureUtils.activeLicence(licenceCode);

        if("激活成功".equals(result)) {

        }

        return JsonResult.successMessage(result);
    }

    /**
     * 激活服务页面
     * @return
     */
    @RequestMapping("managePage")
    public String licencePage(Model model) {
        String requestCode = SecureUtils.getLicenceRequest();
        model.addAttribute("requestCode",requestCode);
        return "licence";
    }
}

import HyperlinkInfo from '@/apps/lfpra/common/components/hyperlinkTable/infoBtn.vue';
import Hyperlink from '@/apps/lfpra/common/components/hyperlinkTable/index.vue';
import Upload from '@/apps/lfpra/common/components/upload/upload.vue';
import { apiUrlCommon } from '@/apps/lfpra/common/hooks/api.js';
import { apiUrl } from './api.js';
import { moreBtnRender,erm_intl } from '@/apps/lfpra/common/hooks/utils.jsx';
export const userTableColumns = ({ seeDateils, editFunc = () => {}, delFunc = () => {} }) => {
  return [
    {
      label: '坑塘承包经营编号',
      prop: 'cerNumber',
      render: ({ row, index }) => {
        return (
          <HyperlinkInfo
            row={row}
            index={index}
            func={seeDateils}
            text={row.cerNumber}
          />
        );
      },
      fixed: 'left'
    },
    {
      label: '流转价款/股权分红（元）',
      prop: 'shareOutBonus',
      render: ({ row }) => {
        return `${erm_intl(row.shareOutBonus)}`;
      }
    },
    {
      label: '土地承包用途',
      prop: 'contractingUse'
    },
    {
      label: '发包方名称',
      prop: 'employerName'
    },
    {
      label: '发包方负责人姓名',
      prop: 'employerHeadName'
    },
    {
      label: '承包经营权取得方式',
      prop: 'dicCerManageName'
    },
    {
      label: '承包方（代表）名称',
      prop: 'contractorName'
    },
    {
      label: '承包方地址',
      prop: 'address'
    },
    {
      label: '核查时间',
      prop: 'createTime'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={editFunc}

              text={'编辑'}
            ></Hyperlink>
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前数据？"
              width="220"
              onConfirm={() => {
                delFunc(row);
              }}
            >
              {{
                reference: () => <Hyperlink  text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          )
        };
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(['EDIT','DEL'], operationBtn)}
          </div>
        );
      }
    }
  ];
};

// 按钮
export const useBtnsConfig = ({ addFunc = () => {}, importFunc = () => {},exportFunc=()=>{} }) => {
  return [
    {
      component: () => (
        // <el-button v-auth="ERM_CONTRACTINCOME_ADD" onClick={addFunc} type="primary">
        //   新建
        // </el-button>
         <el-button onClick={addFunc} type="primary">
         新增
       </el-button>
      )
    },
    {
      component: () => (
        // <Upload
        //   v-auth="ERM_PROJECT_MANPOWER_IMPORT"
        //   dc={downloadCode.ps}
        //   url={apiUrl.monthInvestmentInfoImport}
        //   callbackFun={importFunc}
        //   templateName="员工信息"
        //   title="批量导入"
        //   type="primary"
        // >
        //   导入
        // </Upload>
        <Upload
        dc={apiUrlCommon.downloadImportTemplate}
        dcParams={{downloadTemplate:'KTCB_INFO'}}
        url={apiUrl.importSiteInfo}
        callbackFun={importFunc}
        templateName="坑塘承包地信息模板"
        title="批量导入"
        type="primary"
      >
        导入
      </Upload>
      )
    },
    {
      component: () => (
        // <el-button v-auth="ERM_CONTRACT_COLLECTION_EXPORT" onClick={exportFun} type="primary">
        //   导出
        // </el-button>
         <el-button onClick={exportFunc} type="primary">
         导出
       </el-button>
      )
    }
  ];
};

<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2022-12-13 14:50:56
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-11-22 15:00:11
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniCurdV2/index.vue
 * @Description:
 * Copyright (c) 2022 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <funi-teleport to=".layout-content__wrap" :disabled="!teleported">
    <div ref="contentRef" :class="['funi-curd__wrap-v2', { teleported }]" v-loading="loading">
      <!-- search-form -->
      <CurdSearch
        v-if="isShowSearch"
        class="funi-curd__search"
        :use-search-v2="useSearchV2"
        :search-config="searchConfig"
        :col-number="colNumber"
        :queryFields="queryFields"
        @search="handleSearch"
      >
        <template v-for="(_, slot) in $slots" #[slot]="params">
          <slot :name="slot" v-bind="params || {}" />
        </template>
      </CurdSearch>

      <!-- table -->
      <div class="card funi-curd">
        <!-- 表格头部 操作按钮 -->
        <CurdHeader
          class="funi-curd__header"
          :columns="columns"
          :use-tools="useTools"
          :col-settings="colSettings"
          @refresh="reload"
          @resize="handleResize"
          @fullscreen="handleFullscreen"
          @col-setting="handleColSetting"
          @stripe="handleStripe"
        >
          <template v-if="$slots.header" #header>
            <slot name="header"></slot>
          </template>
          <template #buttonGroup>
            <slot name="buttonGroup"></slot>
          </template>
        </CurdHeader>

        <!-- 表格内容 -->
        <div class="funi-curd__table">
          <el-table
            ref="curdRef"
            v-bind="tableAttrs"
            v-draggable="draggableOptions"
            @cell-click="handleCellClick"
            @cell-dblclick="handleCellDoubleClick"
            @sort-change="handleSortChange"
            @filter-change="handleFilterChange"
          >
            <template #default>
              <el-table-column v-for="column in computedColumns" :key="column.prop" v-bind="column">
                <!-- 自定义表头 -->
                <template #header="scope">
                  <slot :name="column.slots.header">{{ column.label }}</slot>
                  <CurdFilterPanel
                    v-if="customFilters[column.prop] || customFilters[column.label]"
                    :column="{ ...scope.column, ...column }"
                    :store="scope.store"
                  />
                </template>
                <template #default="scope">
                  <slot v-if="column.slots.default" :name="column.slots.default" v-bind="scope" />
                  <component v-else-if="column.render" :is="contentRender(column.render, scope)" />
                  <template v-else-if="!['selection', 'index', 'expand'].includes(column.type)">
                    {{ defaultRenderCell(scope) }}
                  </template>
                </template>
              </el-table-column>
            </template>
            <!-- Table插槽 -->
            <template v-for="slot in ['append', 'empty']" #[slot]="params">
              <slot :name="slot" v-bind="params || {}" />
            </template>
          </el-table>
        </div>

        <!-- 分页 -->
        <CurdPagination
          v-if="pagination"
          ref="pageRef"
          class="funi-curd__pagination"
          style="margin-top: 10px"
          :total="dataTotal"
          :pageSizes="pageSizes"
          @pageChange="doRequest"
        >
          <template #default="params">
            <slot name="pagination_extra" v-bind="params"></slot>
          </template>
        </CurdPagination>
      </div>
    </div>
  </funi-teleport>
</template>

<script lang="jsx" setup>
import { computed, onActivated, ref, unref, useAttrs, watch, toRaw, reactive } from 'vue';
import screenfull from 'screenfull';

import CurdSearch from './components/CurdSearch.vue';
import CurdHeader from './components/CurdHeader.vue';
import CurdPagination from './components/CurdPagination.vue';
import { useRender } from './hooks/useRender.jsx';
import { useSelection } from './hooks/useSelection.js';
import { useDraggable } from './hooks/useDraggable.jsx';
import { useColSetting } from './hooks/useColSetting';
import CurdFilterPanel from './components/CurdFilterPanel.vue';

defineOptions({
  name: 'FuniCurdV2',
  inheritAttrs: false
});

// Props
const props = defineProps({
  // 高级查询
  isShowSearch: { type: Boolean, default: false },
  searchConfig: { type: Object, default: () => ({}) },
  queryFields: { type: Object, default: () => [] },
  sortFields: { type: Object, default: () => [] },
  columnFilters: { type: Object, default: () => [] },
  colNumber: { type: Number, default: 4 },
  /** 是用4.0模式高级查询 */
  useSearchV2: { type: Boolean, default: true },

  // 分页
  pagination: { type: Boolean, default: true },
  pageSizes: Array,
  defaultPage: { type: Object, default: () => ({ pageSize: 10, pageNo: 1 }) },

  // 数据
  data: { type: Array, default: () => [] },
  lodaData: Function,
  loading: Boolean,
  reloadOnActive: Boolean,

  // Table配置
  rowKey: { type: [String, Function], default: 'id' },
  columns: { type: Array, default: () => [] },
  settings: { type: Object, default: () => ({}) },
  teleported: Boolean,
  scrollbarAlwaysOn: { type: Boolean, default: true },
  draggable: { type: Boolean, default: false },
  useTools: { type: Boolean, default: false },

  // 行点击
  rowSelection: {
    type: [String, Boolean],
    default: 'click',
    validator: val => !val || ['click', 'dblclick'].includes(val)
  },
  checkOnRowClick: Boolean,

  fixedButtons: Boolean,
  actionsProps: Object
});

// Emits
const emit = defineEmits([
  'get-curd',
  'beforeRequest',
  'afterRequest',
  'requestError',
  'row-click',
  'row-dblclick',
  'draggableEnd',
  'sort-change',
  'col-setting-change'
]);

// Data
const contentRef = ref();
const curdRef = ref();
const pageRef = ref();
const tableData = ref([]);
const dataTotal = ref(0);
const visibleData = ref([]);
const searchParams = ref([]);
const sortParams = ref([]);
const filterParams = ref([]);
const attrs = useAttrs();
const size = ref('default');
const stripe = ref(true);
const dynamicColumnMap = ref({});
const colSettings = ref([]);
const customFilters = reactive({});

// 表格配置
const tableAttrs = computed(() => ({
  border: true,
  size: size.value,
  tableLayout: 'auto',
  rowKey: props.rowKey,
  stripe: stripe.value,
  cellStyle: cellStyle,
  data: visibleData.value,
  showOverflowTooltip: true,
  cellClassName: cellClassName,
  scrollbarAlwaysOn: props.scrollbarAlwaysOn,
  ...attrs
}));

const filtersMap = computed(() => {
  return props.columnFilters.reduce((map, item) => {
    map[item.field] = {
      attribute: item.attribute,
      column: item.column,
      operateOption: item.operateOptions[0]?.value || 'BELONG',
      type: item.type,
      field: item.field
    };
    return map;
  }, {});
});

const computedColumns = computed(() => {
  const columns = $utils.clone(props.columns || [], true).filter(item => {
    const column = dynamicColumnMap.value[item.prop || item.label] || item;
    return column.hidden !== true;
  });

  if (props.draggable && !columns.some(column => column.prop.includes('draggable_'))) {
    columns.unshift(draggableColumn);
  }
  return columns.map(column => {
    if (column.type === 'selection') return Object.assign({}, column, { width: 40, slots: {} });

    if (column.type === 'radio') {
      return Object.assign({}, column, {
        width: 40,
        slots: {},
        type: 'radio',
        render: ({ row, index }) => (
          <el-radio
            onClick={e => e.preventDefault()}
            modelValue={rowSelected(row)}
            label={true}
            disabled={!rowSelectable(row)}
          >
            {' '}
          </el-radio>
        )
      });
    }

    const style = {};
    column.maxWidth && (style.maxWidth = `${column.maxWidth}px`);

    if (!!props.sortFields.find(i => i.field === column.prop)) {
      column.sortable = 'custom';
    } else {
      delete column.sortable;
    }

    delete column.filters;
    const customFilter = filtersMap.value[column.prop] || filtersMap.value[column.label];
    if (!!customFilter) {
      column.customFilter = customFilter;
      column.columnKey = customFilter.field;
      customFilters[customFilter.field] = customFilter;
    }

    return Object.assign({ slots: { header: `${column.prop}_header` }, showOverflowTooltip: true, style }, column);
  });
});

// hooks
const {
  currentRow,
  currentRowKey,
  handleCellClick,
  handleCellDoubleClick,
  rowSelectable,
  toggleSelection,
  setCurrentRow,
  setCurrentRowByKey,
  rowSelected,
  resetCurrentRow
} = useSelection({
  table: curdRef,
  datas: visibleData,
  columns: computedColumns,
  emit,
  rowKey: props.rowKey,
  rowSelection: props.rowSelection,
  checkOnRowClick: props.checkOnRowClick
});
const { draggableOptions, draggableColumn } = useDraggable(props, emit);
const { contentRender, defaultRenderCell } = useRender();

let skipReloadCount = 1;

watch(
  () => props.data,
  () => !props.lodaData && reload(),
  { deep: true }
);

watch(() => props.settings, setupColSettings, { immediate: true });

//watch curdRef
watch(curdRef, (newValue, oldValue) => {
  if (!oldValue && !!newValue) {
    emit('get-curd', unref(curdRef));
    !props.pagination && doRequest(props.defaultPage);
  }
});

onActivated(() => {
  if (skipReloadCount === 0) {
    const { refreshOnActive, resetPage = false } = history.state;
    (props.reloadOnActive || refreshOnActive) && reload({ resetPage });
  }
  skipReloadCount = Math.max(0, skipReloadCount - 1);
});

async function doRequest(page) {
  try {
    emit('beforeRequest');
    let list = props.data || [];
    let total;
    if (!!props.lodaData && $utils.isFunction(props.lodaData)) {
      // 分页参数
      const pageParams = { ...page};
      // 查询参数
      const queryParams = {...searchParams.value};
      const remoteData = await props.lodaData(pageParams, queryParams);
      list = remoteData.list || [];
      total = remoteData.total;
    }

    tableData.value = list || [];

    if (!props.pagination) {
      visibleData.value = unref(tableData);
    } else if ($utils.isNil(total)) {
      const start = (page.pageNo - 1) * page.pageSize;
      const offset = page.pageSize;
      visibleData.value = unref(tableData).slice(start, start + offset);
      dataTotal.value = unref(tableData).length;
    } else {
      visibleData.value = unref(tableData);
      dataTotal.value = total;
    }
    setCurrentRowByKey(unref(currentRowKey));
    emit('afterRequest', list);
  } catch (error) {
    emit('requestError', error);
    console.error('doRequest - ', error);
  }
}

function reload({ resetPage = true } = {}) {
  if (!props.pagination) {
    doRequest(props.defaultPage);
  } else if (resetPage) {
    resetCurrentRow();
    pageRef.value.resetPageIndex();
  } else {
    doRequest({ pageNo: pageRef.value.currentPage, pageSize: pageRef.value.pageSize });
  }
}

function handleSearch(params) {
  searchParams.value = params;
  reload();
}

function cellStyle({ row, column, rowIndex, columnIndex }) {
  return (
    computedColumns.value.find(item => {
      return (!!column.property && item.prop === column.property) || item.label === column.label;
    })?.style || {}
  );
}

function cellClassName({ row, column, rowIndex, columnIndex }) {
  const col = computedColumns.value.find(item => {
    return (!!column.property && item.prop === column.property) || item.label === column.label;
  });
  const autoWidth = col?.width ? '' : 'auto-width-cell';
  const interaction = col?.cellInteraction === false ? 'disabled-cell' : '';
  return [autoWidth, interaction].join(' ');
}

function setupColSettings() {
  if (!!props.settings.columns && !!props.settings.columns.length) {
    const settingsMap = props.settings.columns.reduce((map, item) => {
      map[item.prop || item.label] = item;
      return map;
    }, {});
    colSettings.value = $utils.clone(props.columns || [], true).map(item => {
      const column = settingsMap[item.prop || item.label] || item;
      return { ...item, hidden: column.hidden };
    });
    handleColSetting(colSettings.value);
  } else {
    colSettings.value = $utils.clone(props.columns || [], true);
  }
}

function handleColSetting(settings) {
  console.debug('col setting change', settings);
  const rawSettings = settings.map(setting => toRaw(setting));

  dynamicColumnMap.value = Object.fromEntries(
    Object.entries($utils.groupBy(rawSettings, i => i.prop || i.label)).map(([key, value]) => [key, value[0]])
  );
  const newColumnSettings = $utils.clone(props.columns || [], true).map(i => {
    const column = dynamicColumnMap.value[i.prop || i.label];
    return { ...i, hidden: column.hidden };
  });
  emit('col-setting-change', newColumnSettings);
}

const handleFullscreen = () => screenfull.toggle(contentRef.value);
const handleResize = val => (size.value = val);
const handleStripe = () => (stripe.value = !stripe.value);

// 排序
const handleSortChange = ({ column, order, prop }) => {
  const sorters = [];
  if (!!order) {
    const sortField = props.sortFields.find(item => item.field === prop);
    sorters.push({ field: sortField.column, sort: order.replace('ending', '') });
  }
  sortParams.value = sorters;
  reload({ resetPage: false });
};

// 表头筛选
const handleFilterChange = () => {
  const { columns, filters } = curdRef.value.store.states;
  const columnGroup = $utils.groupBy(unref(columns), 'id');
  filterParams.value = Object.entries(unref(filters))
    .filter(([_, value]) => !!value.length)
    .map(([id, value]) => {
      const column = (columnGroup[id] || [])[0] || {};
      const filter = filtersMap.value[column.property] || filtersMap.value[column.label];
      return { column: filter.column, value: value.join(','), operate: filter.operateOption };
    });

  reload();
};

defineExpose({
  ref: curdRef,
  reload,
  doRequest,
  tableData,
  visibleData,
  currentRow,
  resetCurrentRow,
  setCurrentRow,
  setCurrentRowByKey,
  toggleSelection
});
</script>

<style lang="scss" scoped>
.funi-curd__wrap-v2 {
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  &.teleported {
    height: 100%;
  }

  .funi-curd__search {
    flex-shrink: 0;
    margin-bottom: 12px;
    border-radius: 0 0 4px 4px;
    overflow: hidden;
  }

  .funi-curd {
    flex-grow: 1;
    padding: 0px 16px 10px;

    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 4px 4px 0 0;

    &__header {
      flex-shrink: 0;
      // padding-left: 12px;
    }

    &__table {
      flex-grow: 1;
      overflow-y: auto;

      :deep(.el-table) {
        height: 100%;
        .el-popper {
          max-width: 50vw;
        }
      }
    }

    &__pagination {
      flex-shrink: 0;
    }

    .draggable-handle {
      cursor: grab;
    }
  }
}
</style>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as *;

:deep(.funi-curd__table) {
  @include b(table) {
    @include m(border) {
      &::before {
        width: var(--funi-curd-border-around-width);
      }

      &::after {
        width: var(--funi-curd-border-around-width);
      }

      .#{$namespace}-table__cell {
        border-right: var(--funi-curd-border-right) !important;
        &.el-table-fixed-column--right {
          &:not(.is-first-column) {
            padding-left: 2px !important;
          }
          &.is-first-column {
            box-shadow: inset 1px 0px 0px rgba(232, 234, 236, 1);
            &:before {
              left: 0px;
              box-shadow: -4px 0px 8px rgba(65, 58, 58, 0.11);
            }
          }

          & .cell {
            width: auto !important;
          }
        }

        &.auto-width-cell:not(.#{$namespace}-table-fixed-column--right) .cell {
          width: 100% !important;
        }

        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: keep-all;
        }
      }
    }

    th.#{$namespace}-table__cell {
      @include set-css-var-value('table-header-bg-color', rgba(248, 248, 249, 1));
      background-color: getCssVar('table', 'header-bg-color') !important;
    }

    @include e(border-left-patch) {
      width: 0px;
    }

    @include e(body-wrapper) {
      .#{$namespace}-scrollbar__bar.is-vertical {
        display: none;
      }
    }
  }
}
</style>

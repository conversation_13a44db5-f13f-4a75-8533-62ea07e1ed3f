// 公共请求
const apiUrlCommon = {
  downloadImportTemplate: '/lfpra/common/downloadImportTemplate', //导入模板下载
  queryOwnerShipInfoList: '/lfpra/ownershipInfo/queryOwnerShipInfoList', //列表查询权属信息结果集
  delete: '/lfpra/ownershipInfo/delete',
  dictList: '/lfpra/common/dictList',
  getOperateLog: '/lfpra/common/getOperateLog',
  getUnderEmployerInfo:'/lfpra/underInfo/getUnderEmployerInfo',//根据发包方编码-查询发包方详情
  getUnderContractorInfo:'/lfpra/underInfo/getUnderContractorInfo'//根据承包方编码-查询承包方详情
};

//列表查询权属信息结果集
export const queryOwnerShipInfoListtHttp = params => {
  return $http.post(apiUrlCommon.queryOwnerShipInfoList, params);
};
// 移除权属信息
export const deleteInfoHttp = params => {
  return $http.post(apiUrlCommon.delete, params);
};
// export const findByEmployeeHttp = params => {
//   return $http.post(apiUrl.findByEmployee, params);
// };
// export const monthInvestmentNewHttp = params => {
//   return $http.post(apiUrl.monthInvestmentInfoNew, params);
// };
// 字典下拉框
export const dictListHttp = params => {
  return $http.post(apiUrlCommon.dictList, params);
};

// 获取工作信息
export const getOperateLogHttp = params => {
  return $http.post(apiUrlCommon.getOperateLog, params);
};

export const getUnderEmployerInfoHttp = params => {
  return $http.fetch(apiUrlCommon.getUnderEmployerInfo, params);
};
export const getUnderContractorInfoHttp = params => {
  return $http.fetch(apiUrlCommon.getUnderContractorInfo, params);
};
export { apiUrlCommon };

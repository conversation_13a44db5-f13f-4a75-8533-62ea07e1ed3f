//承包地信息

import { ElNotification } from 'element-plus'
/**
 * 获取承包地列表
 * @param {*} data 
 */
export const contractList = (data) => {
    return $http.post("/contract/list", data)
}
/**
 * 添加承包地
 * @param {*} data 
 */
export const contractManage = (data) => {
    return $http.post("/contract/manage", data)
}
/**
 * 删除承包地
 * @param {*} data 
 */
export const contractRemove = (data) => {
    return $http.post("/contract/remove", data).then(res => {
        ElNotification({
            title: '删除成功',
            type: 'success',
        });
        return res
    })
}
/**
 * 获取承包地信息
 * @param {*} data 
 */
export const contractInfo = (data,config) => {
    return $http.fetch("/contract/info", data,config)
}
/**
 * 查询户拥有的承包流转信息
 * @param {*} data 
 */
export const queryByHousehold = (data) => {
    return $http.fetch("/contract/queryByHousehold", data)
}

/**
 * 确认签署完成
 * @param {*} data 
 */
export const sureSign = (data) => {
    return $http.post("/contract/sureSign", data)
}
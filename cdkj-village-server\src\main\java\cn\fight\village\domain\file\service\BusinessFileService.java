package cn.fight.village.domain.file.service;


import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.user.entity.User;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 业务文件服务层
 */
public interface BusinessFileService {

    /**
     * 文件上传
     * @return
     */
    JsonResult uploadFile(User user, MultipartFile File) throws IOException;

    /**
     * 文件下载
     * @param fileStoreId
     * @param response
     */
    void downloadFile(String fileStoreId, HttpServletResponse response);
}

<template>
  <div>
    <funi-dialog v-model="dialogVisible" :title="title" size="small">
      <div class="uploadBox">
        <div class="uploadBox__func">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :accept="accept"
            :auto-upload="true"
            :http-request="httpRequest"
            v-loading="loading"
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              <div class="upload__text">点击选择或拖动文件到此区域上传</div>
              <div class="tips">
                <div>1.请上传 {{ accept }} 文件</div>
                <div>2.请上传小于10M文件</div>
              </div>
            </div>
          </el-upload>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted, watch } from 'vue';
import { ElNotification } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';

const loading = ref(false);
const emit = defineEmits(['updateCurd']);
const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  callbackFun: {
    type: Function,
    default: () => {}
  },
  title: {
    type: String,
    default: '导入'
  },

  onlyUpload: {
    type: Boolean,
    default: false
  },
  accept: {
    type: String,
    default: '.jpeg,.jpg,.png,.pdf'
  }
});
const id = ref('');

let dialogVisible = ref(false);
const httpRequest = async options => {
  const formData = new FormData();
  formData.append('file', options.file);
  if (props.url) {
    try{
      loading.value = true
    let res = await $http.upload(`${props.url}?pid=${id.value}&uuid=${sessionStorage.getItem('c_id')}`, formData);
    if (res && res.fileName) {
      ElNotification({
        title: '成功',
        message: `${res.fileName}${props.onlyUpload ? '上传' : '导入'}成功`,
        type: 'success'
      });
    } else {
      ElNotification({
        title: '成功',
        message: '导入成功',
        type: 'success'
      });
    }
    loading.value = false
    props.callbackFun(res);
    }catch(err){
      loading.value = false
    }
    dialogVisible.value = false;
    return Promise.resolve({});
  }
};

const show = e => {
  id.value = e || '';
  // dom && dom.click();
  dialogVisible.value = true;
};
defineExpose({
  show
});
</script>
<style scoped>
@import url(./upload.css);
</style>

<template>
  <el-date-picker
    style="width: 100%"
    :type="dateType"
    placeholder="请选择"
    :value-format="attribute.valueFormat || 'YYYY-MM-DD'"
    range-separator="~"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
  />
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  operate: String,
  attribute: { type: Object, default: () => ({}) }
});

const dateType = computed(() => {
  return props.attribute.dateType || (props.operate === 'BETWEEN' ? 'daterange' : 'date');
});
</script>

<template>
  <div class="funi-layout__collapse" @click="store.toggleCollapse">
    <el-icon :size="18" :color="color" class="cursor-pointer">
      <Expand v-if="store.collapse" />
      <Fold v-else />
    </el-icon>
  </div>
</template>

<script setup>
import { useLayoutStore } from '../useLayoutStore';
const props = defineProps({
  color: { type: String, default: '' }
});

const store = useLayoutStore();
</script>

<style lang="less" scoped>
.funi-layout__collapse {
  display: flex;
  padding: 0 10px;
  cursor: pointer;
  align-items: center;
  transition: background var(--transition-time-02);
}
</style>

class funiTdtMap {
    //地图默认配置
    defaultConfig = {
        zoom: 12
    }
    //绘制对象
    drawHandler = ""
    //绘制完成覆盖物数组
    drawArr = []
    //地图key
    mapKey = "e1e01101b31263d98096970e8544eda9"
    //事件监听对象
    eventObj = {}
    //初始化
    constructor(domId, mapConfig = {}, mapKey) {
        Object.assign(this.defaultConfig, mapConfig)
        this.mapKey = mapKey || this.mapKey;
        this.map = new T.Map(domId);
        if (!mapConfig.center) {
            this.geoLocation()
            this.map.centerAndZoom(new T.LngLat(116.40969, 38.89945), this.defaultConfig.zoom);
        }
        else {
            this.map.centerAndZoom(new T.LngLat(mapConfig.center[0], mapConfig.center[1]), this.defaultConfig.zoom);
        }
        //创建对象
        var ctrl = new T.Control.MapType();
        //添加控件
        this.map.addControl(ctrl);
        let style = document.createElement("style")
        style.innerHTML = ".tdt-noDataTips{ display:none }"
        document.head.appendChild(style)
    }
    //获取地图实例
    getMapInstance() {
        return this.map
    }
    //定位
    geoLocation() {
        let fn = () => {
            var lo = new T.Geolocation({ enableHighAccuracy: true });
            let that = this;
            const fn = function (e) {
                if (this.getStatus() == 0) {
                    that.map.centerAndZoom(e.lnglat, that.defaultConfig.zoom);
                    var marker = new T.Marker(e.lnglat);
                    that.map.addOverLay(marker);
                }
            }
            lo.getCurrentPosition(fn);
        }
        if (T.Geolocation) {
            fn()
        }
        else {
            setTimeout(() => {
                fn()
            }, 300)
        }
    }
    //点绘制
    initDrawMarkerTool(config = { follow: true }, clickObj = {}) {
        if (this.drawHandler) this.drawHandler.close();
        this.drawHandler = new T.MarkTool(this.map, config);
        this.drawHandler.on("mouseup", e => {
            this.drawArr.push(e.currentMarker)
            setTimeout(() => {
                this.emit("selectOverlay", "")
                //无点击事件，内部执行 for  选中
                if (!clickObj.click) {
                    clickObj.click = () => {
                        this.map.panTo(e.currentMarker.getLngLat())
                        this.emit("selectOverlay", e.currentMarker)
                    }
                }
                for (const key in clickObj) {
                    e.currentMarker.on(key, event => clickObj[key](event, e.currentMarker))
                }
            }, 0)
        })
        this.drawHandler.open();
    }
    //线绘制
    initDrawPolylineTool(config = { follow: true }, clickObj = {}) {
        if (this.drawHandler) this.drawHandler.close();
        this.drawHandler = new T.PolylineTool(this.map, config);
        this.drawHandler.on("addpoint", e => {
            this.currentSelectOverlay = e.currentPolyline
            e.currentPolyline.defaultColor= e.currentPolyline.defaultColor || e.currentPolyline.getColor()
            this.emit("selectOverlay", e.currentPolyline)
            e.currentPolyline.disableEdit()
            e.currentPolyline.enableEdit()
        })
        this.drawHandler.on("draw", e => {
            if (!e.currentPolyline.bindClick) {
                e.currentPolyline.bindClick = true
                //无点击事件，内部传递选中
                if (!clickObj.click) {
                    clickObj.click = () => {
                        this.emit("selectOverlay", e.currentPolyline)
                    }
                }
                for (const key in clickObj) {
                    e.currentPolyline.on(key, event => clickObj[key](event, e.currentPolyline))
                }
            }
            e.currentPolyline.defaultColor=e.currentPolyline.defaultColor || e.currentPolyline.getColor()
            e.currentPolyline.disableEdit()
            this.drawArr.push(e.currentPolyline)
        })
        this.drawHandler.open();
    }
    //矩形绘制（仅PC端支持）
    initDrawRectangleTool(config = { follow: true }, clickObj = {}) {
        if (this.drawHandler) this.drawHandler.close();
        this.drawHandler = new T.RectangleTool(this.map, config);
        this.drawHandler.open();
    }
    //多边形绘制
    initDrawPolygonTool(config = { follow: true }, clickObj = {}) {
        if (this.drawHandler) this.drawHandler.close();
        this.drawHandler = new T.PolygonTool(this.map, config);
        this.drawHandler._onlongpress = () => { }
        this.drawHandler.on("addpoint", e => {
            this.currentSelectOverlay = e.currentPolygon
            e.currentPolygon.defaultColor=e.currentPolygon.defaultColor || e.currentPolygon.getColor()
            this.emit("selectOverlay", e.currentPolygon)
            e.currentPolygon.disableEdit()
            e.currentPolygon.enableEdit()
        })
        this.drawHandler.on("draw", e => {
            if (!e.currentPolygon.bindClick) {
                e.currentPolygon.bindClick = true
                //无点击事件，内部传递选中
                if (!clickObj.click) {
                    clickObj.click = () => {
                        this.emit("selectOverlay", e.currentPolygon)
                    }
                }
                for (const key in clickObj) {
                    e.currentPolygon.on(key, event => clickObj[key](event, e.currentPolygon))
                }
            }
            e.currentPolygon.defaultColor=e.currentPolygon.defaultColor || e.currentPolygon.getColor()
            e.currentPolygon.disableEdit()
            this.drawArr.push(e.currentPolygon)
        })

        this.drawHandler.open();
    }
    //绘制结束
    drawEnd() {
        if(this.drawHandler.endDraw){
            this.drawHandler.endDraw()
        }
    }
    //清除覆盖物
    clearOverlay(overlay) {
        this.map.removeOverLay(overlay)
    }
    //移除上一次绘制物
    drawRemove() {
        if (!this.drawArr.length) return;
        let overlay = this.drawArr.splice(this.drawArr.length - 1, 1)?.[0];
        this.map.removeOverLay(overlay)
    }
    //自定义事件监听
    on(eventName, callback) {
        this.eventObj[eventName] = callback
    }
    //触发自定义事件
    emit(eventName, overlay) {
        this.eventObj[eventName] && this.eventObj[eventName](overlay)
    }
    //获取绘制覆盖物
    getDrawOverlay(){
        return this.drawArr;
    }
    //添加图层
    addTileLayer(imageURL) {
        //默认为天地图影像
        if (!imageURL) {
            // https://t7.tianditu.gov.cn/img_w/wmts?service=WMTS&version=1.0.0&request=GetTile&tilematrix=18&layer=img&style=default&tilerow=97771&tilecol=208466&tilematrixset=w&format=tiles&tk=789e558be762ff832392a0393fd8a4f1
            imageURL = "http://t0.tianditu.gov.cn/img_w/wmts?" +
                "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&tilematrix=18&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
                "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=" + this.mapKey;
            let textUrl = "http://t0.tianditu.gov.cn/cia_w/wmts?" +
                "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&tilematrix=18&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
                "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=" + this.mapKey;
            //创建自定义图层对象
            let lay = new T.TileLayer(textUrl, { minZoom: 1, maxZoom: 18 });
            //将图层增加到地图上
            setTimeout(() => {
                this.map.addLayer(lay);
            }, 0)

        }
        //创建自定义图层对象
        var lay = new T.TileLayer(imageURL, { minZoom: 1, maxZoom: 18 });
        //将图层增加到地图上
        this.map.addLayer(lay);
        return lay;
    }
    //移除指定图层
    removeLayer(layer) {
        this.map.removeLayer(layer);
    }
    //移除所有图层
    removeAllLayer() {
        this.map.clearLayers();
    }
    //初始化图斑数据
    initGisLayer(geoJson, callback) {
        geoJson.features.forEach(item => {
            item.geometry.coordinates.forEach(coords => {
                coords.forEach(coord => {
                    let lngLatArr = coord.map(x => new T.LngLat(x[0], x[1]))
                    let polygon;
                    if (callback) {
                        polygon = callback(lngLatArr, item, this.map)
                    }
                    else {
                        polygon = new T.Polygon(lngLatArr, { color: "blue", weight: 2, opacity: 0.5 })
                    }
                    this.map.addOverLay(polygon)
                })
            })
        });
    }
}

export default funiTdtMap
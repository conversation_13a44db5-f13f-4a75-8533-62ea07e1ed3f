<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.user.repository.UserRepository">

    <update id="logicDeleteById">
        update public.rlams_user
        set deleted = 1
        where uuid = #{uuid} and deleted = 0
    </update>

    <select id="selectUserList" resultType="cn.fight.village.domain.user.vo.UserVo">
        select
            t.uuid,
            t.username,
            t.account,
            user_type userType
        from public.rlams_user t
        where t.deleted = 0 and t.uuid != '1'
        <if test="keyword != null and keyword !=''">
            and (
                t.username like concat('%', #{keyword},'%')
                or t.account = #{keyword}
            )
        </if>
        <if test="username != null and username != ''">
            and t.username like concat('%', #{username},'%')
        </if>
        <if test="account != null and account != ''">
            and t.account = #{account}
        </if>
        order by t.create_time desc
    </select>
</mapper>
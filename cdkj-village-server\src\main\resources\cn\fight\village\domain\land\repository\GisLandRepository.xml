<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.land.repository.GisLandRepository">
    <select id="selectProtocolLands" resultType="cn.fight.village.domain.contract.entity.TransProtocolLand">
        select
        l.dkmc landName,
        l.dkbm landNo,
        l.dkdz east,
        l.dknz south,
        l.dkxz west,
        l.dkbz north,
        l.scmj area,
        l.dldj quality,
        cbl.land_type landType,
        ud.contract_cret_no contractCode,
        up.upper village
        from public.lyr_dk l
        left join public.rlams_land cbl on l.dkbm = cbl.land_no and cbl.deleted = 0
        left join public.rlams_contract cbc on cbc.uuid = cbl.contract_id and cbc.deleted = 0 and cbc.type = '家庭承包'
        left join public.rlams_under ud on ud.contract_id = cbc.uuid and ud.deleted = 0
        left join public.rlams_upper up on up.contract_id = cbc.uuid and up.deleted = 0
        where l.dkbm in
        <foreach collection="landNoList" open="(" item="item" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getNextGid" resultType="java.lang.Integer">
        SELECT MAX(gid) + 1 FROM public.lyr_dk;
    </select>

    <update id="updateGeo">
        update public.lyr_dk
        set geom = ST_Transform(
                ST_GeomFromText(#{geomStr},4490),4523
        )
        where dkbm = #{dkbm}
    </update>

    <update id="landSign">
        update public.lyr_dk
        set sflz = 1
        where dkbm in
        <foreach collection="dkbms" separator="," open="(" close=")" item="dkbm">
            #{dkbm}
        </foreach>
    </update>

    <delete id="deleteGisLand">
        delete from public.lyr_dk
        where dkbm = #{dkbm}
    </delete>

</mapper>
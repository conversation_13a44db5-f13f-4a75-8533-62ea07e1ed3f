import { ref, reactive, defineComponent, watch } from 'vue';
import styles from './fileStyle.module.less';
import Hyperlink from '@/apps/lfpra/common/components/hyperlinkTable/index.vue';
import folderIamge from '@/apps/lfpra/ePedigree/assets/image/folder_big.png'
export default {
  props: {
    // 遍历后的每条数据
    item: {
      type: Object,
      default: function () {
        return {};
      }
    },
    // 点击文件夹回调
    treeClick: {
      type: Function,
      default: function () {
        return () => {};
      }
    },
    // 操作项
    btns: {
      type: Array,
      default: []
    },
    headBtnClick: {
      type: Function,
      default: function () {
        return () => {};
      }
    }
  },
  setup(props) {
    let { item, treeClick, headBtnClick } = props;
    // const data = ref();
    const showButton = ref(true);
    const isShowTooltip = ref(true);

    // const controlsData = ref();
    let dataList = reactive({
      data: {},
      controlsData: []
    });

    watch(
      () => props.item,
      () => {
        dataList.data = props.item;
        // dataList.controlsData = props.btns;
        dataList.controlsData = [];
        props.btns.forEach(item => {
          props.item.buttonEnums.forEach(itemChild => {
            if (item.key == itemChild) {
              dataList.controlsData.push(item);
            }
          });
        });
      },
      { immediate: true }
    );
    const moreBtnRender = (btnList, data, headBtnClick) => {
      if (btnList.length < 4) {
        return btnList.map((v, i) => (
          <span
            onClick={e => {
              e.stopPropagation();
              headBtnClick(data, v.key);
            }}
          >
            {v.label}
          </span>
        ));
      } else {
        let a = btnList.slice(0, 2),
          b = btnList.slice(2);
        return (
          <div class={styles.buttons}>
            {a.map((v, i) => (
              <span
                class={styles.btn_span}
                onClick={e => {
                  e.stopPropagation();
                  headBtnClick(data, v.key);
                }}
              >
                {v.label}
              </span>
            ))}
            <el-popover placement="bottom" popper-class={styles.popover_width}>
              {{
                default: (
                  <div style="display: flex;flex-direction: column;justify-content: space-around;align-items: center;">
                    {b.map((v, i) => (
                      <span
                        class={styles.popover_span}
                        onClick={e => {
                          e.stopPropagation();
                          headBtnClick(data, v.key);
                        }}
                      >
                        {v.label}
                      </span>
                    ))}
                  </div>
                ),
                reference: <Hyperlink text={'更多'}></Hyperlink>
              }}
            </el-popover>
          </div>
        );
      }
    };
    //文件名/文件夹名 文字溢出 显示提示框
    const visibilityChange = e => {
      const ev = e.target;
      const evWeight = ev.scrollWidth;
      const contentWeight = ev.clientWidth;
      if (evWeight > contentWeight) {
        // 实际宽度 > 可视宽度  文字溢出
        isShowTooltip.value = false;
      } else {
        // 否则为不溢出
        isShowTooltip.value = true;
      }
    };
    return {
      dataList,
      treeClick,
      showButton,
      headBtnClick,
      moreBtnRender,
      visibilityChange,
      isShowTooltip
    };
  },

  render() {
    let { data, controlsData } = this.dataList;
    return (
      <div
        class={[styles.controls, styles.container_item]}
        // onMouseenter={e => {
        //   e.stopPropagation();
        //   this.showButton = true;
        // }}
        // onMouseleave={e => {
        //   e.stopPropagation();
        //   this.showButton = false;
        // }}
      >
        <div>
          {/* fileType === 1 ? 文件 : 文件夹 */}
          {data.fileType && data.fileType == '2' ? (
            <section
              onClick={e => {
                e.stopPropagation();
                this.treeClick(data);
              }}
              class={[styles.container_file, styles.pointer]}
            >
              <div>
              <el-image src={folderIamge} style="width:48px;height:48px;"></el-image>
              </div>
              <el-tooltip effect="light" content={data.fileName} placement="top" disabled={this.isShowTooltip}>
                <p onMouseenter={this.visibilityChange} class={styles.container_file_title}>
                  {data.fileName}
                </p>
              </el-tooltip>
            </section>
          ) : (
            <div class={styles.container_file}>
              <div>
                <svg
                  t="1700105895668"
                  class="icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="6962"
                  width="48"
                  height="48"
                >
                  <path
                    d="M256.000102 768.000102h438.829825a36.713985 36.713985 0 0 1 34.099986 17.439994 36.699985 36.699985 0 0 1 0 38.299984 36.703985 36.703985 0 0 1-34.099986 17.439993H256.000102c-19.129992-1.459999-33.899986-17.399993-33.899986-36.589985 0.01-19.179992 14.779994-35.129986 33.899986-36.589986z m0-182.819926h219.519913c19.119992 1.459999 33.899986 17.399993 33.899986 36.579985s-14.769994 35.129986-33.899986 36.589985H256.000102c-19.129992-1.459999-33.899986-17.399993-33.899986-36.589985 0.01-19.179992 14.779994-35.119986 33.899986-36.579985zM653.869943 32.210397l264.959894 264.749894a110.114956 110.114956 0 0 1 31.999987 77.659969v539.729784c0 29.079988-11.549995 56.969977-32.109987 77.539969a109.684956 109.684956 0 0 1-77.539969 32.109987H182.830132c-60.559976 0-109.649956-49.08998-109.649956-109.649956v-804.699678C73.170176 49.09039 122.270156 0.00041 182.830132 0.00041h393.389842c29.149988-0.08 57.109977 11.519995 77.649969 32.209987z m-32.209987 71.259972v225.709909h225.69991l-225.69991-225.709909z m-60.589976-30.299988H182.830132c-20.099992 0.12-36.359985 16.379993-36.479986 36.479985v804.689678c0.12 20.099992 16.379993 36.359985 36.479986 36.479985h658.339736c20.149992 0 36.479985-16.329993 36.479986-36.479985v-511.999795h-255.999898c-40.329984-0.12-72.959971-32.849987-72.959971-73.169971v-255.999897h12.379995z m0 0"
                    p-id="6963"
                    fill="#e6e6e6"
                  ></path>
                </svg>
              </div>
              <el-tooltip effect="light" content={data.fileName} placement="top" disabled={this.isShowTooltip}>
                <p onMouseenter={this.visibilityChange} class={styles.container_file_title}>
                  {data.fileName}
                </p>
              </el-tooltip>
            </div>
          )}
        </div>
        <div class={styles.flex_box}>
          {this.showButton && (
            <div class={styles.buttons}>{this.moreBtnRender(controlsData, data, this.headBtnClick)}</div>
          )}
        </div>
      </div>
    );
  }
};

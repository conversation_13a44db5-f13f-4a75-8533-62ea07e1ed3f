package cn.fight.village.domain.homestead.value;

import cn.fight.village.domain.common.entity.BaseValue;

/**
 * 家庭宅基地视图镀锌
 *
 */
public class HouseholdHomesteadVo extends BaseValue {
    //宅基地编号
    private String code;

    //面积
    private Double area;

    //是否取证
    private String hasCert;

    //使用情况
    private String usage;

    //东
    private String east;

    //西
    private String west;

    //南
    private String south;

    //北
    private String north;

    private String point;

    public String getPoint() {
        return "东至：" + this.east + ";" +
                "西至：" + this.west + ";" +
                "南至：" + this.south + ";" +
                "北至：" + this.north;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getHasCert() {
        return hasCert;
    }

    public void setHasCert(String hasCert) {
        this.hasCert = hasCert;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getEast() {
        return east;
    }

    public void setEast(String east) {
        this.east = east;
    }

    public String getWest() {
        return west;
    }

    public void setWest(String west) {
        this.west = west;
    }

    public String getSouth() {
        return south;
    }

    public void setSouth(String south) {
        this.south = south;
    }

    public String getNorth() {
        return north;
    }

    public void setNorth(String north) {
        this.north = north;
    }
}

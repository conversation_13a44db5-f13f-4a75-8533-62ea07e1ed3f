import NumberInput from '@/apps/lfpra/common/components/numberInput/numberInput.vue';
import LetterNumInput from '@/apps/lfpra/common/components/letterNumInput/index.vue';
//select 自定义组件
const getSelectComponent = e => {
  return (
    <el-select
      style="width:100%"
      onChange={i => {
        e.onChange ? e.onChange(i) : {};
      }}
    >
      {e.list.map(item => (
        <el-option key={item[e.value]} label={item[e.label]} value={item[e.value]} />
      ))}
    </el-select>
  );
};
// 五级区划校验
const validateCommander = (rule, value, callback) => {
  if (value === '' || value === null || value === undefined || !Object.keys(value).length) {
    callback(new Error('必填'));
  } else if (value && Object.keys(value).length) {
    if (!value?.province?.value) {
      callback(new Error('省必填'));
    } else if (!value?.city?.value) {
      callback(new Error('市必填'));
    } else if (!value?.district?.value) {
      callback(new Error('区必填'));
    } else if (!value?.community?.value) {
      callback(new Error('街道必填'));
    } else if (!value?.street?.value) {
      callback(new Error('村委会必填'));
    } else if (!value?.other) {
      callback(new Error('组必填'));
    } else if (!value?.addressFull) {
      callback(new Error('详细地址必填'));
    }
    callback();
  } else {
    callback();
  }
};

// 表单配置
export const useSchema = params => {
  let arr = [
    {
      title: '发包方信息',
      list: [
        {
          label: '发包方代码',
          component: () => <LetterNumInput />,
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          on: {
            change: params.dicCardChange
          },
          prop: 'employerCode'
        },
        {
          label: '发包方名称',
          component: 'el-input',
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'employerName'
        },
        {
          label: '发包方负责人姓名',
          component: 'el-input',
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'employerHeadName'
        },
        {
          label: '发包方负责人联系电话',
          component: 'el-input',
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'empTelephone'
        },
        {
          label: '发包方证件类型',
          component: () => getSelectComponent({ list: params.cardType, value: 'code', label: 'name' }),
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'dicCardTypeCode'
        },
        {
          label: '发包方证件号码',
          component: 'el-input',
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'empCertificateNo'
        },
        {
          label: '备注',
          component: 'el-input',
          hidden: false,
          props: {
            placeholder: '请输入',
            maxlength: 200,
            type: 'textarea'
          },
          colProps: {
            span: 24
          },
          prop: 'remark'
        }
      ],
      rules: {
        employerCode: [{ required: true, message: '必填', trigger: 'change' }],
        employerName: [{ required: true, message: '必填', trigger: 'change' }],
        employerHeadName: [{ required: true, message: '必填', trigger: 'change' }], // 宅基地面积（㎡）
        dicCardTypeCode: [{ required: true, message: '必填', trigger: 'change' }], //
        empCertificateNo: [{ required: true, message: '必填', trigger: 'change' }],
        empTelephone: [
          {
            validator: (rule, value, callback) => {
              var reg_tel = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;
              if (value === '' || value === null || value === undefined || value.length == 0) {
                callback('必填');
              } else if (!reg_tel.test(value)) {
                callback('手机号格式不对');
              } else {
                callback();
              }
            },
            required: true,
            trigger: 'change'
          }
        ]
      }
    },
    {
      title: '承包方信息',
      list: [
        {
          label: '承包方代码',
          component: () => <LetterNumInput />,
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          on: {
            change: params.contractorChange
          },
          prop: 'contractorCode'
        },
        {
          label: '是否村民',
          component: () =>
            getSelectComponent({
              list: params.obtainType,
              value: 'value',
              label: 'label',
              onChange: params.isVillagerChange
            }),
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'isVillager'
        },
        {
          label: '承包方（代表）名称',
          component: 'el-input',
          props: {
            placeholder: '请输入',
            maxlength: 50,
            readonly: params.isVillager
          },
          on: {
            click: params.ownerNameChange
          },
          prop: 'contractorName'
        },
        {
          label: '承包方（代表）证件类型',
          component: params.isVillager
            ? null
            : () => getSelectComponent({ list: params.cardType, value: 'code', label: 'name' }),
          props: {
            placeholder: '请输入'
          },
          prop: params.isVillager ? 'dicCardTypeName' : 'dicCardTypeCode'
        },
        {
          label: '承包方（代表）证件号码',
          component: params.isVillager ? null : 'el-input',
          props: {
            placeholder: '请输入'
          },
          prop: 'cerCertificateNo'
        },
        {
          label: '承包方地址',
          component: params.isVillager ? null : () => <FuniRegion />,
          props: params.isVillager
            ? {
                placeholder: '请输入'
              }
            : {
                regionProps: {
                  province: {
                    disabled: true
                  },
                  city: {
                    disabled: true
                  },
                  district: {
                    disabled: true
                  }
                },
                extension: () => (
                  <el-input oninput="value=value.replace(/[^0-9]/g,'')" style="width:10%;flex: 0 0 auto">
                    {{
                      append: '组'
                    }}
                  </el-input>
                )
              },
          colProps: {
            span: 24
          },
          prop: 'address'
        },
        {
          label: '承包方（代表）联系电话',
          component: params.isVillager ? null : 'el-input',
          props: {
            placeholder: '请输入'
          },
          prop: 'cerTelephone'
        },
        {
          label: '承包经营权取得方式',
          component: () => getSelectComponent({ list: params.manageType, value: 'code', label: 'name' }),
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'dicCerManageCode'
        },
        {
          label: '土地承包用途',
          component: 'el-input',
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'contractingUse'
        },
        {
          label: '流转价款/股权分红（元）',
          component: () => <NumberInput />,
          props: {
            placeholder: '请输入',
            maxlength: 50
          },
          prop: 'shareOutBonus'
        },
        {
          label: '备注',
          component: 'el-input',
          props: {
            placeholder: '请输入',
            maxlength: 200,
            type: 'textarea'
          },
          colProps: {
            span: 24
          },
          prop: 'remark'
        }
      ],
      rules: {
        contractorCode: [
          { required: true, message: '必填' },
          {
            message: '请输入数字或大小写字母组成的编号',
            pattern: /^[a-zA-Z0-9]{1,50}$/,
            trigger: ['blur', 'change']
          }
        ],
        isVillager: [{ required: true, message: '必填', trigger: 'change' }],
        contractorName: params.isVillager ?{}:[{ required: true, message: '必填', trigger: 'change' }],
        cerTelephone: params.isVillager ?{}:[
          {
            validator: (rule, value, callback) => {
              var reg_tel = /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/;
              if (value === '' || value === null || value === undefined || value.length == 0) {
                callback('必填');
              } else if (!reg_tel.test(value)) {
                callback('手机号格式不对');
              } else {
                callback();
              }
            },
            required: true,
            trigger: 'change'
          }
        ],
        dicCardTypeCode: params.isVillager ?{}:[{ required: true, message: '必填', trigger: 'change' }],
        cerCertificateNo: params.isVillager ?{}:[{ required: true, message: '必填', trigger: 'change' }],
        address:params.isVillager ?{}: [{ required: true, validator: validateCommander, trigger: 'blur' }],
        contractingUse: [{ required: true, message: '必填', trigger: 'change' }],
        cerNumber: [{ required: true, message: '必填', trigger: 'change' }],
        awardCertArea: [{ required: true, message: '必填', trigger: 'change' }],
        dicCerManageCode: [{ required: true, message: '必填', trigger: 'change' }],
        shareOutBonus: [{ required: true, message: '必填', trigger: 'change' }]
      }
    }
  ];
  let detail = [
    { title: '', list: [] },
    { title: '', list: [] }
  ];
  // 查看详情 文本显示
  if (!params.isEdit) {
    arr.map((item, index) => {
      detail[index].title = item.title;
      item.list.map(itemChild => {
        let obj = {
          label: itemChild.label,
          prop: itemChild.prop,
          colProps: itemChild.colProps
        };
        if (['dicCardTypeCode', 'dicCerManageCode'].includes(itemChild.prop)) {
          obj.prop = itemChild.prop.replace('Code', 'Name');
        }
        if (itemChild.prop == 'isVillager') {
          obj.prop = 'isVillagerName';
        }
        detail[index].list.push(obj);
      });
    });
  }
  return params.isEdit ? arr : detail;
};

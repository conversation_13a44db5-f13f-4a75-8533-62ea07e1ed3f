<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.land.repository.RealLandRepository">

    <select id="getRealLandNoSec" resultType="java.lang.Integer">
        SELECT nextval('public.seq_real_land_no');
    </select>

    <select id="selectRealLandList" resultType="cn.fight.village.domain.land.entity.RealLandValue">
        select
            rl.uuid,
            rl.land_no landNo,
            rl.land_type landType,
            rl.contract,
            rl.area,
            rl.farming,
            rl.transfer,
            rl.new_created newCreated,
            c.householder,
            d.dkmc landName
        from public.rlams_real_land rl
        inner join public.rlams_real_land_chenbao c on rl.uuid = c.real_land_id
        left join public.lyr_dk d on d.dkbm = rl.land_no
                 <!--inner join public.rlams_land cl on rl.land_no = cl.land_no and cl.deleted = 0
                 inner join public.rlams_contract ct on ct.uuid = cl.contract_id and ct.deleted = 0
                 inner join public.rlams_household h on h.uuid = ct.household_id and h.deleted = 0
                 inner join public.rlams_member m on m.uuid = h.householder_id and m.deleted = 0-->
        where c.householder like '%' || #{underName} || '%' and rl.deleted = 0
    </select>

    <select id="selectLandsByProtocol" resultType="cn.fight.village.domain.contract.entity.Land">
        select
            l.land_type landType,
            l.land_no landNo,
            l.contract,
            l.farming,
            l.transfer,
            l.area,
            l.remark
        from  public.rlams_land_protocol p
        inner join public.rlams_protocol_land pl on pl.protocol_id = p.uuid
        inner join public.rlams_real_land l on l.land_no = pl.land_no and l.deleted = 0
        where p.uuid = #{uuid} and p.deleted = 0
    </select>

    <select id="getChenbaoInfo" resultType="java.util.Map">
        select
            real_land_id realLandId,
            household_id householdId,
            householder
        from public.rlams_real_land_chenbao
        where real_land_id = #{realLandId}
    </select>

    <insert id="insertChenbaoRelation">
        insert into public.rlams_real_land_chenbao(real_land_id, household_id,householder)
        values(#{realLandId},#{householdId},#{householder})
    </insert>

    <delete id="deleteChenbaoRel">
        delete from public.rlams_real_land_chenbao
        where real_land_id = #{landUuid}
    </delete>
</mapper>
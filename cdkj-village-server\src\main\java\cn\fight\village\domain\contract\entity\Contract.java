package cn.fight.village.domain.contract.entity;

import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 承包-流转合同信息
 *
 */
@TableName("public.rlams_contract")
public class Contract extends BaseEntity {
    //类型
    private String type;

    //合同编号
    private String contractNo;

    //家庭承包关联户ID
    private String householdId;

    //备注
    private String remark;

    //项目归属
    private String project;

    //是否签约；1是 空否
    private String signed;

    public String getSigned() {
        return signed;
    }

    public void setSigned(String signed) {
        this.signed = signed;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getHouseholdId() {
        return householdId;
    }

    public void setHouseholdId(String householdId) {
        this.householdId = householdId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

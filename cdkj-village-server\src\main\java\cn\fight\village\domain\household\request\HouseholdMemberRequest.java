package cn.fight.village.domain.household.request;

import cn.fight.village.domain.common.entity.BaseRequest;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 家庭成员请求对象
 *
 */
public class HouseholdMemberRequest extends BaseRequest {
    //姓名
    private String  name;

    //性别
    private String  gender;

    //证件类型
    private String  idType;

    //证件号码
    private String  idCode;

    //联系电话
    private String  phone;
    private String  phoneSec;

    //家庭关系
    private String  relation;

    //是否房主
    private Integer  householder;

    //是否集体经济组织成员
    private String  collMember;

    //是否残疾人
    private String disabled;

    //集体组
    private String  collGroup;

    //是否户口
    private String huko;

    //是否退伍军人
    private String veteran;

    //备注
    private String remark;

    //备注时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date remarkTime;

    //备注原因
    private String remarkReason;

    //其他备注
    private String other;

    public String getDisabled() {
        return disabled;
    }

    public void setDisabled(String disabled) {
        this.disabled = disabled;
    }

    public String getCollGroup() {
        return collGroup;
    }

    public void setCollGroup(String collGroup) {
        this.collGroup = collGroup;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneSec() {
        return phoneSec;
    }

    public void setPhoneSec(String phoneSec) {
        this.phoneSec = phoneSec;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public Integer getHouseholder() {
        return householder;
    }

    public void setHouseholder(Integer householder) {
        this.householder = householder;
    }

    public String getCollMember() {
        return collMember;
    }

    public void setCollMember(String collMember) {
        this.collMember = collMember;
    }

    public String getHuko() {
        return huko;
    }

    public void setHuko(String huko) {
        this.huko = huko;
    }

    public String getVeteran() {
        return veteran;
    }

    public void setVeteran(String veteran) {
        this.veteran = veteran;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getRemarkTime() {
        return remarkTime;
    }

    public void setRemarkTime(Date remarkTime) {
        this.remarkTime = remarkTime;
    }

    public String getRemarkReason() {
        return remarkReason;
    }

    public void setRemarkReason(String remarkReason) {
        this.remarkReason = remarkReason;
    }

    public String getOther() {
        return other;
    }

    public void setOther(String other) {
        this.other = other;
    }
}

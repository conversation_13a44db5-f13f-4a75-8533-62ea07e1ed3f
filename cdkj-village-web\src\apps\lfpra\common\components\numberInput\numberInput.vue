<template>
  <el-input
    v-model="inputValues"
    :onKeypress="handleKeyPress"
    placeholder="请输入数字"
    @blur="handleBlur"
    type="number"
    @wheel.prevent
  />

</template>

<script setup>
import { ref } from 'vue';
let inputValues = ref('');
const handleKeyPress = event => {
  let reg = /^\d+\.\d{2}$/;
  if (reg.test(event.target.value.toString())) {
    event.preventDefault();
  }
  const inputValue = inputValues.value.trim() || event.target.value.trim();
  if (event.key === 'Enter') {
  } else if (
    (event.keyCode < 48 || event.keyCode > 57) && // 不是数字键
    event.key !== '.' && // 不是小数点
    event.keyCode !== 8  // 不是退格键
  ) {
    event.preventDefault(); // 阻止输入
  } else if (
    (event.key === '.' && (inputValue.includes('.') || inputValue.length === 0)) || // 已经有小数点或者尚未输入数字
    (event.key !== '.' && inputValue.includes('.') && inputValue.split('.')[1].length >= 2) || // 小数位数已经超过2位
    (event.key === '0' && inputValue.length === 1 && inputValue === '0') // 已经输入0
  ) {
    event.preventDefault(); // 阻止输入
  }
};
// 自动补齐两位小数
const handleBlur = () => {
  if (!inputValues.value) return;
  const inputValue = inputValues.value.trim();
  if (!inputValue.includes('.')) {
    inputValues.value += '.00';
  } else {
    const decimalPart = inputValue.split('.')[1];
    if (decimalPart.length === 0) {
      inputValues.value += '00';
    } else if (decimalPart.length === 1) {
      inputValues.value += '0';
    }
  }
};
</script>
<style lang="less" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
::v-deep input[type='number'] {
  -moz-appearance: textfield !important;
}
</style>

<template>
  <div class="main">
    <openLayerMap
      ref="openLayerMapRef"
      :patternSpotCallBack="patternSpotCallBack"
      :geoJson="null"
      :content="content"
      @init="initMap"
    >
      <template #search>
        <div>
          <searchCard :searchCallBack="searchCallBack" :searchNameCallBack="searchNameCallBack" />
        </div>
      </template>
      <template #rightMasking>
        <div>
          <collect :searchForm="searchForm" :geoList="geoList" />
        </div>
      </template>
    </openLayerMap>
  </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue';
import { queryPatternListApi } from './api/index';
import openLayerMap from '@/apps/lfpra/common/components/openLayerMap/index.vue';
import searchCard from './components/searchCard.vue';
import collect from './components/collect.vue';

import { GeoJSON, WFS } from 'ol/format.js';
import { and as andFilter, equalTo as equalToFilter, like as likeFilter } from 'ol/format/filter.js';
import { TileWMS, OSM, Vector as VectorSource } from 'ol/source';
import { Tile as TileLayer, Vector as VectorLayer, Image as ImageLayer } from 'ol/layer.js';
import { Style, Stroke, Fill } from 'ol/style';

import { or, equalTo } from 'ol/format/filter';
import { ElNotification } from 'element-plus';
const content = ref();
const openLayerMapRef = ref();
let searchForm = ref(['3', '1', '2']); // 搜索数据
let geoList = ref(null); // 图斑
let contentObj = {
  cbdContent: `<div>
            <h1>承包地</h1>
            <article style='width:300px'>
                <section style="display: flex;justify-content: space-between;">
                    <div>地块编号：<span>CBD2023010001</span></div>
                    <div>面积：<span>1.21亩</span></div>
                </section>
                <section style="display: flex;justify-content: space-between;">
                    <div>是否承包：<span>是</span></div>
                    <div>土地类型：<span>耕种</span></div>
                </section>
                <section style="display: flex;justify-content: space-between;">
                    <div>承包方名称：<span>牛大方</span></div>
                    <div>种植情况：<span>在种</span></div>
                </section>
            </article>
            <div style="color:#007fff">查看详情</div>
        </div> `,
  ldContent: `<div>
            <h1>林地</h1>
            <article style='width:300px'>
                <section style="display: flex;justify-content: space-between;">
                    <div>林地编号：<span>CBD2023010001</span></div>
                    <div>面积：<span>1.21亩</span></div>
                </section>
                <section style="display: flex;justify-content: space-between;">
                    <div>权利人姓名：<span>牛大方</span></div>
                </section>
            </article>
            <div style="color:#007fff">查看详情</div>
        </div> `,
  zjdContent: `<div>
            <h1>宅基地</h1>
            <article style='width:300px'>
                <section style="display: flex;justify-content: space-between;">
                    <div>宅基地编号：<span>CBD2023010001</span></div>
                    <div>面积：<span>1.21平方米</span></div>
                </section>
                <section style="display: flex;justify-content: space-between;">
                    <div>使用人名称：<span>找油田</span></div>
                    <div>使用状态：<span>耕种</span></div>
                </section>
            </article>
            <div style="color:#007fff">查看详情</div>
        </div> `
};
const patternIncludes = () => {
  const arr = ['3', '1', '2'];
  let isShow = arr.some(item => searchForm.value.includes(item));
  return isShow;
};
// 获取图斑数据
const getList = async () => {
  if (!patternIncludes()) return;
  let data = await queryPatternListApi({ patternTypes: searchForm.value });
  geoList.value = data;
};

onMounted(() => {
  getList();
});
const vectorSource = new VectorSource();
const vector = new VectorLayer({
  source: vectorSource,
  style: new Style({
    fill: new Fill({ color: '#F08611' })
  })
});

// 图斑回调
const patternSpotCallBack = res => {
  console.log(res);
  content.value = contentObj['cbdContent'];
};
// 搜索回调
const searchCallBack = res => {
  searchForm.value = res;
  let layers = openLayerMapRef.value.getWmsLayer();
  layers.forEach(item => {
    item?.setVisible(false);
  });
  res.forEach(item => {
    layers[item - 1]?.setVisible(true);
  });
};

const searchNameCallBack = async (res, spot, type) => {
  // 重置操作
  if (type == 'reset') {
    if(vectorSource.getExtent()[0] !== Infinity){
     openLayerMapRef.value.map.getView().fit(vectorSource.getExtent(), {
      maxZoom: 15
    });
    openLayerMapRef.value.closeOverLay()
    vectorSource.clear();
  }
  }
  let data = await queryPatternListApi({ holderName: res?.name, groupNumber: res?.zh, patternTypes: searchForm.value });
  geoList.value = data;
  let filters = Object.keys(res)
    .map(item => {
      if (res[item]) {
        return equalTo(item, res[item]);
      }
    })
    .filter(Boolean);

  let enumerate = [
    {
      label: 'fragment_zjd',
      value: '3'
    },
    {
      label: 'fragment_cbd',
      value: '1'
    },
    {
      label: 'fragment_ktsm',
      value: '2'
    }
  ];
  // 筛选出需要搜索的图斑
  let featureTypes = enumerate
    .map(item => {
      if (spot.includes(item.value)) return item.label;
    })
    .filter(Boolean);
  // 如果搜索图斑为空则不搜索
  if (featureTypes.length === 0) {
    return vectorSource.clear();
  }
  const featureRequest = new WFS().writeGetFeature({
    srsName: 'EPSG:4490',
    featureNS: 'http://geoserver.org',
    featurePrefix: 'osm',
    featureTypes: featureTypes,
    outputFormat: 'application/json',
    filter: filters.length === 1 ? filters[0] : or(...filters)
  });
  fetch('/ggo/cyheal/wfs', {
    method: 'POST',
    body: new XMLSerializer().serializeToString(featureRequest)
  })
    .then(function (response) {
      return response.json();
    })
    .then(function (json) {
      vectorSource.clear();
      const features = new GeoJSON().readFeatures(json);
      vectorSource.addFeatures(features);
      openLayerMapRef.value.map.getView().fit(vectorSource.getExtent(), {
        maxZoom: 18
      });
      openLayerMapRef.value.map.addLayer(vector);
    })
    .catch(error => {
      ElNotification({
        title: '提示',
        message: '未查询到对应图斑！',
        type: 'warning'
      });
    });
};

//地图初始化完成
const initMap = map => {};
</script>
<style lang="scss" scoped></style>

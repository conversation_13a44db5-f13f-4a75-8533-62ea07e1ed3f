<template>
  <div>
    <funi-detail :bizName="bizName" :showHead="true" :steps="steps" :detailHeadOption="detailHeadOption">
      <template #headTitle="{ option }">
        <span class="biz-title">
          {{ option.title }}
          <el-button type="primary" style="margin-left: 10px;" @click="toDoc">档案信息</el-button>
        </span>
      </template>
    </funi-detail>
  </div>
</template>

<script setup>
import { reactive } from "vue";
import { ref } from "vue";
import { useRoute,useRouter } from "vue-router";
import baseForm from "./components/baseForm.vue";
const route = useRoute();
const router = useRouter();
const bizName = ref(route.query.bizName);
// 头部数据
const detailHeadOption = reactive({
  title: route.query.title,
  hideStatusBar: true,
});
const steps = [
  {
    title: "宅基地信息",
    preservable: false,
    type: baseForm,
    props: {
      isDetail: true,
    },
  },
];

function toDoc(){
  router.push({
    name: 'FileManageContractDetail',
    query: {
      id: route.query.id,
      title: '承包地合同',
      bizName: '详情',
      tab: `承包地合同-${route.query.contractNo}-详情`
    }
  })
}
</script>

<style lang="scss" scoped>
.biz-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: #1a233b;
  text-align: left;
  vertical-align: top;
}
</style>

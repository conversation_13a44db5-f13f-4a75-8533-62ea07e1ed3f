@use '@/styles/mixins/utils' as *;
@use './var.scss' as *;

// @include b(layout) {
//   //--header-bg-color
//   @include set-component-css-var('header', $header);
//   @include set-component-css-var('aside-menu', $aside-menu);
//   @include set-component-css-var('nav-menu', $nav-menu);
//   @include set-component-css-var('logo', $logo);
//   @include set-component-css-var('multi-tab', $multi-tab);
//   @include set-component-css-var('app-content', $app-content);

//   @include set-css-var-value('transition-time-02', 0.2s);
// }

@include b(layout) {
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  align-items: stretch;
  flex-direction: column;
}

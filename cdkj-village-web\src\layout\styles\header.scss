@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
@use '@/styles/mixins/functions.scss' as *;
@use '@/styles/mixins/utils.scss' as *;
@use './var.scss';
@use 'sass:map';

@include b(layout-header) {
  // corlor: map.get($mask-color, 'extra-light')
  // color: var(--el-menu-active-color)
  // color: getCssVar('header', 'color');
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;
  height: getCssVar('header', 'height');
  // border: 1px solid #eee;
  background-color: getCssVar('header', 'bg-color');
  color:getCssVar('header', 'text-color');

  :deep() {
    $d: '';

    .#{$namespace}-layout-header__left-aside {
      display: flex;
      align-items: center;
      flex-shrink: 0;

      @include el.b(overlay) {
        top: getCssVar('header', 'height');
        height: unset;

        @include el.b(drawer) {
          width: auto !important;
        }
      }

      @include b(layout-logo) {
        flex-shrink: 0;
      }
    }

    .#{$namespace}-layout-header__client-switch {
      background-color: el.getCssVar('color', 'primary');
      width: getCssVar('header', 'height');
      height: getCssVar('header', 'height');

      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }

    .#{$namespace}-layout-header__dashboard {
      cursor: pointer;
      box-sizing: border-box;
      width: 90px;
      margin-left: 20px;
      margin-right: 10px;
      border-radius: 4px;
      padding: 6px 12px;
      background-color: getCssVar('header', 'bg-gary-color');

      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      &:hover {
        background-color: getCssVar('header', 'bg-hover-color');
      }

      &-icon {
        display: inline-block;
      }

      &-text {
        font-size: 14px;
        font-weight: 400;
        display: inline-block;
      }
    }

    .#{$namespace}-layout-header__tools {
      display: flex;
      align-items: center;
      padding: 0 10px;

      &-item + &-item {
        margin-left: 20px;
      }
    }
  }

  :deep(.user-menu-view .user-drop span) {
    color: getCssVar('header', 'text-color');;
  }

  .collapse {
    transform: scale(-1, 1);
  }
}

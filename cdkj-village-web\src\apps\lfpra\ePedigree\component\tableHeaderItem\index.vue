<template>
    <span class="fileName" :class="props.fileName.indexOf('.') !== -1 ? 'fileImage': 'folderImage'">{{props.fileName}}</span>

</template>
<script setup>
  import { computed, onMounted, reactive, ref,defineProps } from 'vue';

  const props = defineProps({
    fileName:{type:String,default:''},
  })
</script>
<style lang="less" scoped>
    .fileImage{
      &::before{
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 20px;
        vertical-align: bottom;
        background-image: url('../../assets/image/file.png');
        background-size: 100%;
      }
    }
    .folderImage{
      &::before{
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 20px;
        vertical-align: bottom;
        background-image: url('../../assets/image/folder_new.png');
        background-size: 100%;
      }
    }
</style>

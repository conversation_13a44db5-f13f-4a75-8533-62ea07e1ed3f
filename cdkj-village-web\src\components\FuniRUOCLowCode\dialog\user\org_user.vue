<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-23 11:17:47
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-02 15:59:25
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCode/dialog/user/org_user.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="banner">
    <div class="orglist" v-loading="loadingOrg">
      <el-tree
        ref="orgTree"
        node-key="id"
        default-expand-all
        check-on-click-node
        highlight-current
        :data="orgList"
        :props="defaultProps"
        :expand-on-click-node="false"
        @node-click="nodeClick"
      >
      </el-tree>
    </div>
    <div class="userList" v-loading="loadingUser">
      <div class="userSearch">
        <el-form validate-status="success">
          <el-input
            placeholder="人员搜索"
            clearable
            v-model="keyword"
            @input="iptChange"
            :suffix-icon="Search"
          ></el-input>
        </el-form>
      </div>
      <div class="userListBox">
        <el-checkbox-group v-model="userSelectId" @change="checkedChange" :validate-event="false">
          <template v-for="(item, index) in userList" :key="item.id">
            <el-checkbox v-show="showFunc(item)" :id="item.id" :label="item.id" :validate-event="false">
              <div>
                <span>{{ item.nickName }}</span>
              </div>
            </el-checkbox>
          </template>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, unref, watch, computed, nextTick } from 'vue';
import { getOrgTree, getUserList } from './hooks/orgUser';
import { Search } from '@element-plus/icons-vue';
const props = defineProps({
  allList: {
    type: Array,
    default: () => []
  },
  valueList: {
    type: Array,
    default: () => []
  },
  propsConfig: Object
});
const emit = defineEmits(['setallList', 'setValue']);
const defaultProps = {
  label: 'orgName'
};
const loadingOrg = ref(false);
const loadingUser = ref(false);
const orgList = ref([]);
const userList = ref([]);
const userSelectId = ref([]);
const keyword = ref('');
const searchName = ref('');
const orgTree = ref();
let timer = null;

const mode = computed(() => {
  return props?.propsConfig?.mode;
});

const nodeClick = async e => {
  if (e.disabled) return false;
  keyword.value = '';
  searchName.value = '';
  userList.value = await getUserList({
    loadingUser,
    orgId: e.id,
    allList: props.allList,
    callback: setAllUser
  });
};

const setAllUser = list => {
  emit('setallList', list);
};
const checkedChange = async (e, a) => {
  await nextTick();
  if (mode.value !== 'multiple' && e && e.length) {
    userSelectId.value = [e.pop()];
  }
  emit('setValue', userSelectId.value);
};

const iptChange = e => {
  clearTimeout(timer);
  timer = setTimeout(() => {
    clearTimeout(timer);
    searchName.value = keyword.value;
  }, 600);
};
const showFunc = item => {
  let key = searchName.value;
  if (key === '' || key === null || key === void 0) {
    return true;
  }
  let i = item.nickName.indexOf(searchName.value);
  let flag = i > -1;
  return flag;
};
onMounted(async () => {
  orgList.value = await getOrgTree({ loadingOrg, allowedOrg: props.propsConfig.allowedOrg });
  await nextTick();
  orgTree.value.setCurrentKey(orgList.value[0].id);
  nodeClick(orgList.value[0]);
});

watch(
  () => props.valueList,
  newVal => {
    userSelectId.value = newVal.map(item => item.id);
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<style scoped>
@import url('./../../style/banner.css');
@import url('./../../style/checkbox.css');
@import url('./../../style/userList.css');
.orglist {
  border-right: 1px solid var(--funi-ruoc-lc-border-color);
}

.orglist {
  width: 50%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.custom-tree-node {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
}
</style>

package cn.fight.village.domain.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * 区域
 */
@Data
@TableName("public.rlams_region")
public class Region {
    private Integer id;

    private String code;

    //父节点
    private String parentCode;

    private String name;

    private Integer rate;

    private Integer status;

    private Region parent;

    private List<Region> childes;
}

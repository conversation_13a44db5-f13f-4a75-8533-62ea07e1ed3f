package cn.fight.village;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration;
import org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration;
import org.springframework.core.task.TaskExecutor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * 应用启动类
 */
@Slf4j
@EnableAsync
@EnableTransactionManagement
@MapperScan("cn.fight.village.domain.*.repository")
@SpringBootApplication(exclude = {TaskSchedulingAutoConfiguration.class, TaskExecutionAutoConfiguration.class})
public class Application extends SpringBootServletInitializer{


    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Value("${spring.task.execution.pool.max-size}")
    private int maxSize;

    @Value("${spring.task.execution.pool.core-size}")
    private int coreSize;

    @Value("${spring.task.execution.pool.keep-alive}")
    private int keepAlive;

    @Value("${spring.task.execution.pool.queue-capacity}")
    private int queueCapacity;

    /**
     * 指定线程池任务管理器
     * @return
     */
    @Bean
    public TaskExecutor taskExecutor() {
        /*SimpleAsyncTaskExecutor simpleAsyncTaskExecutor = new SimpleAsyncTaskExecutor();
        return simpleAsyncTaskExecutor;*/
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAlive);
        executor.initialize();
        return executor;
    }
}

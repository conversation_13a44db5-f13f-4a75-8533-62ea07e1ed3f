<template>
  <funiDialog v-model="isShow" title="图例样式修改" size="small" :onCancel="onCancel">
    <funiForm ref="funiFormRef" :schema="schema"></funiForm>
  </funiDialog>
</template>

<script setup>
import { watch, ref } from 'vue';
const props = defineProps({
  modelValue: Boolean
});
const emit = defineEmits(['update:modelValue']);
watch(
  () => props.modelValue,
  val => {
    isShow.value = val;
  }
);

const isShow = ref(props.modelValue);
const funiFormRef = ref();
const schema = [
  {
    component: 'el-color-picker',
    label: '填充颜色',
    prop: 'fill',
    props: {
      'color-format': 'hex'
    },
    rules: [{ required: true, message: '请选择', trigger: 'blur' }]
  },
  {
    component: 'el-input-number',
    label: '透明度',
    prop: 'fill-opacity',
    props: {
      min: 0,
      max: 1,
      step: 0.1
    },
    rules: [{ required: true, message: '请选择', trigger: 'blur' }]
  },
  {
    component: 'el-color-picker',
    label: '边框颜色',
    prop: 'stroke',
    props: {
      'color-format': 'hex'
    },
    rules: [{ required: true, message: '请选择', trigger: 'blur' }]
  },
  {
    component: 'el-input-number',
    label: '透明度',
    prop: 'stroke-opacity',
    props: {
      min: 0,
      max: 1,
      step: 0.1
    },
    rules: [{ required: true, message: '请选择', trigger: 'blur' }]
  },
  {
    component: 'el-input-number',
    label: '边框尺寸',
    prop: 'stroke-width',
    props: {
      min: 0,
      max: 20,
      step: 1
    },
    rules: [{ required: true, message: '请选择', trigger: 'blur' }]
  }
];

/**
 * 赋值
 * @param values 表单值
 */
function setValues(values) {
  funiFormRef.value.setValues(values);
}
function getValues() {
  return funiFormRef.value.getValues();
}
function validate() {
  return funiFormRef.value.validate();
}

function onCancel() {
  emit('update:modelValue', false);
}

defineExpose({
  setValues,
  getValues,
  validate
});
</script>

<style lang="scss" scoped></style>

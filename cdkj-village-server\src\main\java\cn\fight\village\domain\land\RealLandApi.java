package cn.fight.village.domain.land;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.land.entity.RealLand;
import cn.fight.village.domain.land.entity.RealLandQuery;
import cn.fight.village.domain.land.entity.RealLandRequest;
import cn.fight.village.domain.user.entity.User;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 地理土地接口
 */
@RestController
@RequestMapping("/realLand")
public class RealLandApi {

    @Resource
    private RealLandService realLandService;


    /**
     * 创建土地
     * @param realLand
     * @return
     */
    @PostMapping("create")
    public JsonResult create(@RequestBody RealLandRequest realLand,@UserInfo User user) {
        if (realLand == null || realLand.getLand() == null) {
            throw new BusinessException("请求对象不能为空");
        }

        return realLandService.createLand(realLand,user);
    }

    /**
     * 获取土地
     * @param landNo
     * @return
     */
    @GetMapping("info")
    public JsonResult getLand(String landNo) {
        if (StringUtils.isEmpty(landNo)) {
            throw new BusinessException("土地编号不能为空");
        }

        return realLandService.getLandInfo(landNo);
    }

    /**
     * 删除土地
     * @param realLand
     * @param user
     * @return
     */
    @PostMapping("delete")
    public JsonResult delete(@RequestBody RealLandRequest realLand,@UserInfo User user) {
        if (StringUtils.isBlank(realLand.getLandNo())) {
            throw new BusinessException("待删除的土地编号不能为空");
        }

        if (realLand.getSure() == null || realLand.getSure() != 1) {
            throw new BusinessException("删除数据将无法恢复，请确认");
        }

        return realLandService.deleteLand(realLand,user);
    }

    /**
     * 列表查询
     * @param query
     * @return
     */
    @PostMapping("list")
    public JsonResult list(@RequestBody RealLandQuery query) {
        return realLandService.selectList(query);
    }

    /**
     * 根据地块编码获取地块信息
     * 用于在签订流转协议时加载土地列表
     * @param query
     * @return
     */
    @PostMapping("getLandsInfo")
    public JsonResult getLandsInfo(@RequestBody RealLandRequest query) {
        List<String> landNoList = query.getLandNoList();
        if (CollectionUtils.isEmpty(landNoList)) {
            return JsonResult.valueOfObject(Collections.emptyList());
        }

        return realLandService.getLandsInfo(query);
    }
}

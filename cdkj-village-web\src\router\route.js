import layout from '@/layout//index.vue'
export default [
    {
        path: '/system',
        component: layout,
        name: 'System',
        redirect: "/user",
        meta: { title: "系统设置", icon: "uil:setting" },
        children: [{
            path: '/user',
            component: () => import('@/apps/system/user/index.vue'),
            name: 'user',
            meta: { title: "用户管理", icon: "mdi:user-outline" },
        }, {
            path: '/user/add',
            component: () => import('@/apps/system/user/add.vue'),
            name: 'userAdd',
            meta: { title: "新增/编辑", icon: "" },
            hidden: true
        }, {
            path: '/user/detail',
            component: () => import('@/apps/system/user/detail.vue'),
            name: 'userDetail',
            meta: { title: "详情", icon: "" },
            hidden: true
        }]
    },
    {
        path: '/base',
        component: layout,
        name: 'Base',
        meta: { title: "基础信息管理", icon: "ri:file-settings-line" },
        children: [{
            path: '/household',
            component: () => import('@/apps/baseInfo/household/index.vue'),
            name: 'Household',
            meta: { title: "人员与户信息", icon: "mdi:user-settings-variant" },
        }, {
            path: '/household/add',
            component: () => import('@/apps/baseInfo/household/add.vue'),
            name: 'HouseholdAdd',
            meta: { title: "新增/编辑", icon: "" },
            hidden: true
        }, {
            path: '/household/detail',
            component: () => import('@/apps/baseInfo/household/detail.vue'),
            name: 'HouseholdDetail',
            meta: { title: "详情", icon: "" },
            hidden: true
        }, {
            path: '/homestead',
            component: () => import('@/apps/baseInfo/homestead/index.vue'),
            name: 'Homestead',
            meta: { title: "宅基地信息", icon: "mdi:store-settings-outline" },
        }, {
            path: '/homestead/add',
            component: () => import('@/apps/baseInfo/homestead/add.vue'),
            name: 'HomesteadAdd',
            meta: { title: "新增/编辑", icon: "" },
            hidden: true
        }, {
            path: '/homestead/detail',
            component: () => import('@/apps/baseInfo/homestead/detail.vue'),
            name: 'HomesteadDetail',
            meta: { title: "详情", icon: "" },
            hidden: true
        },
        {
            path: '/contract',
            component: () => import('@/apps/baseInfo/contract/index.vue'),
            name: 'Contract',
            meta: { title: "家庭承包信息", icon: "mdi:table-settings" },
        },
        {
            path: '/contract/add',
            component: () => import('@/apps/baseInfo/contract/add.vue'),
            name: 'ContractAdd',
            meta: { title: "新增/编辑", icon: "" },
            hidden: true
        }, {
            path: '/contract/detail',
            component: () => import('@/apps/baseInfo/contract/detail.vue'),
            name: 'ContractDetail',
            meta: { title: "详情", icon: "" },
            hidden: true
        }
        ]
    },
    {
        path: "/wanderManage",
        component: layout,
        meta: { title: "土地流转管理", icon: "fluent:content-settings-16-regular" },
        children: [
            {
                path: '/landManage',
                component: () => import('@/apps/wanderContract/land/index.vue'),
                name: 'LandManage',
                meta: { title: "地块管理", icon: "fluent:document-settings-20-regular" },
            },
            {
                path: '/landWander',
                component: () => import('@/apps/wanderContract/landWander/index.vue'),
                name: 'LandWander',
                meta: { title: "土地流转", icon: "fluent:document-settings-20-regular" },
            },
            {
                path: '/wanderContract',
                component: () => import('@/apps/wanderContract/index.vue'),
                name: 'WanderContract',
                meta: { title: "流转协议", icon: "fluent:document-settings-20-regular" },
            },
            {
                path: '/wanderContract/add',
                component: () => import('@/apps/wanderContract/add.vue'),
                name: 'WanderContractAdd',
                meta: { title: "新增/编辑", icon: "" },
                hidden: true
            }, {
                path: '/wanderContract/detail',
                component: () => import('@/apps/wanderContract/detail.vue'),
                name: 'WanderContractDetail',
                meta: { title: "详情", icon: "" },
                hidden: true
            }
        ]
    },
    {
        path: '/fileManage',
        component: layout,
        meta: { title: "档案管理", icon: "fluent:archive-settings-24-regular" },
        children: [
            {
                path: 'contract',
                component: () => import('@/apps/fileManage/contract/index.vue'),
                name: 'FileManageContract',
                meta: { title: "承包地合同", icon: "fluent:calendar-settings-20-regular" },
            }, {
                path: 'contract/detail',
                component: () => import('@/apps/fileManage/contract/detail.vue'),
                name: 'FileManageContractDetail',
                meta: { title: "详情", icon: "" },
                hidden: true
            },
            {
                path: 'wanderContract',
                component: () => import('@/apps/fileManage/wanderContract/index.vue'),
                name: 'FileManageWanderContract',
                meta: { title: "土地流转合同", icon: "fluent:calendar-settings-20-regular" },
            }, {
                path: 'wanderContract/detail',
                component: () => import('@/apps/fileManage/wanderContract/detail.vue'),
                name: 'FileManageWanderContractDetail',
                meta: { title: "详情", icon: "" },
                hidden: true
            }
        ]
    },
    {
        path: '/mir',
        name: "mir",
        component: () => import('@/apps/mir/index.vue'),
        isNewOpen: true,
        meta: { title: "乡村资源一张图", icon: "tdesign:map-setting" },
    }
]

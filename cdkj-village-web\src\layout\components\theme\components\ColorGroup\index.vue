<template>
  <div>
    <ThemeTitle>{{ title }}</ThemeTitle>
    <div class="ww-theme__color">
      <div
        v-for="item in colors"
        :key="item"
        class="ww-theme__color__item ww-theme__cursor"
        :class="{
          'is-actived': item == modelValue
        }"
        :style="{
          background: item,
          color:getTextColor(item)
        }"
        @click="check(item)"
      >
        <span v-if="item == modelValue">
          <Select></Select>
        </span>
      </div>
      <div
        ref="customColor"
        id="ww-custom-color__picker"
        class="ww-theme__color__item ww-theme__cursor"
        :class="{
          'is-actived': !colors.includes(modelValue)
        }"
        :style="{
          background: colors.includes(modelValue)
            ? 'conic-gradient(from 90deg at 50% 50%, rgba(132, 61, 255, 1) 0%, rgba(59, 190, 255, 1) 21.39%, rgba(61, 255, 100, 1) 46.74%, rgba(255, 236, 66, 1) 73.74%, rgba(255, 66, 66, 1) 100%)'
            : modelValue
        }"
        v-click-outside="onClickOutside"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          width="11.9803466796875"
          height="11.98040771484375"
          viewBox="0 0 11.9803466796875 11.98040771484375"
          fill="none"
        >
          <path
            id="路径 1"
            fill-rule="evenodd"
            style="fill: #ffffff"
            opacity="1"
            d="M6.59967 2.55228L9.42807 5.38073L2.82843 11.9804L0 11.9804L0 9.15193L6.59967 2.55228ZM7.54247 1.60947L8.95667 0.19526C9.21707 -0.0650867 9.63913 -0.0650867 9.89947 0.19526L11.7851 2.08088C12.0455 2.34123 12.0455 2.76334 11.7851 3.02369L10.3709 4.4379L7.54247 1.60947Z"
          ></path>
        </svg>
      </div>
    </div>
    <el-popover
      ref="colorPicker"
      placement="left"
      :width="220"
      :popper-style="{ padding: 0 }"
      effect="dark"
      :virtual-ref="customColor"
      trigger="click"
      virtual-triggering
    >
      <div id="ww-custom-color__popper" class="colorPicker">
        <ColorPicker
          @changeColor="changeColor"
          :color="colors.includes(modelValue) ? void 0 : modelValue"
        ></ColorPicker>
        <div style="padding: 4px 10px; display: flex; justify-content: space-between; background: #1d2024">
          <el-button size="small" @click.stop="hide">取消</el-button>
          <el-button type="primary" size="small" @click.stop="confirm">确定</el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted, nextTick, unref, watch } from 'vue';
import ThemeTitle from './../Title/index.vue';
import { Select } from '@element-plus/icons-vue';
import { ColorPicker } from 'vue-color-kit';
import { ClickOutside as vClickOutside } from 'element-plus';
import 'vue-color-kit/dist/vue-color-kit.css';
import useThemeConfigStore from './../../hooks/setTheme.js';
const themeConfigStore = useThemeConfigStore();

const props = defineProps({
  modelValue: {
    type: String,
    default: 'default'
  },
  colors: {
    type: Array,
    default: ['#007fff', '#E13C39', '#E76033', '#EFB041', '#5ABFC1', '#3853E2', '#6A32C9']
  },
  title: {
    type: String,
    default: '主题色'
  },
  type: {
    type: String,
    default: 'theme-color'
  },
  setFunc:{
    default:"setGlobalColor"
  }
});
const emit = defineEmits(['update:modelValue']);

const customColor = ref();
const colorPicker = ref();
const colorPickerValue = ref();

const check = value => {
  emit('update:modelValue', value);
  themeConfigStore[props.setFunc](value, true);
};
onMounted(() => {});

const onClickOutside = () => {
  unref(colorPicker).popperRef?.delayHide?.();
};
const changeColor = obj => {
  colorPickerValue.value = obj.hex;
};
const hide = () => {
  unref(colorPicker).hide();
};
const confirm = () => {
  check(colorPickerValue.value);
  hide();
};

function getTextColor(item){
  return $utils.isLightColor(item)?"#000000":"#ffffff"
}
</script>
<style scoped>
@import url('./../style/comm.css');
.ww-theme__color__item {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 12px;
  transition: all 0.2s;
  position: relative;
  box-sizing: border-box;
  border: 1px solid #d8d8d8;
}
.ww-theme__color__item > span {
  display: inline-block;
  width: 16px;
  height: 16px;
}
.ww-theme__color__item.is-actived {
  box-shadow: 0px 4px 12px 3px rgba(0, 0, 0, 0.4);
  border: 1px solid #d8d8d8;
}
.pickColor {
  position: absolute;
  top: 0;
  left: 0;
}

.ww-theme__color__item:hover {
  box-shadow: 0px 4px 12px 3px rgba(0, 0, 0, 0.4);
  transition: all 0.2s;
}
.colorPicker {
  width: max-content;
}
</style>

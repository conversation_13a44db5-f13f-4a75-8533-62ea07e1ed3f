package cn.fight.village.domain.common.entity;

import cn.fight.village.domain.common.exception.BusinessException;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据查询基类
 */
public class BaseQuery implements Serializable {
    private Integer pageSize;

    private Integer pageNo;

    private Boolean pageable;

    private String keyword;

    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void isPageable(Boolean pageable) {
        this.pageable = pageable;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return this.pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Boolean getPageable() {
        return pageable;
    }

    public void setPageable(Boolean pageable) {
        this.pageable = pageable;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public void checkPageParam() {
        if (null == this.pageSize || null == this.pageNo
                || this.pageSize <= 0 || this.pageNo <= 0)
            throw new BusinessException("分页参数错误");

        if (pageSize > 50) {
            throw new BusinessException("分页参数过大");
        }

        this.pageable = Boolean.TRUE;
    }

    @Data
    public static class DefaultBaseQuery extends BaseQuery {
        DefaultBaseQuery() {
            super();
        }
    }
}

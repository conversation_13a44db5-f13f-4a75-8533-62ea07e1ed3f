package cn.fight.village.domain.contract.entity;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 承包方共同权利人信息
 *
 */
@TableName("public.under_members")
public class UnderMembers extends BaseEntity {
    //承包方ID
    private String underId;
    //姓名
    private String name;
    //年龄
    private String gender;
    //证件类型
    private String idType;
    //证件号
    @Sensitive
    private String idCode;
    //关系
    private String relation;

    public String getUnderId() {
        return underId;
    }

    public void setUnderId(String underId) {
        this.underId = underId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }
}

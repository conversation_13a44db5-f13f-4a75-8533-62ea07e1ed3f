package cn.fight.village.domain.common.service.impl;


import cn.fight.village.domain.common.service.CacheService;
import cn.fight.village.domain.user.entity.User;
import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.CacheObj;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;

@Service
public class CacheServiceImpl implements CacheService {
    //@Value("login-timeout")
    private String loginTimeout = "1800000";

    /**
     * 用户登录缓存
     */
    private static Cache<String, Object> USER_LOGIN_CACHE = CacheUtil.newLRUCache(20);

    /**
     * 业务数据缓存
     */
    private static Cache<String, Object> BUSINESS_CACHE = null;

    @Override
    public void userLoginCache(String token, User user) {
        USER_LOGIN_CACHE.put(token,user,Long.parseLong(loginTimeout));
    }

    @Override
    public User getUserLoginCache(String token) {
        return (User)USER_LOGIN_CACHE.get(token);
    }

    @Override
    public Object getBusinessCache(String key) {
        if (BUSINESS_CACHE == null)
            return null;

        return BUSINESS_CACHE.get(key);
    }

    @Override
    public void putBusinessCache(String key, Object value, long timeout) {
        if (BUSINESS_CACHE == null)
            this.initCache();

        BUSINESS_CACHE.put(key,value,timeout);
    }

    @Override
    public void removeCache(String key) {
        if (StrUtil.isNotBlank(key) && BUSINESS_CACHE != null) {
            BUSINESS_CACHE.remove(key);
        }
    }

    //初始化缓存
    private synchronized void initCache() {
        BUSINESS_CACHE = CacheUtil.newLRUCache(50);
    }
}

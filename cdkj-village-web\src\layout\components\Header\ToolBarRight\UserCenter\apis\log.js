/*
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-03-07 20:05:21
 * @LastEditors: 古加文 <EMAIL>
 * @LastEditTime: 2023-03-07 22:02:05
 * @FilePath: \funi-paas-cscas-ui\src\apps\csuc\apis\log.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

const apiUrl = {
  // 日志录制开始
  // logRecordStart: '/csuc/logRecord/logRecordStart',
  logRecordStart: '/csuc/userRecord/startRecord',
  // 日志录制结束
  // logRecordEnd: '/csuc/logRecord/logRecordEnd',
  logRecordEnd: '/csuc/userRecord/endRecord',
  //获取用户信息
  getUserInfo: '/csuc/userCenter/getUserInfo',
  //修改用户信息
  updateUserInfo: '/csuc/userCenter/updateUserInfo',
  //修改密码
  updatePassword: '/csuc/userCenter/updatePassword',
  //登出
  logout: '/csuc/userCenter/logout',
  //获取用户密码规则
  getPasswordRegular: '/csuc/userCenter/getPasswordRegular'
};

/** 日志录制开始 */
export function logRecordStart() {
  return $http.post(apiUrl.logRecordStart);
}

/** 日志录制结束 */
export function logRecordEnd(data) {
  return $http.post(apiUrl.logRecordEnd, data);
}

/**
 * @description:获取用户信息
 * @return {*}
 */
export function getUserInfoApi() {
  return $http.post(apiUrl.getUserInfo);
}

/**
 * @description:修改用户信息
 * @return {*}
 */
export function updateUserInfoApi(params) {
  return $http.post(apiUrl.updateUserInfo, params);
}

/**
 * @description:修改密码
 * @return {*}
 */
export function updatePasswordApi(params) {
  return $http.post(apiUrl.updatePassword, params);
}

/**
 * @description:登出
 * @return {*}
 */
export function logoutApi() {
  return $http.post(apiUrl.logout);
}

/**
 * @description:获取用户密码规则
 * @return {*}
 */
export function getPasswordRegularApi() {
  return $http.post(apiUrl.getPasswordRegular);
}

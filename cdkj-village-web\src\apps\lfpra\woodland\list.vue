<!-- 坑塘承包信息管理 -->
<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { userTableColumns, useBtnsConfig } from './hooks/index.jsx';
import { apiUrl,queryListHttp, deleteHttp } from './hooks/api.js';
import { expotrFunction,getNowTime } from '@/apps/lfpra/common/hooks/utils.jsx';
import { useRouter } from 'vue-router';
import { ElNotification } from 'element-plus';
const router = useRouter();
const listPage = ref();
const query = ref();
// 获取列表数据
const lodaData = async (pages, parmas) => {
  query.value = parmas;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryListHttp({
    ...pages,
    ...parmas
  });
  return data;
};
onMounted(() => {});
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({addFunc, importFunc,exportFunc}),
        lodaData: lodaData,
        reloadOnActive: true,
        fixedButtons: true,
        checkOnRowClick: true,
        columns: userTableColumns({ seeDateils, editFunc, delFunc })
      }
    }
  ];
});
// 查看详情
const seeDateils = row => {
  router.push({
    name: 'lfpra_woodland_details',
    query: {
      title: '坑塘承包信息详情',
      bizName: '详情',
      type: 'info',
      id: row.id,
      principalSn: row.contractSn,
      no:row.cerNumber,
      tab: ['坑塘承包信息', row.cerNumber, '详情'].join('-')
    }
  });
};
// 编辑
const editFunc = row => {
  router.push({
    name: 'lfpra_woodland_add',
    query: {
      title: '坑塘承包信息编辑',
      bizName: 'edit',
      type: 'edit',
      id: row.id,
      no:row.cerNumber,
      tab: ['坑塘承包信息', row.cerNumber, '编辑'].join('-')
    }
  });
};
// 删除
const delFunc = async row => {
   await deleteHttp({ contractId: row.id });
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};
//新增
const addFunc = () => {
  router.push({
    name: 'lfpra_woodland_add',
    query: {
      title: '坑塘承包信息新增',
      bizName: '新建',
      type: 'add',
      tab: '坑塘承包信息-新增'
    }
  });
};
//导入
const importFunc = () => {
  listPage.value.reload({ resetPage: false });
};
// 导出
const exportFunc = () => {
   expotrFunction({ url: apiUrl.queryExport, params: query.value, FileName: '坑塘承包信息管理'+getNowTime() });
};
</script>




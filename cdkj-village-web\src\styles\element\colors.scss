@use 'sass:math';
@use 'sass:map';

// 基础色
$colors: () !default;
$colors: map.deep-merge(
  (
    'white': #ffffff,
    'black': #000000,
    'primary': (
      'base': #007fff
    ),
    'success': (
      'base': #67cc35
    ),
    'warning': (
      'base': #f2ac13
    ),
    'danger': (
      'base': #fa5757
    ),
    'error': (
      'base': #f56c6c
    ),
    'info': (
      'base': #787f94
    )
  ),
  $colors
);

// Text文字色
$text-color: () !default;
$text-color: map.merge(
  (
    'primary': #303133,
    'regular': #606266,
    'secondary': #909399,
    'placeholder': #a8abb2,
    'disabled': #c0c4cc,
    'white': map.get($colors, 'white')
  ),
  $text-color
);

// Border边框色
$border-color: () !default;
$border-color: map.merge(
  (
    '': #dcdfe6,
    'light': #e4e7ed,
    'lighter': #ebeef5,
    'extra-light': #f2f6fc,
    'dark': #d4d7de,
    'darker': #cdd0d6
  ),
  $border-color
);

// Fill填充色
$fill-color: () !default;
$fill-color: map.merge(
  (
    '': #f0f2f5,
    'light': #f5f7fa,
    'lighter': #fafafa,
    'extra-light': #fafcff,
    'dark': #ebedf0,
    'darker': #e6e8eb,
    'blank': map.get($colors, 'white')
  ),
  $fill-color
);

// Background背景色
$bg-color: () !default;
$bg-color: map.merge(
  (
    '': #ffffff,
    'page': #f2f3f5,
    'overlay': #ffffff
  ),
  $bg-color
);

// mask蒙版
//--el-mask-color
//--el-mask-color-extra-light
$mask-color: () !default;
$mask-color: map.merge(
  (
    '': rgba(255, 255, 255, 0.9),
    'extra-light': rgba(255, 255, 255, 0.6)
  ),
  $mask-color
);

// overlay
$overlay-color: () !default;
$overlay-color: map.merge(
  (
    '': rgba(0, 0, 0, 0.8),
    'light': rgba(0, 0, 0, 0.7),
    'lighter': rgba(0, 0, 0, 0.5)
  ),
  $overlay-color
);

// nav
$nav-color: () !default;
$nav-color: map.merge(
  (
    'light': #ffffff,
    'light-gray': #ececec,
    'light-hover': #e5f2ff,
    'dark': #222a35,
    'dark-gray': #2b3542,
    'dark-hover': #181e26,
    'blue': #3499ff,
    'blue-gray': #59acff,
    'blue-hover': #007fff
  ),
  $nav-color
);

// Table
$table: () !default;
$table: map.merge(
  (
    'row-hover-bg-color': #e5f2ff,
    'header-text-color': #535a6c,
    'text-color': #535a6c
  ),
  $table
);

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.homestead.repository.HomesteadRepository">

    <select id="pageQuery" resultType="cn.fight.village.domain.homestead.value.HomesteadListVo">
        select
            hs.uuid,
            hs.code,
            hs.area,
            hs.usage,
            hs.has_cert hasCert,
            hm.name,
            hm.id_type idType,
            hm.id_code idCode,
            h.location
        from public.rlams_homestead hs
        left join public.rlams_household h on hs.household = h.uuid and hs.deleted = 0
        left join public.rlams_member hm on hm.house_id = h.uuid and hm.deleted = 0 and hm.householder = 1
        where hs.deleted = 0
        <if test="householder != null and householder != ''">
            and hm.name like '%' || #{householder} || '%'
        </if>
        <if test="usage != null and usage != ''">
            and hs.usage = #{usage}
        </if>
        <if test="areaMin != null">
            and hs.area >= #{areaMin}
        </if>
        <if test="areaMax != null">
            and hs.area &lt;= #{areaMax}
        </if>
        <if test="hasCert != null and hasCert !=''">
            and hs.has_cert = #{hasCert}
        </if>
        <if test="code != null and code !=''">
            and hs.code = #{code}
        </if>
        <if test="idCode != null and idCode !=''">
            and hm.id_code = #{idCode}
        </if>
        order by hs.create_time desc
    </select>
</mapper>
<template>
  <div class="page-box">
    <div class="search-box">
      <el-input v-model="keyword" style="width: 240px" placeholder="请输入承包方姓名查询" />
      <el-button type="primary" @click="search" style="margin-left: 20px">查询</el-button>
    </div>
    <div class="container-box">
      <div class="left">
        <funiOlMap
          :layers="layers"
          ref="funiOlMapRef"
          :showOverlay="true"
          :showLayerTree="false"
          :isAutoShowModal="true"
          :legendListFunc="legendListFunc"
          @layerClick="layerClick"
        >
          <template #overlayContent="data">
            <div>
              <div class="row">
                <div class="label">地块编号：</div>
                <div class="value">{{ modalInfo.dkbm }}</div>
              </div>
              <div class="row">
                <div class="label">地块名称：</div>
                <div class="value">{{ modalInfo.dkmc }}</div>
              </div>
              <div class="row">
                <div class="label">土地类型：</div>
                <div class="value">{{ modalInfo.landType }}</div>
              </div>
              <div class="row">
                <div class="label">承包方：</div>
                <div class="value">{{ modalInfo.householder }}</div>
              </div>
              <div class="row">
                <div class="label">面积(亩)：</div>
                <div class="value">{{ modalInfo.area }}</div>
              </div>
              <div class="row">
                <div class="label">备注：</div>
                <div class="value">{{ modalInfo.dkbm }}</div>
              </div>
              <div class="btn-box">
                <el-button @click="ModalCancel">取消</el-button>
                <el-button type="primary" @click="ModalSure">添加</el-button>
              </div>
            </div>
          </template>
        </funiOlMap>
      </div>
      <div class="right">
        <div style="display: flex; padding: 12px 12px 0">
          <div style="white-space: nowrap">项目归属：</div>
          <el-input v-model="project" placeholder="请输入项目归属"></el-input>
        </div>
        <funi-curd-v2 :data="land.dataList" rowKey="landNo" :columns="land.columns" :pagination="false" :stripe="false">
        </funi-curd-v2>
      </div>
    </div>
  </div>
  <el-drawer v-model="drawer" title="地块信息" direction="rtl">
    <FuniCurdV2 ref="FuniCurdV2Ref" v-bind="cardTab"> </FuniCurdV2>
  </el-drawer>
</template>

<script setup lang="tsx">
import { ref, reactive } from "vue";
import { ElNotification } from "element-plus";
import { info, list } from "@/apps/api/land.js";

const emit = defineEmits(["output"]);

let currentChooseFeature;
const project = ref();
const keyword = ref();
const drawer = ref(false);
const modalInfo = ref({});
const funiOlMapRef = ref();
const FuniCurdV2Ref = ref();
const layers = ref([
  {
    id: "lyr_dk",
    name: "流转图层",
    type: "wms",
    checked: true,
    url: "/geoserver/village/wms", //http://**************:8081/geoserver/village/wms
    layer: "village:lyr_dk_test",
  },
]);
const land = ref({
  columns: [
    {
      label: "地块编号",
      prop: "landNo",
      width: 200,
      // render: ({ row, index }) => {
      //   return (
      //     <el-button type="primary" link onClick={() => locationLand(row, index)}>
      //       {row.landNo}
      //     </el-button>
      //   );
      // },
    },
    {
      label: "承包方",
      prop: "householder",
    },
    {
      label: "面积",
      prop: "area",
    },
    {
      label: "操作",
      prop: "opt",
      align: "center",
      width: 80,
      render: ({ row, index }) => {
        return (
          <div>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                del(row), index;
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                ),
              }}
            ></el-popconfirm>
          </div>
        );
      },
    },
  ],
  dataList: [],
});

function ModalCancel() {
  modalInfo.value = {};
  funiOlMapRef.value.highLightLayerClear();
  funiOlMapRef.value.closeModal();
}

function ModalSure() {
  if (currentChooseFeature.getProperties().sflz == "1") {
    ElNotification({
      title: "此地块已经流转，不能选择！",
      type: "warning",
    });
    return;
  }
  if (land.value.dataList.find((x) => x.householdId != modalInfo.value.householdId)) {
    ElNotification({
      title: "禁止添加不同户主地块！",
      type: "warning",
    });
    return;
  }
  modalInfo.value._chooseFeature = currentChooseFeature;
  land.value.dataList.push(modalInfo.value);
  funiOlMapRef.value.addChooseFeature(currentChooseFeature);
  ModalCancel();
}

function legendListFunc(legendList, layersInstance) {
  // 参数保留用于回调函数接口兼容
  return [];
}

function layerClick(data, layer, feature) {
  currentChooseFeature = feature;
  funiOlMapRef.value.highLightFeatures({ features: [feature], isFit: false });

  info({ landNo: data.dkbm }, { loading: true }).then((res) => {
    modalInfo.value = { ...res.land, householder: res.householder, householdId: res.householdId, ...res.gisLand };
  });
}

/**
 * 下一步
 */
async function nextStep() {
  if (!land.value.dataList.length) {
    ElNotification({
      title: "请选择地块",
      type: "warning",
    });
    return Promise.reject();
  }
  emit("output", land.value.dataList, project.value);
  return Promise.resolve();
}

function del(row, index) {
  land.value.dataList.splice(index, 1);
  funiOlMapRef.value.removeChooseFeature(row._chooseFeature);
}

function search() {
  drawer.value = true;
  FuniCurdV2Ref.value.reload();
}

/**
 * 列表配置
 */
const cardTab = reactive({
  reloadOnActive: true,
  lodaData: (pages, parmas) => {
    return list({ ...pages, underName: keyword.value });
  },
  searchConfig: {
    schema: [],
  },
  columns: [
    {
      label: "地块编码",
      prop: "landNo",
      render: ({ row, index }) => {
        return (
          <el-button type="primary" link onClick={() => choose(row)}>
            {row.landNo}
          </el-button>
        );
      },
    },
    { label: "地块名称", prop: "landName" },
    { label: "承包方", prop: "householder" },
  ],
});

function choose(row) {
  funiOlMapRef.value.highLightByFilter({ wfsFilter: [{ type: "=", key: "dkbm", value: row.landNo }] });
}
defineExpose({
  nextStep,
});
</script>

<style lang="scss" scoped>
.page-box {
  height: calc(100vh - 276px);
  display: flex;
  flex-direction: column;
  .search-box {
  }
  .container-box {
    flex: 1;
    display: flex;
  }
  .left {
    flex: 1;
    padding-top: 12px;
    ::v-deep() {
      .row {
        display: flex;
        padding: 5px;
        .label {
          width: 120px;
        }
        .value {
          flex: 1;
        }
      }
      .btn-box {
        margin-top: 6px;
        text-align: right;
      }
    }
  }
  .right {
    flex: 1;
  }
}
</style>

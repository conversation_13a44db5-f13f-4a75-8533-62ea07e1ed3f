package cn.fight.village.domain.common.entity;


import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.user.entity.User;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务实体对象基类
 */
public abstract class BaseEntity implements Serializable {
    public static Integer IS_DELETED = 1;
    public static Integer IS_NOT_DELETED = 0;

    @TableId("uuid")
    private String uuid;

    @TableField("deleted")
    private Integer deleted;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField("delete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;

    @TableField("creator_id")
    private String creatorId;

    @TableField("updater_id")
    private String updaterId;

    @TableField("deleter_id")
    private String deleterId;

    /**
     * 修改业务对象记录字段回写
     * @param user
     * @return
     */
    public BaseEntity manageUpdateInfo(User user) {
        if (user == null)
            throw new BusinessException("更新业务对象操作者不能为空");

        this.updateTime = CommonUtils.currentDate();
        this.updaterId = user.getUuid();
        return this;
    }

    /**
     * 新建业务对象记录字段回写
     * @param user
     * @return
     */
    public BaseEntity manageCreateInfo(User user) {
        if (user == null)
            throw new BusinessException("新建业务对象操作者不能为空");

        this.deleted = IS_NOT_DELETED;
        this.uuid = CommonUtils.getGuid();
        this.createTime = CommonUtils.currentDate();
        this.creatorId = user.getUuid();
        return this;
    }

    /**
     * 删除业务件对象记录字段回写
     * @param user
     * @return
     */
    public BaseEntity manageDeleteInfo(User user) {
        if (user == null)
            throw new BusinessException("删除业务对象操作者不能为空");

        this.deleted = IS_DELETED;
        this.deleterId = user.getUuid();
        this.deleteTime = CommonUtils.currentDate();
        return this;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getUpdaterId() {
        return updaterId;
    }

    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
    }

    public String getDeleterId() {
        return deleterId;
    }

    public void setDeleterId(String deleterId) {
        this.deleterId = deleterId;
    }

}

.archivesInfo {
  margin-top: 20px;
  background-color: #fff;
  padding-bottom: 20px;
}
.container {
  margin-top: 20px;
  display:flex;
  align-items: center;
  flex-wrap: wrap;
  // display: grid;
  // gap: 30px 0px;
  // grid-auto-flow: row;
  // grid-template-areas:

  .container_file {

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
}

.container_item{
  width: 20%;
}
.pointer {
  cursor: pointer;
}
.btn_span{
  font-size: 15px;
}
.popover_width{
  min-width: 90px!important;
  width: 90px !important;
}
.popover_span{
  width: 100px;
  font-size: 15px;
  color: #007fff;
  text-align: center;
  cursor: pointer;
  margin-bottom: 5px;
  &:last-child{
    margin-bottom: 0;
  }
}
.container_file_title{
  width: 50%;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.controls {
  height: 160px;
  padding-top: 20px;
  // position: relative;
  &:hover {
    background-color: #f5f5f5;
  }
  .flex_box{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .buttons {
    width: 90%;
    color: #007fff;
    text-align: center;
    cursor: pointer;
    // position: absolute;
    // bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;

  }

}

<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="cardTab" @headBtnClick="headBtnClick" />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { userList, userRemove } from '@/apps/api/user.js';

const router = useRouter();
const listPage = ref();
/**
 * 列表配置
 */
const cardTab = reactive([
  {
    curdOption: {
      reloadOnActive: true,
      lodaData: (pages, parmas) => {
        return userList({ ...pages, ...parmas });
      },
      searchConfig: {
        schema: [
          {
            prop: 'keyword',
            label: '关键字',
            component: 'el-input'
          },
          {
            prop: 'account',
            label: '账号',
            component: 'el-input'
          },
          {
            prop: 'username',
            label: '用户名',
            component: 'el-input'
          }
        ]
      },
      btns: [{ key: 'add', label: '新增' }],
      columns: [
        {
          label: '账号',
          prop: 'account',
          render: ({ row, index }) => {
            return (
              <el-button type="primary" link onClick={() => detail(row)}>
                {row.account}
              </el-button>
            );
          }
        },
        { label: '用户名', prop: 'username' },
        { label: '用户类型', prop: 'userType' },
        {
          label: '操作',
          prop: 'opt',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            return (
              <div>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    edit(row);
                  }}
                >
                  编辑
                </el-button>
                <el-popconfirm
                  title="确认删除该条数据吗？"
                  onConfirm={() => {
                    del(row);
                  }}
                  v-slots={{
                    reference: () => (
                      <el-button type="primary" link>
                        删除
                      </el-button>
                    )
                  }}
                ></el-popconfirm>
              </div>
            );
          }
        }
      ]
    }
  }
]);

/**
 * 编辑
 * @param row 数据行
 */
function edit(row) {
  router.push({
    path: '/user/add',
    query: {
      userId: row.uuid,
      title: '用户管理编辑',
      bizName: '编辑',
      tab: `用户管理-${row.account}-编辑`
    }
  });
}
/**
 * 删除
 * @param row 数据行
 */
function del(row) {
  userRemove({ userId: row.uuid }).then(res => {
    listPage.value.reload();
  });
}
/**
 * 详情
 * @param row 数据行
 */
function detail(row) {
  router.push({
    path: '/user/detail',
    query: {
      userId: row.uuid,
      title: '用户管理详情',
      bizName: '详情',
      tab: `用户管理-${row.account}-详情`
    }
  });
}
/**
 * 按钮点击事件
 */
function headBtnClick(res) {
  switch (res) {
    case 'add':
      router.push({
        path: '/user/add',
        query: {
          title: '用户管理新增',
          bizName: '新建',
          tab: '用户管理-新增'
        }
      });
      break;
    default:
      break;
  }
}
</script>

<style lang="scss" scoped></style>

<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" title="添加共有人信息">
      <div>
        <FuniCurd
          ref="funiCurd"
          @get-curd="
            e => {
              funi_curd = e;
            }
          "
          height="calc(50vh - 40px)"
          :columns="columnsProject"
          :useSearchV2="false"
          :isShowSearch="false"
          :searchConfig="searchConfig"
          :lodaData="lodaData"
          :pagination="false"
          :loading="loading"
          @row-click="getData"
          @select-all="getData"
          @select="getData"
          :row-class-name="tableRowClassName"
          size="small"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :disabled="selection.length == 0" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref } from 'vue';
import {getFamilyMembersHttp} from '../../hooks/api'
// 表单查询

const funi_curd = ref(null);
const dialogVisible = ref(false); // 控制模态框显示隐藏
const funiCurd = ref(void 0);

const selection = ref([]);
const familyId = ref('');
let snList = [];
const loading = ref(false);
// 模态框表格配置
const columnsProject = computed(() => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left',
      selectable: (row, index) => {
        return snList.indexOf(row.id) < 0;
      }
    },
    {
      label: '姓名',
      prop: 'memberName'
    },
    {
      label: '性别',
      prop: 'dicGenderName'
    },
    {
      label: '证件类型',
      prop: 'dicCardTypeName'
    },
    {
      label: '证件号码',
      prop: 'cerCertificateNo'
    },
    {
      label: '联系方式',
      prop: 'phone'
    },
    {
      label: '家庭关系',
      prop: 'dicRelationName'
    }
  ];
});
const searchConfig = computed(() => {
  let obj = {
    schema: [
      {
        label: '关键字',
        component: 'el-input',
        prop: 'keyword',
        props: {
          placeholder: '请输入项目名称、归属公司、归属部门搜索'
        }
      }
    ]
  };
  return obj;
});
const emit = defineEmits(['exportObject']);

const getData = e => {
  if (Array.isArray(e)) {
    selection.value = e;
  } else {
    selection.value = e.selection;
  }
};

const show = async (id,list) => {
  selection.value = [] //清楚上一次关闭时的数据
  familyId.value = id;
  snList = list;
  await nextTick();
  dialogVisible.value = true;
};

const lodaData = async (page, params) => {
  loading.value = true;
  const resData = await getFamilyMembersHttp({
    familyId: familyId.value,
    isNeedOwner:false,

  }).finally(() => {
    loading.value = false;
  });

  return resData;
};

const tableRowClassName = ({ row }) => {
  if (snList.indexOf(row.id) > -1) {
    return 'disabled-row';
  }
  return '';
};

const confirmFunc = () => {
  emit('exportObject', unref(selection));
  cancelClick();
};

const cancelClick = () => {
  dialogVisible.value = false;
  selection.value = [];
};

defineExpose({
  show
});
</script>
<style scoped>
@import url('@/apps/lfpra/common/style/table_disabled.css');
</style>

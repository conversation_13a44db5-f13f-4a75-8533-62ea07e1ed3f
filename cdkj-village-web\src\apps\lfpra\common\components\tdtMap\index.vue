<template>
  <div class="main">
    <div id="container" />
    <div class="searchCard">
      <slot name="search"> </slot>
    </div>
    <div class="collect">
      <slot name="rightMasking"> </slot>
    </div>
  </div>
</template>
<script setup>
/**
 *  @param { array } centralCoordinate : 地图中心点 - 默认 [ 104.0608646, 30.65545222 ]
 *  @param { number } zoom : 缩放级别 - 默认 18
 *  @param { object } geoJson : 图斑数据
 *  @param { function } patternSpotCallBack : 图斑回调
 *  @param { function } content : 图斑详情模板
 *
 */
import { ref, onMounted } from 'vue';
import funiTdtMap from './tdtSdk';
import geoJson from '../../../../../../public/js/geoJson';

const props = defineProps({
  centralCoordinate: {
    type: Array,
    default: [104.0608646, 30.65545222] // [104.24577, 30.63932]
  },
  zoom: {
    type: Number,
    default: 18
  },
  geoJson: {
    type: Object,
    default: null
  },
  patternSpotCallBack: {
    type: Function,
    default: () => {}
  },
  content: String
});
let map;
onMounted(() => {
  map = new funiTdtMap(
    'container',
    { center: props.centralCoordinate, zoom: props.zoom },
    'eec68d0c06e0314338aa8d2372b49035'
  );
  loadGeoJson();
});
const loadGeoJson = () => {
  let selectPolygon;
  map.initGisLayer(props.geoJson || geoJson, (lngLatArr, feature, mapInstance) => {
    let color = feature.properties.fwmj === 16114.48 ? 'red' : 'blue'; // 图斑颜色
    let polygon = new T.Polygon(lngLatArr, { color: color, weight: 2, opacity: 0.5 });
    var infoWin1 = new T.InfoWindow();
    polygon.on('click', event => {
      new Promise((resolve, reject) => {
        // 图斑回调
        props.patternSpotCallBack(feature);
        polygon.setColor('red'); // 图斑点击后颜色
        resolve();
      }).then(() => {
        infoWin1.setContent(props.content);
      });

      //   //自行判断，颜色还原
      if (selectPolygon) {
        selectPolygon.setColor('black');
      }

      selectPolygon = polygon;
      // console.log(polygon.getLngLats());
      polygon.openInfoWindow(infoWin1);
    });
    return polygon;
  });
};
</script>
<style lang="scss" scoped>
.main {
  position: relative;
  .searchCard {
    position: absolute;
    top: 0;
    z-index: 99999;
  }
  .collect {
    position: absolute;
    top: 15%;
    right: 1%;
    z-index: 99999;
  }
  #container {
    height: 100vh;
  }
}
</style>

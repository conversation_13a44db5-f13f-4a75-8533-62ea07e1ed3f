<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-23 11:18:44
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-02 15:34:55
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCode/dialog/user/oneself.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="oneself-banner" v-loading="loading">
    <el-checkbox-group v-model="userSelectId" @change="checkedChange" :validate-event="false">
      <el-checkbox
        v-for="(item, index) in userList"
        :key="item.id"
        :id="item.id"
        :label="item.id"
        :validate-event="false"
      >
        <div>
          <span>{{ item.nickName || item.nickname }}</span>
        </div>
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
<script setup>
import { ref, onBeforeMount, watch, computed, nextTick } from 'vue';
import { getUserInfo } from './hooks/oneself.js';
const userSelectId = ref([]);
const userList = ref([]);
const emit = defineEmits(['setallList', 'setValue']);
const loading = ref(false);
const props = defineProps({
  allList: {
    type: Array,
    default: () => []
  },
  valueList: {
    type: Array,
    default: () => []
  },
  propsConfig: Object
});

watch(
  () => props.valueList,
  newVal => {
    userSelectId.value = newVal.map(item => item.id);
  },
  {
    deep: true,
    immediate: true
  }
);
// csuc/userCenter/getUserInfo
const setAllUser = list => {
  emit('setallList', list);
};
const checkedChange = async (e, a) => {
  await nextTick();
  if (mode.value !== 'multiple' && e && e.length) {
    userSelectId.value = [e.pop()];
  }
  emit('setValue', userSelectId.value);
};

const mode = computed(() => {
  return props?.propsConfig?.mode;
});

onBeforeMount(async () => {
  userList.value = await getUserInfo({
    loading,
    allList: props.allList,
    callback: setAllUser,
    allowedSelf: props?.propsConfig?.allowedSelf
  });
});
</script>
<style scoped>
@import url('./../../style/banner.css');
@import url('./../../style/checkbox.css');
</style>

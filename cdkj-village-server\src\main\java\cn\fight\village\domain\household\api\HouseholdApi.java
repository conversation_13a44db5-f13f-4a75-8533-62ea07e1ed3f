package cn.fight.village.domain.household.api;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.entity.BaseQuery;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.household.entity.Household;
import cn.fight.village.domain.household.query.HouseholdListQuery;
import cn.fight.village.domain.household.request.HouseholdRequest;
import cn.fight.village.domain.household.service.HouseholdService;
import cn.fight.village.domain.user.entity.User;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 人员家庭户信息接口层
 *
 */
@Controller
@RequestMapping("household")
public class HouseholdApi {

    @Resource
    private HouseholdService householdService;

    /**
     * 添加户信息
     *
     * @param request
     * @return
     */
    @ResponseBody
    @PostMapping("add")
    public JsonResult addHousehold(@UserInfo User user, @RequestBody HouseholdRequest request) {
        if (request == null) {
            throw new BusinessException("请求对象不能为空");
        }

        return householdService.addHousehold(user,request);
    }

    /**
     * 获取列表
     *
     * @return
     */
    @ResponseBody
    @PostMapping("list")
    public JsonResult getList(@RequestBody HouseholdListQuery query) {
        return householdService.queryList(query);
    }

    /**
     * 获取详情信息
     *
     * @param uuid
     * @return
     */
    @ResponseBody
    @GetMapping("info")
    public JsonResult getInfo(String uuid) {
        CommonUtils.notNull(uuid,"查询ID不能为空");
        return householdService.queryInfo(uuid);
    }

    /**
     * 删除信息
     *
     * @param request
     * @return
     */
    @ResponseBody
    @PostMapping("remove")
    public JsonResult remove(@RequestBody HouseholdRequest request,@UserInfo User user) {
        return householdService.remove(request,user);
    }

    /**
     * 信息更新
     *
     * @param request
     * @param user
     * @return
     */
    @ResponseBody
    @PostMapping("update")
    public JsonResult update(@RequestBody HouseholdRequest request,@UserInfo User user) {
        return householdService.update(request,user);
    }

    /**
     * 获取家庭成员信息-分页
     *
     * @param query
     * @return
     */
    @ResponseBody
    @PostMapping("houseMembers")
    public JsonResult getHouseMembers(@RequestBody BaseQuery query) {
        return householdService.getHouseMembers(query);
    }

    /**
     * 获取家庭成员信息
     *
     * @param
     * @return
     */
    @ResponseBody
    @GetMapping("homeMembers")
    public JsonResult getHomeMembers(String householdId,Integer exHouseholder) {
        return householdService.getHomeMembers(householdId,exHouseholder);
    }

    /**
     * 获取
     *
     * @param name
     * @return
     */
    @ResponseBody
    @GetMapping("qyeryByHouseholder")
    public JsonResult queryByHouseholder(String name,Integer pageSize,Integer pageNo) {
        if (StringUtils.isBlank(name)) {
            return JsonResult.valueOfObject(Collections.emptyList());
        }

        return householdService.queryByHouseholder(name,pageSize,pageNo);
    }
}

<template>
    <funi-curd-v2  :columns="columns" :data="dataList" :pagination="false"></funi-curd-v2>
</template>

<script setup lang="jsx">
import { ref } from 'vue';
import { queryByHousehold } from '@/apps/api/household.js'
import { useRoute,useRouter } from 'vue-router';

const { id } = useRoute().query;
const router = useRouter()
if(id){
    queryByHousehold({houseHoldId:id}).then(res=>{
        dataList.value = res
    })
}
const columns = ref([
{
      label: '宅基地编号',
      prop: 'code',
      render: ({ row, index }) => {
        return (
            <el-button type="primary" link onClick={()=>toPage(row,index)}>
              {row.code}
            </el-button>
        )
      }
    },
    {
      label: '宅基地面积（㎡）',
      prop: 'area'
    },
    { label: '使用情况', prop: 'usage' },
    { label: '土地四至', prop: 'point' },
])
const dataList =ref([])

function toPage(row){
    router.push({
        name:"HomesteadDetail",
        query:{
            id:row.uuid,
            title: '宅基地信息详情',
            bizName: '详情',
            tab: `宅基地信息-${row.code}-详情`,
        }
    })
}
</script>

<style lang="scss" scoped>

</style>
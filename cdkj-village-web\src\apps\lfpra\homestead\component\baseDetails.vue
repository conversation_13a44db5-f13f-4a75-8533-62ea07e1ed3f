<template>
  <div>
    <GroupTitle title="宅基地信息" />
    <funi-form :schema="schema" @get-form="e => (form = e)" :rules="rules" :border="false" :col="2" />
    <GroupTitle title="权属信息" />
    <OwnerInfo @exData="getExData" :tableData="tableData" :isEdit="props.isEdit" />
    <GroupTitle title="宅基地图斑" />
    <!-- <mapSpot layerType="cyheal:fragment_zjd" :isEdit="props.isEdit" :id="patternId" @mapExportData="mapExportData" /> -->
    <!-- 提交成功dailog 跳转列表页 -->
    <SubmitSuccess ref="su" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, inject, nextTick, watch, onMounted, unref, reactive, provide } from 'vue';
import { useSchema, useRules } from '../hooks/baseDetails.jsx';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
import GroupTitle from '@/apps/lfpra/common/components/groupTitle/index.vue';
import OwnerInfo from '@/apps/lfpra/common/components/ownerInfo/index.vue';
// import mapSpot from '@/apps/lfpra/common/components/mapSpot/index.vue';
import SubmitSuccess from '@/apps/lfpra/common/components/submit_success/index.vue';
import { infoHttp, newHttp, getOtherSiteinfoPatternIdsHttp } from '../hooks/api';
import { dictListHttp } from '@/apps/lfpra/common/hooks/api';
const router = useRouter();

const form = ref();
// const layerType = inject('loadingStatus');
const su = ref();
const isContract = ref('');
// 权属信息数据
const tableData = ref([]);
const ownerInfoParams = reactive({
  memberName: '',
  groupNumber: ''
});
//图斑id
const patternId = ref('');
const patternIds = ref([]);
provide('otherPatternIds', patternIds); //其他已经选择的图斑id集合
// 接收数据
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true
  },
  id: {
    type: String,
    default: ''
  }
});
//下来选择框
const selectList = reactive({
  obtainType: [
    { label: '是', value: true },
    { label: '否', value: false }
  ], //是否取证
  LandType: [], //土地类型
  floorType: [], //房屋楼层
  structureType: [], //房屋结构
  usageType: [] //使用情况
});
// 是否取证选择框
const contractChange = e => {
  isContract.value = e;
  console.log(typeof isContract.value, 'isContract.value----111');
};
// 配置表单
const schema = computed(() => {
  return useSchema({ isEdit: props.isEdit, ...selectList, contractChange, isContract: isContract.value });
});
// 表单验证规则
const rules = computed(() => {
  return useRules({ isEdit: props.isEdit });
});

onMounted(() => {
  init();
});
// 勾选后的权属信息
const getExData = e => {
  tableData.value = e;
  ownerInfoParams.memberName = e[0].memberName;
  ownerInfoParams.groupNumber = e[0].groupNumber;
  console.log(tableData.value, 'tableData.value---');
};
// 图斑数据
const mapExportData = e => {
  patternId.value = e;
};
// 初始化数据
const init = () => {
  getDictLisType('LAND', 'LandType');
  getDictLisType('FLOOR', 'floorType');
  getDictLisType('HOUSE_STRUCT', 'structureType');
  getDictLisType('USE_COND', 'usageType');
  getOtherPatternIds();
  // 未携带id 新增进入
  if (!props.id) return;
  // 携带id进入 编辑，详情
  getDetails();
};
// 字典查询
const getDictLisType = async (e, i) => {
  let { list } = await dictListHttp({ dictiEnum: e });
  selectList[i] = list;
};
//查询其他地块id
const getOtherPatternIds = async () => {
  let data = await getOtherSiteinfoPatternIdsHttp({ siteInfoId: props.id });
  patternIds.value = data;
};
// 获取编辑/详情回显的数据
const getDetails = () => {
  infoHttp({ siteInfoId: props.id }).then(res => {
    tableData.value = res.ownerShipInfoVos;
    isContract.value = res.isForensics;
    if(res.ownerShipInfoVos && res.ownerShipInfoVos.length >0){
      ownerInfoParams.memberName = res.ownerShipInfoVos[0].memberName;
      ownerInfoParams.groupNumber = res.ownerShipInfoVos[0].groupNumber;
    }
    patternId.value = res.patternId;
    nextTick(() => {
      form.value.setValues(res);
    });
  });
};
// 判断是否关联权属信息
const verificationTable = () => {
  if (tableData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '请关联权属信息',
      type: 'warning'
    });
    return false;
  }
  return true;
};
// 判断是否图斑信息
const verificationMap = () => {
  if (!patternId.value) {
    ElNotification({
      title: '提示',
      message: '请关联图斑',
      type: 'warning'
    });
    return false;
  }
  return true;
};
//处理数据
const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await form.value.validate();
  if (type == 'ts' || (isValid && verificationTable() && verificationMap())) {
    // if (type == 'ts' || (isValid && verificationTable())) {
    let fromData = form.value.getValues();
    let obj = {
      id: props.id || '',
      ...fromData,
      familySns: tableData.value.map(item => item.familySn),
      patternId: patternId.value,
      memberName: ownerInfoParams.memberName,
      groupNumber: ownerInfoParams.groupNumber
    };
    console.log(obj, 'obj------');
    return obj;
  } else {
    return false;
  }
};
// 保存数据 提交接口
const saveDate = async type => {
  let data = await getData(type);

  if (!data) return;
  // loadingStatus.value.status = true;
  // loadingStatus.value.status = true;
  await newHttp(data).then(res => {
    if (res.dataId) {
      su.value.show();
    }
  });
  await nextTick();
  if (!props.id) return Promise.reject();
  // if (type == 'ts') {
  //   ElNotification({
  //     title: '暂存成功',
  //     type: 'success'
  //   });
  // } else {
  //   su.value.show();
  // }
  return Promise.resolve({});
};

//提交按钮
const submit = () => {
  return saveDate();
};
defineExpose({
  submit
  // ts
});
</script>

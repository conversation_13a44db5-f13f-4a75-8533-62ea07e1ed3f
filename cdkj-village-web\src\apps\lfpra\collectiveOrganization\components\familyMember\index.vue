<template>
  <div>
    <header class="title">
      <FuniGroupTitle title="人员信息" />
      <el-button type="primary" @click="_add">新增</el-button>
    </header>
    <funi-curd ref="refFuniListPage" :columns="cardTab.columns" :data="cardTab.dataList" :pagination="false">
    </funi-curd>
    <!-- <AddModal ref="addModalRef" :isExamine="isExamine" @addCallBack="addCallBack" /> -->
  </div>
</template>
<script setup lang="jsx">
import { ref, reactive, watch } from 'vue';
// import AddModal from './addModal.vue';
const props = defineProps({
  personnelData: {
    type: Array,
    default: []
  }
});
const eimt = defineEmits(['dataListCallBack']);
const refFuniListPage = ref();
const addModalRef = ref(null);
const isExamine = ref(true);
const cardTab = reactive({
  dataList: [],
  columns: [
    { label: '姓名', prop: 'memberName' },
    {
      label: '性别',
      prop: 'dicGenderName'
    },
    { label: '证件类型', prop: 'dicCardTypeName' },
    { label: '证件号码', prop: 'cerCertificateNo' },
    { label: '联系方式', prop: 'phone' },
    { label: '家庭关系', prop: 'dicRelationName' },
    {
      label: '操作',
      prop: 'operList',
      align: 'center',
      fixed: 'right',
      render: ({ row, index }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => {
                _edit(row, index);
              }}
            >
              编辑
            </el-button>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                _delete(row, index);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ]
});

const updated = () => {
  refFuniListPage.value.reload();
};

const subscript = ref();

const addCallBack = res => {
  let { isNew, data } = res;
  // isNew ? 新增 ：编辑
  if (isNew) {
    cardTab.dataList.push(data);
  } else {
    cardTab.dataList[subscript.value] = data;
  }
};

const _add = () => {
  isExamine.value = true;
  addModalRef.value.show();
};
const _edit = (row, index) => {
  subscript.value = index;
  isExamine.value = true;
  addModalRef.value.show(row);
};
const _delete = (row, index) => {
  cardTab.dataList.splice(index, 1);
};
defineExpose({
  familyMemberRequests: cardTab.dataList
});

watch(
  () => props.personnelData,
  () => {
    if (props.personnelData.length > 0) {
      cardTab.dataList = props.personnelData;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
</style>

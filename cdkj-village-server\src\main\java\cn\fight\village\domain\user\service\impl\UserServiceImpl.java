package cn.fight.village.domain.user.service.impl;

import cn.fight.village.domain.common.constant.BusinessConstant;
import cn.fight.village.domain.common.entity.BaseEntity;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.service.CacheService;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.domain.user.repository.UserRepository;
import cn.fight.village.domain.user.request.LoginRequest;
import cn.fight.village.domain.user.request.UserQuery;
import cn.fight.village.domain.user.request.UserRequest;
import cn.fight.village.domain.user.service.UserService;
import cn.fight.village.domain.user.vo.UserVo;
import cn.fight.village.task.CheckLicenceTask;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.google.code.kaptcha.Producer;
import org.springframework.util.FastByteArrayOutputStream;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserRepository userRepository;

    @Resource
    private CacheService cacheService;

    //*@Resource(name = "captchaProducer")
    private Producer captchaProducer;

    //@Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Override
    public JsonResult userLogin(LoginRequest request) {
        if(!CheckLicenceTask.getLicenceAvailable()) {
            throw new BusinessException("验证应用许可证失败，请联系系统开发商");
        }

        if(StrUtil.isBlank(request.getAccount()) || StrUtil.isBlank(request.getPassword())) {
            throw new BusinessException("用户名或密码不能为空");
        }

        //获取用户
        String enPsw = SecureUtils.digestEncode(request.getPassword());
        LambdaQueryWrapper<User> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.eq(User::getAccount,request.getAccount());
        userQueryWrapper.eq(User::getPassword,enPsw);
        userQueryWrapper.eq(User::getDeleted,User.IS_NOT_DELETED);
        User user = userRepository.selectOne(userQueryWrapper);
        if (user == null) {
            throw new BusinessException("用户名或密码错误");
        }

        //生成jwt
        //String token = SecureUtils.createToken(user);
        String token = SecureUtils.generateToken(user);

        //将用户信息放入缓存
        try {
            cacheService.userLoginCache(token, user);
        } catch (Exception e) {
            log.error("生成token失败");
            throw new BusinessException("登录失败,请重试");
        }

        //返回加密后的token
        return JsonResult.valueOfObject(SecureUtils.stringEncode(token));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult addUser(User user, UserRequest userRequest) {
        if (user == null)
            throw new BusinessException("获取当前登录信息失败,请重新登录");

        if (StrUtil.isBlank(userRequest.getAccount()) 
                || StrUtil.isBlank(userRequest.getPassword()) 
                || StrUtil.isBlank(userRequest.getUsername())
                || userRequest.getUserType() == null)
            throw new BusinessException("必填项不能为空");

        //账号重复校验
        User existAccount = userRepository.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getAccount, userRequest.getAccount())
                .eq(User::getDeleted,User.IS_NOT_DELETED));
        if (existAccount != null)
            throw new BusinessException("当前账号已存在");

        User insertUser = new User();
        String uuid = CommonUtils.getGuid();
        BeanUtils.copyProperties(userRequest,insertUser);
        insertUser.setUuid(uuid);
        insertUser.setCreatorId(user.getUuid());
        insertUser.setDeleted(User.IS_NOT_DELETED);
        insertUser.setCreateTime(CommonUtils.currentDate());
        insertUser.setPassword(SecureUtils
                .digestEncode(userRequest.getPassword()));
        
        if (1 != userRepository.insert(insertUser))
            throw new BusinessException("新建用户失败");

        insertUser.setPassword("*");
        return JsonResult.valueOfObject(insertUser);
    }

    @Override
    public JsonResult removeUser(User user,String userId) {
        if (user == null)
            throw new BusinessException("获取当前登录信息失败,请重新登录");

        if (!BusinessConstant.USER_TYPE_ADMIN.equals(user.getUserType())) {
            throw new BusinessException("权限不足");
        }

        if ("1".equals(userId)) {
            throw new BusinessException("超级管理员不允许修改");
        }

        if (1 != userRepository.logicDeleteById(userId))
            throw new BusinessException("删除失败");

        return JsonResult.successMessage("删除成功");
    }

    @Override
    public JsonResult getUserList(UserQuery userQuery) {
        userQuery.checkPageParam();
        PageHelper.startPage(userQuery.getPageNo(),userQuery.getPageSize());
        List<UserVo> userList = userRepository.selectUserList(userQuery);
        return JsonResult.valueOfObject(new PageInfo<>(userList));
    }

    @Override
    public JsonResult getUserInfo(String userId) {
        User user = userRepository.selectOne(new LambdaQueryWrapper<User>()
                .eq(BaseEntity::getUuid, userId)
                .eq(BaseEntity::getDeleted,User.IS_NOT_DELETED));

        UserVo result = new UserVo();
        if (user != null) {
            BeanUtils.copyProperties(user,result);
        }

        return JsonResult.valueOfObject(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResult updateUser(User user, UserRequest userRequest) {
        if (user == null || user.getUserType() != 1)
            throw new BusinessException("权限不足");

        if ("1".equals(userRequest.getUuid())) {
            throw new BusinessException("超级管理员不允许修改");
        }

        User userUpdater = new User();
        BeanUtils.copyProperties(userRequest,userUpdater);
        userUpdater.setUpdaterId(user.getUuid());
        userUpdater.setUpdateTime(CommonUtils.currentDate());

        if (!StrUtil.isBlank(userRequest.getPassword())) {
            user.setPassword(SecureUtils.digestEncode(userRequest.getPassword()));
        }

        if (userRepository.updateById(userUpdater) != 1) {
            throw new BusinessException("用户修改失败");
        }

        userUpdater.setPassword("*");
        return JsonResult.valueOfObject(userUpdater);
    }

    @Value("captcha-timeout")
    private String captchaTimeout;

    @Override
    public JsonResult getCaptchaImage(HttpServletResponse response) {
        // 保存验证码信息
        String uuid = CommonUtils.getGuid();
        String verifyKey = BusinessConstant.CAPTCHA_HEADER + uuid;

        BufferedImage image = null;
        String capStr = null, code = null;

        capStr = code = captchaProducer.createText();
        image = captchaProducer.createImage(capStr);

        //保存到缓存
        cacheService.putBusinessCache(verifyKey,code,Long.parseLong(captchaTimeout));

        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        }
        catch (IOException e) {
            return JsonResult.failMessage(e.getMessage());
        }

        Map<String, Object> result = new HashMap<>();
        result.put("key",verifyKey);
        result.put("image", Base64.encode(os.toByteArray()));
        return JsonResult.valueOfObject(result);
    }
}

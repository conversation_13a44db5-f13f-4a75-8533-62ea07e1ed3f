package cn.fight.village.domain.land.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.transaction.annotation.Transactional;

/**
 * gis中使用的土地信息
 */
@Data
@TableName("public.lyr_dk")
public class GisLand {
    private Integer gid;

    private Long bsm;

    private String ysdm;

    //地块代码
    private String dkbm;

    private String dkmc;

    private String syqxz;

    private String dklb;

    private String tdlylx;

    private String sfjbnt;

    private Double scmj;

    private String dkdz;

    private String dkxz;

    private String dknz;

    private String dkbz;

    private String dkbzxx;

    private String zjrxm;

    private String kjzb;

    private Double scmjm;

    private String dldj;

    @TableField(exist = false)
    private String geomStr;

    //所属组别
    private String sszb;

    private String geom;
}

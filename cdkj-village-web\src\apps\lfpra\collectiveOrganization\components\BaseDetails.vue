<template>
  <div>
    <GroupTitle title="家庭信息" />
    <funiForm v-bind="formConfig" ref="refForm" :col="2" />
    <FamilyMember ref="familyRef" :personnelData="personnelData" @dataListCallBack="dataListCallBack" />
    <!-- 提交成功dailog 跳转列表页 -->
    <SubmitSuccess ref="su" />
  </div>
</template>
<script setup lang="jsx">
import { ref, reactive, watch, nextTick } from 'vue';
import SubmitSuccess from '@/apps/lfpra/common/components/submit_success/index.vue';
import { getFiveLevelApi, familyInfoApi, newFamilyInfoApi } from '../api/index';
import FamilyMember from './familyMember/index.vue';
import { ElMessage } from 'element-plus';
const props = defineProps({
  id: String
});
const refForm = ref(null); // 家庭信息
const familyRef = ref(null);
const su = ref();
const personnelData = ref([]);

// 五级区划校验
const validateCommander = (rule, value, callback) => {
  if (value === '' || value === null || value === undefined || !Object.keys(value).length) {
    callback(new Error('必填'));
  } else if (value && Object.keys(value).length) {
    if (!value?.province?.value) {
      callback(new Error('省必填'));
    } else if (!value?.city?.value) {
      callback(new Error('市必填'));
    } else if (!value?.district?.value) {
      callback(new Error('区必填'));
    } else if (!value?.community?.value) {
      callback(new Error('街道必填'));
    } else if (!value?.street?.value) {
      callback(new Error('村委会必填'));
    } else if (!value?.other) {
      callback(new Error('组必填'));
    } else if (!value?.addressFull) {
      callback(new Error('详细地址必填'));
    }
    callback();
  } else {
    callback();
  }
};

// 家庭住址Columus
const formConfig = reactive({
  schema: [
    { prop: 'memberCode', label: '户编号', component: 'el-input', colProps: { span: 24 }, style: 'width:50%' },
    {
      prop: 'address',
      label: '家庭住址',
      component: () => <FuniRegion />,
      colProps: { span: 24 },
      props: {
        // showAddressFull: false,
        // lvl: 5,
        regionProps: {
          province: {
            disabled: true
          },
          city: {
            disabled: true
          },
          district: {
            disabled: true
          }
        },
        extension: () => (
          <el-input oninput="value=value.replace(/[^0-9]/g,'')" style="width:10%;flex: 0 0 auto">
            {{
              append: '组'
            }}
          </el-input>
        )
      }
    },
    {
      prop: 'isPoverty',
      label: '是否贫困户',
      component: 'funi-select',
      props: {
        placeholder: '请选择',
        options: [
          {
            value: true,
            label: '是'
          },
          {
            value: false,
            label: '否'
          }
        ],
        style: 'width:100%'
      }
    },
    {
      prop: 'isAllowance',
      label: '是否低保户',
      component: 'funi-select',
      props: {
        placeholder: '请选择',
        options: [
          {
            value: true,
            label: '是'
          },
          {
            value: false,
            label: '否'
          }
        ],
        style: 'width:100%'
      }
    },
    {
      prop: 'remark',
      label: '备注信息',
      component: 'el-input',
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '输入内容' }
    }
  ],
  rules: {
    memberCode: [{ required: true, message: '户编号必填', trigger: 'change' }],
    address: [{ required: true, validator: validateCommander, trigger: 'blur' }],
    isPoverty: [{ required: true, message: '请选择', trigger: 'change' }],
    isAllowance: [{ required: true, message: '请选择', trigger: 'change' }]
  }
});

// 区划数据
const leveList = reactive({
  dicProvinceCode: '510000',
  dicCityCode: '510100',
  dicDistrictCode: '510112',
  dicCommunityCode: '',
  dicStreetCode: '',
  groupNumber: '',
  detailedAddress: ''
});
//提交按钮
const submit = async () => {
  let valid = await refForm.value.validateField();
  let value = refForm.value.getValues();

  if (value.address) {
    leveList.dicCommunityCode = value.address.street.value;
    leveList.dicStreetCode = value.address.community.value;
    leveList.groupNumber = value.address.other;
    leveList.detailedAddress = value.address.addressFull;
  }
  if (valid.isValid) {
    let familyMemberRequests =
      familyRef.value.familyMemberRequests.length !== 0 ? familyRef.value.familyMemberRequests : value.familyMemberVos; // 家庭成员信息
    let data = await newFamilyInfoApi({ ...value, ...leveList, familyMemberRequests });

    // ElMessage({
    //   message: !props.id ? '新建成功' : '编辑成功',
    //   type: 'success'
    // });
    su.value.show();
  }

  return Promise.resolve({});
};
defineExpose({
  submit
});

let obj = {
  province: { label: '四川省', value: '510000' },
  city: { label: '成都市', value: '510100' },
  district: { label: '龙泉驿区', value: '510112' },
  community: {},
  street: {},
  other: ''
};

// 获取编辑数据
const getEditData = () => {
  try {
    familyInfoApi({ familyInfoId: props.id }).then(res => {
      nextTick(async () => {
        obj.community = { value: res.familyInfoVo.dicStreetCode, label: res.familyInfoVo.dicStreetName };
        obj.street = { value: res.familyInfoVo.dicCommunityCode, label: res.familyInfoVo.dicCommunityName };
        obj.other = res.familyInfoVo.groupNumber;
        obj.addressFull = res.familyInfoVo.detailedAddress;
        refForm.value.setValues({ ...res.familyInfoVo, address: obj });
        personnelData.value = res.familyInfoVo.familyMemberVos;
      });
    });
  } catch (error) {
    console.log(error);
  }
};

watch(
  () => props.id,
  () => {
    if (props.id) {
      getEditData();
    } else {
      nextTick(async () => {
        refForm.value && refForm.value.setValues({ address: obj });
      });
    }
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped></style>

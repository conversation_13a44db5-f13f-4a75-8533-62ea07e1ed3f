package cn.fight.village.domain.contract.api;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.contract.request.ContractQuery;
import cn.fight.village.domain.contract.request.ContractRequest;
import cn.fight.village.domain.contract.service.ContractService;
import cn.fight.village.domain.user.entity.User;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 合同信息接口层
 *
 */
@Controller
@RequestMapping("contract")
public class ContractApi {

    @Resource
    private ContractService contractService;

    /**
     * 合同信息添加-修改
     *
     * @param user
     * @param request
     * @return
     */
    @ResponseBody
    @PostMapping("manage")
    public JsonResult addContract(@UserInfo User user, @RequestBody ContractRequest request) {
        return contractService.addContract(request,user);
    }

    /**
     * 合同信息列表获取
     *
     * @param query
     * @return
     */
    @ResponseBody
    @PostMapping("list")
    public JsonResult getList(@RequestBody ContractQuery query) {
        return contractService.getList(query);
    }

    /**
     * 合同删除
     *
     * @param request
     * @param user
     * @return
     */
    @ResponseBody
    @PostMapping("remove")
    public JsonResult remove(@RequestBody ContractRequest request,@UserInfo User user) {
        return contractService.remove(request,user);
    }

    /**
     * 合同详情
     *
     * @param uuid
     * @return
     */
    @ResponseBody
    @GetMapping("info")
    public JsonResult getInfo(String uuid) {
        return contractService.info(uuid);
    }

    /**
     * 根据户ID获取土地承包信息
     *
     * @param householdId
     * @return
     */
    @ResponseBody
    @GetMapping("queryByHousehold")
    public JsonResult queryByHousehold(String householdId,String type) {
        return contractService.queryByHousehold(householdId,type);
    }

    /**
     * 创建土地流转协议
     * @param request
     * @return
     */
    @ResponseBody
    @PostMapping("createProtocol")
    public JsonResult createProtocol(@RequestBody ContractRequest request,@UserInfo User user) {
        return contractService.createProtocol(request,user);
    }

    /**
     * 确认协议完成前夜
     * @param request
     * @param user
     * @return
     */
    @ResponseBody
    @PostMapping("sureSign")
    public JsonResult sureProtocolSign(@RequestBody ContractRequest request,@UserInfo User user) {
        String uuid = request.getUuid();
        String signed = request.getSigned();

        if (StringUtils.isEmpty(uuid) || StringUtils.isEmpty(signed)) {
            throw new BusinessException("缺失必要确认信息");
        }

        return contractService.sureSign(uuid,signed,user);
    }
}

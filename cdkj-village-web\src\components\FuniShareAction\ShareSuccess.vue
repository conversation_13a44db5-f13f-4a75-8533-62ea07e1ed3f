<template>
  <div class="ShareSuccess">
    <el-dialog v-model="dialogVisible"
      :title="title"
      :destroy-on-close="true"
      :append-to-body="true"
      :width="500"
      @close="onClose">
      <div class="share-row">
        <el-row>
          <span style="font-size:14px;color: #606266;font-weight: bold;">分享链接为：</span>
        </el-row>
        <el-row style="margin-top:8px;">
          <el-input id="share-url"
            type="textarea"
            :disabled="true"
            v-model="virtualUrl" />
        </el-row>
        <el-row justify="end"
          style="margin-top:32px;">
          <el-button type="success"
            @click="copyUrl">复制链接</el-button>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';

const emit = defineEmits(["close"]);

const dialogVisible = ref(false);
const title = ref('分享信息');
const virtualUrl = ref('');
let closeNext;

function show (url, next) {
  virtualUrl.value = url;
  closeNext = next;
  dialogVisible.value = true;
}

function copyUrl () {
  const save = function (e) {
    e.clipboardData.setData('text/plain', virtualUrl.value);
    e.preventDefault();
  }
  document.addEventListener('copy', save);
  document.execCommand('copy');
  ElMessage({
    message: 'url已复制到剪切板',
    type: 'success',
  });
}

function onClose () {
  if (closeNext) {
    closeNext();
  }
  emit('close');
}

defineExpose({ show });
</script>
<style lang='scss' scoped>
</style>

<template>
  <div class="main">
    <div id="container" />
    <div class="searchCard">
      <slot name="search"> </slot>
    </div>
    <div class="collect">
      <slot name="rightMasking"> </slot>
    </div>
    <div id="map_popup">
      <el-icon class="close_icon" @click="closeOverLay"><CircleClose /></el-icon>
      <div v-if="selectData.layerType == 'cbd'">
        <h1>承包地</h1>
        <article style="width: 490px">
          <section style="display: flex">
            <div style="width: 50%">
              地块编号：<span>{{ selectData['dkdm'] ?? '--' }}</span>
            </div>
            <div>
              面积：<span>{{ selectData['mj'] ? selectData['mj'] + '亩' : '--' }}</span>
            </div>
          </section>
          <section style="display: flex">
            <div style="width: 50%">
              是否承包：<span>{{ selectData['sfcb'] ? '是' : '否' }}</span>
            </div>
            <div>
              土地类型：<span>{{ selectData['tdlx'] ?? '--' }}</span>
            </div>
          </section>
          <section style="display: flex">
            <div style="width: 50%">
              承包方名称：<span>{{ selectData['name'] ?? '--' }}</span>
            </div>
            <div>
              种植情况：<span>{{ selectData['zzqk'] ?? '--' }}</span>
            </div>
          </section>
        </article>
        <div style="color: #007fff; cursor: pointer" @click="seeDateils('cbd')">查看详情</div>
      </div>
      <div v-else-if="selectData.layerType == 'ktsm'">
        <h1>坑塘</h1>
        <article style="width: 490px">
          <section style="display: flex">
            <div style="width: 50%">
              坑塘编号：<span>{{ selectData['dkdm'] ?? '--' }}</span>
            </div>
            <div>
              面积：<span>{{ selectData['mj'] ? selectData['mj'] + '亩' : '--' }}</span>
            </div>
          </section>
          <section style="display: flex">
            <div>
              承包方（代表）名称：<span>{{ selectData['name'] ?? '--' }}</span>
            </div>
          </section>
        </article>
        <div style="color: #007fff; cursor: pointer" @click="seeDateils('kt')">查看详情</div>
      </div>
      <div v-else-if="selectData.layerType == 'zjd'">
        <h1>宅基地</h1>
        <article style="width: 490px">
          <section style="display: flex">
            <div style="width: 50%">
              宅基地编号：<span>{{ selectData['dkdm'] ?? '--' }}</span>
            </div>
            <div>
              面积：<span>{{ selectData['mj'] ? selectData['mj'] + '平方米' : '--' }}</span>
            </div>
          </section>
          <section style="display: flex">
            <div style="width: 50%">
              使用人名称：<span>{{ selectData['name'] ?? '--' }}</span>
            </div>
            <div>
              使用状态：<span>{{ selectData['syzt'] ?? '--' }}</span>
            </div>
          </section>
        </article>
        <div style="color: #007fff; cursor: pointer" @click="seeDateils('zjd')">查看详情</div>
      </div>
    </div>
  </div>
</template>
<script setup>
/**
 *  @param { array } centralCoordinate : 地图中心点 - 默认 [ 104.0608646, 30.65545222 ]
 *  @param { number } zoom : 缩放级别 - 默认 18
 *  @param { object } geoJson : 图斑数据
 *  @param { function } patternSpotCallBack : 图斑回调
 *  @param { function } content : 图斑详情模板
 *
 */
import XYZ from 'ol/source/XYZ';
import openlayerSdk from './openlayerSdk.js';
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();
const props = defineProps({
  centralCoordinate: {
    type: Array,
    default: [104.0608646, 30.65545222] // [104.24577, 30.63932]
  },
  zoom: {
    type: Number,
    default: 18
  },
  geoJson: {
    type: Object,
    default: null
  },
  patternSpotCallBack: {
    type: Function,
    default: () => {}
  },
  content: String
});
//选中图斑数据
let selectData = ref({});
//承包地
let cbdLayer;
//坑塘水面
let ktsmLayer;
//宅基底
let zjdLayer;
//国土空间变更
let dlybgdccgLayer;
//永久基本农田
let yjjbntLayer;
//土地现状
let xztcLayer;
//矢量图层
let vectorLayer;
let tunderLayer;
//矢量图层
let overlay;
let map = ref(null);
onMounted(() => {
  let funiMapSdk = new openlayerSdk();
  funiMapSdk.init('container');
  //天地图图层
  // funiMapSdk.addTileLayer({
  //   source: new XYZ({
  //     url: 'http://t0.tianditu.com/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=16efebb5258f64b86de89851ebcca46c'
  //   }),
  //   name: '天地图影像图层'
  // });
  tunderLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/fragment/wms',
    url: '/tdgeo/fragment/wms',
    params: { LAYERS: 'fragment:img_ljc' },
    ratio: 1,
    serverType: 'geoserver'
  });
  cbdLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/cyheal/wms',
    url: '/ggo/cyheal/wms',
    params: { LAYERS: 'cyheal:fragment_cbd' },
    ratio: 1,
    serverType: 'geoserver'
  });
  ktsmLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/cyheal/wms',
    url: '/ggo/cyheal/wms',
    params: { LAYERS: 'cyheal:fragment_ktsm' },
    ratio: 1,
    serverType: 'geoserver'
  });
  zjdLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/cyheal/wms',
    url: '/ggo/cyheal/wms',
    params: { LAYERS: 'cyheal:fragment_zjd' },
    ratio: 1,
    serverType: 'geoserver'
  });
  dlybgdccgLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/cyheal/wms',
    url: '/ggo/cyheal/wms',
    params: { LAYERS: 'cyheal:fragment_tdlybgdccg' },
    ratio: 1,
    serverType: 'geoserver'
  });
  yjjbntLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/cyheal/wms',
    url: '/ggo/cyheal/wms',
    params: { LAYERS: 'cyheal:fragment_yjjbnt' },
    ratio: 1,
    serverType: 'geoserver'
  });
  xztcLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/cyheal/wms',
    url: '/ggo/cyheal/wms',
    params: { LAYERS: 'cyheal:fragment_xztc' },
    ratio: 1,
    serverType: 'geoserver'
  });
  vectorLayer = funiMapSdk.addVectorLayer();
  overlay = funiMapSdk.addOverlay(document.querySelector('#map_popup'));
  map.value = funiMapSdk.getMap();
  map.value.on('singleclick', function (evt) {
    var viewResolution = map.value.getView().getResolution();
    let arr = [];
    [dlybgdccgLayer,yjjbntLayer,xztcLayer,cbdLayer, ktsmLayer, zjdLayer].forEach(item => {
      if (item.getVisible()) {
        var url = item
          .getSource()
          .getFeatureInfoUrl(evt.coordinate, viewResolution, 'EPSG:4326', { INFO_FORMAT: 'application/json' });
        if (url) {
          arr.push(
            fetch(url)
              .then(response => {
                return response.text();
              })
              .then(data => {
                return JSON.parse(data);
              })
          );
        }
      }
    });
    //从图层叠加顺序找到顶层有数据的图层
    Promise.all(arr).then(res => {
      let obj = res.reverse().find(x => x.features.length);
      funiMapSdk.renderRenderVectorLayer(vectorLayer, obj);
      selectData.value = obj.features[0].properties;
      if (obj.features[0].id.includes('cbd')) {
        selectData.value.layerType = 'cbd';
      } else if (obj.features[0].id.includes('ktsm')) {
        selectData.value.layerType = 'ktsm';
      } else if (obj.features[0].id.includes('zjd')) {
        selectData.value.layerType = 'zjd';
      }
      overlay.setPosition(evt.coordinate);
    });
  });
});
///获取wms图层
const getWmsLayer = () => {
  return [cbdLayer, ktsmLayer, zjdLayer,dlybgdccgLayer,yjjbntLayer,xztcLayer];
};
//关闭弹框
const closeOverLay = () => {
  overlay.setPosition(undefined);
  vectorLayer.getSource().clear();
};
defineExpose({ getWmsLayer, map,closeOverLay });

const seeDateils = res => {
  let obj = {
    zjd: {
      name: 'lfpra_homestead_details',
      title: '宅基地信息详情'
    },
    cbd: {
      name: 'lfpra_contractedLand_details',
      title: '承包地信息详情'
    },
    kt: {
      name: 'lfpra_woodland_details',
      title: '坑塘信息详情'
    }
  };
  let data = JSON.parse(selectData.value.system_data);
  router.push({
    name: obj[res].name,
    query: {
      title: obj[res].title,
      tab: obj[res].title,
      bizName: '详情',
      type: 'info',
      id: data?.id,
      no: selectData.value['dkdm'],
      principalSn: data?.contractSn ?? data?.siteSn
    }
  });
};
</script>
<style lang="scss" scoped>
.main {
  position: relative;

  .searchCard {
    margin-left: 30px;
    position: absolute;
    top: 0;
    z-index: 99999;
  }

  .collect {
    position: absolute;
    top: 15%;
    right: 1%;
    z-index: 99999;
  }

  #container {
    height: 100vh;
  }

  #map_popup {
    position: absolute;
    background-color: #fff;
    transform: translate(-50%, -100%);
    padding: 10px;
    top: -10px;
    .close_icon {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
  }
  #map_popup:after,
  #map_popup:before {
    top: 100%;
    border: solid transparent;
    content: ' ';
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
  }
  #map_popup:after {
    border-top-color: white;
    border-width: 10px;
    left: 50%;
    margin-left: -10px;
  }
  #map_popup:before {
    border-top-color: #cccccc;
    border-width: 11px;
    left: 50%;
    margin-left: -11px;
  }
}
</style>

<template>
  <el-select
    clearable
    :multiple="attribute.multi"
    style="width: 100%"
    placeholder="请选择"
    :loading="loading"
    @visible-change="handleVisibleChange"
  >
    <el-option v-for="item in dynamicOptions" :key="item.value" :label="item.key" :value="item.value" />
  </el-select>
</template>

<script setup>
import { ref, watchEffect, inject } from 'vue';

defineOptions({
  name: 'SearchFormSelectItem'
});

const props = defineProps({
  column: String,
  attribute: { type: Object, default: () => ({}) }
});

const { getFilters } = inject($utils.symbols.searchForm);

const options = ref([]);
const dynamicOptions = ref([]);

const loading = ref(false);

watchEffect(() => {
  if (!!props.attribute.url) {
    fetchOptions();
  }
});

async function fetchOptions() {
  try {
    const res = await $http.fetch(props.attribute.url);
    if (res && res.list && res.list.length > 0) {
      options.value = res.list;
    }
  } catch (error) {
    options.value = [];
  }
  return Promise.resolve();
}

async function fetchDynamicOptions() {
  try {
    if (!options.value.length) {
      await fetchOptions();
    }

    if (props.attribute?.dynamic && !!props.attribute?.dynamicUrl) {
      const filters = getFilters().filter(item => item.column !== props.column);
      if (!!filters.length) {
        const requestParams = { queryField: props.column, filters, ...props.attribute?.params };
        const res = await $http.post(props.attribute?.dynamicUrl, requestParams);
        const dynamicValues = (res.list || []).map(item => item.value);
        dynamicOptions.value = options.value.filter(item => dynamicValues.includes(item.value));
      } else {
        dynamicOptions.value = options.value || [];
      }
    }
  } catch (error) {
    dynamicOptions.value = [];
  }

  return Promise.resolve();
}

async function handleVisibleChange(visible) {
  if (visible && props.attribute?.dynamic) {
    loading.value = true;
    await fetchDynamicOptions();
    loading.value = false;
  } else if (visible) {
    dynamicOptions.value = options.value;
  }
}
</script>

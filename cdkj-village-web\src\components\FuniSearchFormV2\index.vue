<template>
  <el-form ref="formRef" :model="dynamicValidateForm" label-width="0px">
    <div class="form-item__row">
      <div class="form-item__fields">
        <template v-for="(field, index) in dynamicValidateForm.fields" :key="field.key">
          <SearchFormItem
            v-model="dynamicValidateForm.fields[index]"
            :prop="{
              column: `fields.${index}.column`,
              operate: `fields.${index}.operate`,
              value: `fields.${index}.value`
            }"
            :query-fields="queryFields"
            @newField="addfield"
            @deleteField="removefield(field)"
          >
            <template v-for="(_, slot) in $slots" #[slot]="params">
              <slot :name="slot" v-bind="params || {}" />
            </template>
          </SearchFormItem>
        </template>
      </div>
      <div class="form-item__action">
        <el-form-item>
          <el-button type="primary" @click="submitForm(formRef)">
            <el-icon><Search /></el-icon>查询
          </el-button>
          <el-button @click="resetForm(formRef)">
            <el-icon><RefreshLeft /></el-icon>重置
          </el-button>
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import SearchFormItem from './searchFormItem.vue';
import { useRoute } from 'vue-router';

const emit = defineEmits(['search', 'reset']);
const props = defineProps({
  pageCode: String
});

const formRef = ref();
const queryFields = ref([]);
const dynamicValidateForm = reactive({
  fields: [{ key: 1, value: '', column: '', operate: '' }]
});

const route = useRoute();

onMounted(() => {
  $http.fetch('/gsbms/advQueryList/getAdvancedQueryList', { pageCode: props.pageCode || route.name }).then(res => {
    queryFields.value = res.list || [];
  });
});

const removefield = item => {
  const index = dynamicValidateForm.fields.indexOf(item);
  if (index !== -1) {
    dynamicValidateForm.fields.splice(index, 1);
  }
};

const addfield = () => {
  dynamicValidateForm.fields.push({
    key: Date.now(),
    column: '',
    operate: '',
    value: ''
  });
};

const submitForm = formEl => {
  if (!formEl) return;
  formEl.validate(valid => {
    if (valid) {
      const filters = dynamicValidateForm.fields
        .filter(item => !!item.value && !!item.column && !!item.operate)
        .map(item => {
          const newItem = {
            column: item.column,
            operate: item.operate,
            value: item.value
          };
          if ($utils.isArray(item.value)) {
            Object.assign(newItem, { value: item.value.join(',') });
          }
          if ($utils.isString(newItem.value) && !!newItem.value) {
            Object.assign(newItem, { value: newItem.value.trim() });
          }
          return newItem;
        });
      emit('search', !!filters.length ? { filters } : {});
    } else {
      console.log('error submit!');
      return false;
    }
  });
};

const resetForm = formEl => {
  dynamicValidateForm.fields.splice(1, dynamicValidateForm.fields.length - 1);
  setTimeout(() => {
    !!formEl && formEl.resetFields();
    emit('reset');
  }, 0);
};
</script>

<style lang="scss" scoped>
.el-form {
  padding: 16px 16px 0;
}

.form-item__row {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: start;
  flex-wrap: nowrap;

  .form-item__action {
    flex: 1;
  }

  .form-item__fields {
    margin-right: 14px;

    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
}
</style>

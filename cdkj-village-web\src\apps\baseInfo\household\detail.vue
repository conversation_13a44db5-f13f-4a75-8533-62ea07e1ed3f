<template>
    <div>
      <funi-detail
        :bizName="bizName"
        :showHead="true"
        :steps="steps"
        :detailHeadOption="detailHeadOption"
      />
    </div>
  </template>
  
  <script setup>
  import { reactive } from 'vue';
  import { ref } from 'vue';
  import { useRoute } from 'vue-router';
  import baseForm from './components/baseForm.vue';
  import homestead from './components/homestead/index.vue';
  import contract from './components/contract/index.vue';
  const route = useRoute();
  const bizName = ref(route.query.bizName);
  // 头部数据
  const detailHeadOption = reactive({
    title: route.query.title,
    hideStatusBar: true,
  });
  const steps = [
    {
      title: '人员与户信息',
      type: baseForm,
      props: {
        isDetail:true
      },
    },{
      title: '宅基地信息',
      type: homestead,
    },{
      title: '家庭承包信息',
      type: contract,
    },{
      title: '土地流转信息',
      type: contract,
      props:{
        type:"土地流转"
      }
    }
  ];
  
  </script>
  
  <style lang="scss" scoped></style>
  
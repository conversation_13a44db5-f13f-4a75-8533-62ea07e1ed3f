import { ElNotification } from 'element-plus';
import Hyperlink from '@/apps/lfpra/common/components/hyperlinkTable/index.vue';
//表格操作
const moreBtnRender = (btnList, operationBtn) => {
  if (btnList.length < 4) {
    return btnList?.map(item => operationBtn[item]);
  } else {
    let a = btnList.slice(0, 2),
      b = btnList.slice(2);
    return [
      ...a.map(item => operationBtn[item]),
      <el-popover placement="bottom">
        {{
          default: (
            <div style="display: flex;flex-direction: column;justify-content: space-around;align-items: center;gap: 12px">
              {...b.map(item => operationBtn[item])}
            </div>
          ),
          reference: <Hyperlink text={'更多'}></Hyperlink>
        }}
      </el-popover>
    ];
  }
};

// 导出表格
const expotrFunction = async ({ url, params, method = 'post', FileName, FileType = 'xls' }) => {
  let resData = await $http[method](url, params, {
    responseType: 'blob'
  });
  if (resData.type == 'application/json') {
    const reader = new FileReader();
    reader.onload = function () {
      const result = JSON.parse(reader.result); //此处的msg就是后端返回的msg内容
      if (result.success === false) {
        ElNotification({ title: result.message, type: 'error' });
      }
    };
    reader.readAsText(resData);
  } else {
    let downloadElement = document.createElement('a');
    let href = window.URL.createObjectURL(resData); //创建下载的链接
    downloadElement.href = href;
    downloadElement.download = FileName + '.' + FileType; //下载后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); //点击下载
    document.body.removeChild(downloadElement); //下载完成移除元素
  }
};

/**
 * @description 格式化金额数字
 * @param {Number} num
 * **/
const erm_intl = (num) => {

  if (num === null || num === "" || num === undefined) {
    return "--";
  } else {
    num = num === void 0 || num === null || num === '' ? 0 : num
    let isNegativeNumber = num < 0
    const formatter = new Intl.NumberFormat('zh-CN', {
      // style: 'currency',
      // 输出人民币
      currency: 'CNY',
      // currencySign选项启用记帐格式
      currencySign: 'accounting',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
    let m = formatter.format(Math.abs(num))
    const insertStr = (source, newStr) => {
      return newStr + source
    }

    return isNegativeNumber ? insertStr(m, '-') : m;
  }
}
//获取当前系统时间
const getNowTime = ()=>{
  let time = new Date();
  //获取年月日，时分秒
  let year = time.getFullYear();
  let month = time.getMonth() + 1;
  let day = time.getDate();
  let hours = time.getHours();
  let min = time.getMinutes();
  let seconds = time.getSeconds();
  const getTime= (e)=>{
   let str =  e < 10?'0'+e:e
    return str
  }
  let timeToString = `${year}${getTime(month)}${getTime(day)}`
  return timeToString
}

// 小写转大写
function toChineseNum(num) {
  var chineseNumArr = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"]; // 定义中文数字数组
  var chineseUnitArr = ["", "十", "百", "千", "万", "亿"]; // 定义中文数字单位数组
  var chineseStr = ""; // 定义中文数字字符串

  num = parseInt(num); // 将数字转换为整数
  if (isNaN(num)) { // 判断是否为数字
    return "不是有效的数字！";
  }

  if (num < 0) { // 处理负数
    chineseStr = "负";
    num = Math.abs(num);
  }

  // 处理整数部分
  var integerStr = num.toString(); // 转换为字符串
  var integerLen = integerStr.length; // 整数部分位数

  if (integerLen > chineseUnitArr.length * 2) { // 判断是否超出范围
    return "超出范围！";
  }

  // 遍历整数部分每一位
  for (var i = 0; i < integerLen; i++) {
    var digit = integerStr.charAt(i); // 取当前位的数字
    var unitPos = integerLen - i - 1; // 计算当前位数和中文数字单位的对应关系
    var unit = chineseUnitArr[unitPos % 6]; // 取单位
    if (digit == '0') { // 处理零
      if (unitPos % 6 == 0 && chineseStr.charAt(chineseStr.length - 1) !== chineseNumArr[0]) { // 当前位为万或亿的第一位，且前一位不是零
        chineseStr += chineseNumArr[0];
      }
    } else { // 处理非零
      chineseStr += chineseNumArr[digit] + unit;
    }
  }

  return chineseStr;
}
export { moreBtnRender,expotrFunction,erm_intl,toChineseNum,getNowTime}

<template>
  <div>
    <funi-detail :bizName="bizName" :showHead="true" :steps="steps" :detailHeadOption="detailHeadOption"
      ><template #headTitle="{ option }">
        <span class="biz-title">
          <div>
            {{ option.title }}
            <el-button type="primary" style="margin-left: 10px" @click="toDoc">档案信息</el-button>
          </div>
          <el-button :type="data.signed=='已完成签约'?'success':'warning'">{{ data.signed }}</el-button>
        </span>
      </template>
    </funi-detail>
  </div>
</template>

<script setup>
import { reactive, computed, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import baseForm from "./components/baseForm.vue";
import contractTemplate from "./landWander/components/contractTemplate.vue";
import contractSupplement from "./landWander/components/contractSupplement.vue";
import { contractInfo } from "@/apps/api/contract.js";

const route = useRoute();
const router = useRouter();
const bizName = ref(route.query.bizName);

const data = ref({});

//详情
const { id } = route.query;
if (id) {
  contractInfo({ uuid: id }, { isLoading: true }).then((res) => {
    data.value = res;
  });
}

// 头部数据
const detailHeadOption = reactive({
  title: route.query.title,
  hideStatusBar: true,
});
const steps = computed(() => {
  return [
    {
      title: "宅基地信息",
      type: baseForm,
      props: {
        isDetail: true,
        data: data.value,
      },
    },
    {
      title: "出租协议",
      preservable: false,
      type: contractTemplate,
      props: {
        isDetail: true,
        transProtocol: data.value.transProtocol,
      },
    },
    {
      title: "补充协议",
      type: contractSupplement,
      props: {
        isDetail: true,
        transProtocolSup: data.value.transProtocolSup,
      },
    },
  ];
});

function toDoc() {
  router.push({
    name: "FileManageWanderContractDetail",
    query: {
      id: route.query.id,
      title: "土地流转合同",
      bizName: "详情",
      tab: `土地流转-${route.query.contractNo}-详情`,
    },
  });
}
</script>

<style lang="scss" scoped>
.biz-title {
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: #1a233b;
  text-align: left;
  vertical-align: top;
  display: flex;
  width: 100%;
  justify-content: space-between;
}
</style>

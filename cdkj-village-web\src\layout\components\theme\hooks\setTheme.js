import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getColors } from 'theme-colors';
import { useLayoutStore } from '@/layout/useLayoutStore';
import AppApis from '@/apis/app';
const Utils = {
  clone:(data)=>JSON.parse(JSON.stringify(data)),
  isNil:(val)=>(val === undefined || val === null)
};
let controller = void 0;
export default defineStore('theme_config', () => {
  let defaultConfig = {
    color: '#007fff',
    head_bgc: 'black',
    menu_bgc: 'white',
    size: 'default',
    table: 'default',
    form: 'default',
    menu: 'vertical',
    openTabs: true
  };
  const themeConfig = ref(Utils.clone(defaultConfig, true));

  const configData = ref({});
  /**
   * @description 重置配置
   * **/
  const resetConfig = () => {
    localStorage.removeItem("_theme")
    getInitConfig()
  };
  /**
   * @description 初始化请求
   * **/
  const getInitConfig = async () => {
    // let data = await AppApis.unifyAccountConfigDetailMy()
    // configData.value = data
    let extendParams = {
      color: '#007fff',
      head_bgc: '#000000',
      menu_bgc: '#ffffff',
      size: 'small',
      table: 'default',
      form: 'default',
      menu: 'vertical',
      openTabs: true,
      personalizedRecommend: false,
      disablePersonalizedRecommend: true,
      allowRecommendation: '1'
    };
    let _theme =localStorage.getItem("_theme")
    if(_theme){
      extendParams = JSON.parse(_theme)
    }
    setThemeConfig(extendParams, false);
  };
  /**
   * @description 设置全局样式
   * @param {object} extendParams 扩展信息
   * @param {true | false} bool 是否请求后端
   ***/
  const setThemeConfig = async (extendParams, bool = false) => {
    let enums = {
      color: setGlobalColor,
      head_bgc: setHeadColor,
      menu_bgc: setMenuColor,
      size: setGlobalFontSize,
      // menu: setMenuLayout,
      openTabs: setOpenTabs,
      table: setTableBorder,
      form: setFormBorder
    };
    if (extendParams) {
      Object.keys(extendParams).forEach(key => {
        themeConfig.value[key] = extendParams[key];
        if (enums[key] && typeof enums[key] === 'function') enums[key](extendParams[key]);
      });
      if (bool) setUserThemeConfig();
    }
  };
  /**
   * @description 设置全局color
   * @param {string} value color值
   * @param {true | false} bool 是否请求api存储数据
   ***/
  const setGlobalColor = (value, bool = false) => {
    if (!value) return;
    document.documentElement.style.setProperty('--el-color-primary', value);
    document.documentElement.style.setProperty('--aside-menu-hover-bg-color', value+"40");
    document.documentElement.style.setProperty('--aside-menu-active-bg-color', value+"40");
    bool && setUserThemeConfig()
  };

  const setMenuColor = (value,bool)=>{
    document.documentElement.style.setProperty('--aside-menu-bg-color', value);
    if($utils.isLightColor(value)){
      document.documentElement.style.setProperty('--el-menu-text-color', 'var(--el-text-color-primary)');
    }
    else{
      document.documentElement.style.setProperty('--el-menu-text-color', 'var(--el-text-color-white)');
    }
    bool && setUserThemeConfig()
  }

  const setHeadColor = (value,bool)=>{
    document.documentElement.style.setProperty('--header-bg-color', value);
    if($utils.isLightColor(value)){
      document.documentElement.style.setProperty('--header-text-color', 'var(--el-text-color-primary)');
    }
    else{
      document.documentElement.style.setProperty('--header-text-color', 'var(--el-text-color-white)');
    }
    bool && setUserThemeConfig()
  }

  /**
   * @description 设置菜单导航模式
   * @param {'vertical' | 'horizontal'} value color值
   * @param {true | false} bool 是否请求api存储数据
   * **/
  const setMenuLayout = (value, bool = false) => {
    if (!value) return;
    const layoutStore = useLayoutStore();
    layoutStore.toggleLayoutMode(value);
    bool && setUserThemeConfig()
  };

  /**
   * @description 设置全局字体大小
   * @param {'default' | 'large' | 'small'} value color值
   * @param {true | false} bool 是否请求api存储数据
   * **/
  const setGlobalFontSize = (value, bool = false) => {
    let enums = {
      default: {
        '--el-font-size-extra-large': '20px',
        '--el-font-size-large': '18px',
        '--el-font-size-medium': '16px',
        '--el-font-size-base': '14px',
        '--el-font-size-small': '13px',
        '--el-font-size-extra-small': '12px'
      },
      large: {
        '--el-font-size-extra-large': '22px',
        '--el-font-size-large': '20px',
        '--el-font-size-medium': '18px',
        '--el-font-size-base': '16px',
        '--el-font-size-small': '14px',
        '--el-font-size-extra-small': '13px'
      },
      small: {
        '--el-font-size-extra-large': '18px',
        '--el-font-size-large': '16px',
        '--el-font-size-medium': '14px',
        '--el-font-size-base': '13px',
        '--el-font-size-small': '12px',
        '--el-font-size-extra-small': '12px'
      }
    };
    if (!value || !enums[value]) return;
    Object.keys(enums[value]).forEach(key => {
      document.documentElement.style.setProperty(key, enums[value][key]);
    });
    bool && setUserThemeConfig()
  };

  /**
   * @description 设置是否开启都多页签
   * @param {true | false} value true开启，false关闭
   * @param {true | false} bool 是否请求api存储数据
   * **/
  const setOpenTabs = (value, bool = false) => {
    if (Utils.isNil(value)) return;
    bool && setUserThemeConfig()
  };

  /**
   * @description 表格网格设置
   * @param {'default' | 'border'}  value
   * @param {true | false}  bool
   * **/
  const setTableBorder = (value, bool = false) => {
    let enums = {
      default: {
        '--funi-curd-border-right': 'none',
        '--funi-curd-border-around-width': '0'
      },
      border: {
        '--funi-curd-border-right': '1px solid var(--el-border-color-lighter)',
        '--funi-curd-border-around-width': '1px'
      }
    };
    if (!value || !enums[value]) return;
    Object.keys(enums[value]).forEach(key => {
      document.documentElement.style.setProperty(key, enums[value][key]);
    });

    bool && setUserThemeConfig()
  };
  /**
   * @description 表单网格设置
   * @param {'default' | 'border'}  value
   * @param {true | false}  bool
   * **/
  const setFormBorder = (value, bool = false) => {
    let enums = {
      default: {
        '--funi-form-border-color': '#d3d3d3',
        '--funi-form-label-bgc': '#fff'
      },
      border: {
        '--funi-form-border-color': 'var(--el-border-color-lighter)',
        '--funi-form-label-bgc': '#f8f8f8'
      }
    };
    if (!value || !enums[value]) return;
    Object.keys(enums[value]).forEach(key => {
      document.documentElement.style.setProperty(key, enums[value][key]);
    });

    bool && setUserThemeConfig()
  };

  const setUserThemeConfig = () => {
    localStorage.setItem("_theme",JSON.stringify(themeConfig.value))
  };

  return {
    themeConfig,
    setThemeConfig,
    setGlobalColor,
    setMenuColor,
    setUserThemeConfig,
    setMenuLayout,
    setHeadColor,
    setGlobalFontSize,
    setOpenTabs,
    resetConfig,
    getInitConfig,
    setTableBorder,
    setFormBorder
  };
});

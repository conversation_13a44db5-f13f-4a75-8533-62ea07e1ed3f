package cn.fight.village.domain.contract.service;

import cn.fight.village.domain.common.constant.BusinessConstant;
import cn.fight.village.domain.common.entity.BaseEntity;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.domain.contract.entity.*;
import cn.fight.village.domain.contract.enumtation.ContractType;
import cn.fight.village.domain.contract.repository.*;
import cn.fight.village.domain.contract.request.ContractQuery;
import cn.fight.village.domain.contract.request.ContractRequest;
import cn.fight.village.domain.contract.value.ContractListVo;
import cn.fight.village.domain.contract.value.ContractValue;
import cn.fight.village.domain.contract.value.HouseHoldContractVo;
import cn.fight.village.domain.doc.entity.Document;
import cn.fight.village.domain.doc.repository.DocumentMapper;
import cn.fight.village.domain.doc.service.DocumentService;
import cn.fight.village.domain.land.entity.RealLand;
import cn.fight.village.domain.land.repository.GisLandRepository;
import cn.fight.village.domain.land.repository.RealLandRepository;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.domain.user.service.UserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.net.BindException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 承包-流转服务层
 *
 */
@Service
public class ContractService {
    @Resource
    private ContractMapper contractMapper;

    @Resource
    private LandMapper landMapper;

    @Resource
    private UnderMapper underMapper;

    @Resource
    private UpperMapper upperMapper;

    @Resource
    private UnderMemberMapper underMemberMapper;

    @Resource
    private DocumentService documentService;

    @Resource
    private TransProtocolRepository transProtocolRepository;

    @Resource
    private TransProtocolSupRepository transProtocolSupRepository;

    @Resource
    private TransProtocolLandRepository transProtocolLandRepository;

    @Resource
    private RealLandRepository realLandRepository;

    @Resource
    private GisLandRepository gisLandRepository;

    @Resource
    private DocumentMapper documentMapper;

    @Transactional(rollbackFor = Exception.class)
    public JsonResult addContract(ContractRequest request, User user) {
        String type = request.getType();
        String householdId = request.getHouseholdId(); //家庭户ID

        CommonUtils.notNull(type,"合同类型不能为空");
        if (!(type.equals(ContractType.FAMILY_CONTRACT.getCode()) || type.equals(ContractType.LAND_TRANS.getCode()))) {
            throw new BusinessException("合同类型不正确");
        }

        String contractId = request.getUuid();
        boolean isUpdate = StringUtils.hasText(contractId); //编辑标识

        //校验流水号重复
        Contract existContract = this.selectByContractNo(request.getContractNo());
        if (isUpdate) {
            if (existContract != null && !existContract.getUuid().equals(request.getUuid())) {
                throw new BusinessException("合同流水号不允许重复");
            }
        } else {
            if (!StringUtils.hasText(request.getContractNo())) {
                throw new BusinessException("合同流水号必填");
            }

            if (existContract != null) {
                throw new BusinessException("合同流水号不允许重复");
            }
        }

        //管理合同信息
        Contract contractSaver = new Contract();
        contractSaver.setHouseholdId(householdId);
        if (isUpdate) {
            //编辑
            contractSaver.setUuid(contractId);
            BeanUtils.copyProperties(request,contractSaver);
            contractSaver.manageUpdateInfo(user);
            if (contractMapper.updateById(contractSaver) != 1) {
                throw new BusinessException("修改合同信息失败");
            }
        } else {
            //新增
            BeanUtils.copyProperties(request,contractSaver);
            contractSaver.manageCreateInfo(user);
            if (contractMapper.insert(contractSaver) != 1) {
                throw new BusinessException("添加合同信息失败");
            }
        }
        contractId = contractSaver.getUuid();

        /**********************维护土地信息********************/
        List<Land> landList = request.getLandList();
        if (isUpdate) {
            List<Land> existLands = this.selectLandByContractId(contractId);
            List<String> removeList = existLands.stream().map(Land::getUuid).collect(Collectors.toList());

            //编辑土地信息
            if (!CollectionUtils.isEmpty(landList)) {
                for (Land land : landList) {
                    //有ID编辑土地对象
                    if (StringUtils.hasText(land.getUuid())) {
                        land.manageUpdateInfo(user);
                        landMapper.updateById(land);

                        //剔除已存在的列表
                        removeList.remove(land.getUuid());
                    }
                    //新增土地对象
                    else {
                        land.manageCreateInfo(user);
                        land.setContractId(contractSaver.getUuid());
                        landMapper.insert(land);
                    }

                    //删除不存在的
                    if (!CollectionUtils.isEmpty(removeList)) {
                        this.removeLandByList(removeList,user);
                    }
                }
            } else  {
                //移除所有信息
                this.removeLandByList(removeList,user);
            }
        } else {
            this.insertLand(landList,user,contractId);
        }

        /******************添加发包方信息***********************/
        List<Upper> upperList = request.getUpperList();
        if (isUpdate) {
            //编辑
            List<Upper> existUpper = this.selectUpperByContractId(contractId);
            List<String> removeList = existUpper.stream().map(Upper::getUuid).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(upperList)) {
                for (Upper upper : upperList) {
                    //数据加密
                    SecureUtils.sensitiveFieldEncrypt(upper);

                    //编辑
                    if (StringUtils.hasText(upper.getUuid())) {
                        upper.manageUpdateInfo(user);
                        upperMapper.updateById(upper);

                        removeList.remove(upper.getUuid());
                    } else {
                        //新增
                        upper.manageCreateInfo(user);
                        upper.setContractId(contractId);
                        upperMapper.insert(upper);
                    }

                    if (!CollectionUtils.isEmpty(removeList)) {
                        this.removeUpperByList(removeList, user);
                    }
                }
            } else {
                //清空所有发包方信息
                this.removeUpperByList(removeList, user);
            }
        } else {
            //新增
            this.insertUpper(upperList,user,contractId);
        }

        /**************************添加承包方信息*******************************/
        List<Under> underList = request.getUnderList();
        if (isUpdate) {
            //编辑
            List<Under> extisUnders = this.selectUnderByContractId(contractId);
            List<String> removeList = extisUnders.stream().map(Under::getUuid).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(underList)) {
                for (Under under : underList) {
                    //敏感数据加密
                    SecureUtils.sensitiveFieldEncrypt(under);

                    //编辑
                    if (StringUtils.hasText(under.getUuid())) {
                        under.manageUpdateInfo(user);
                        underMapper.updateById(under);

                        removeList.remove(under.getUuid());

                        //编辑成员信息
                        this.updateUnderMember(removeList, under.getUuid(), under.getUnderMembersList(), user);
                    } else {
                        //新增
                        this.insertUnder(under, user, contractId, householdId);
                    }
                }
                if (!CollectionUtils.isEmpty(removeList)) {
                    this.removeUnderByIds(removeList, user);
                }
            } else {
                //删除所有
                this.removeUnderByIds(removeList,user);
            }
        } else {
            //新增
            if (CollectionUtils.isEmpty(underList)) {
                throw new BusinessException("承包方信息不能为空");
            }

            for (Under under : underList) {
                this.insertUnder(under,user,contractId,householdId);
            }
        }

        /******************************生成合同信息************************/
        if (!isUpdate) {
            documentService.createDocument(contractSaver,user);
        }

        return JsonResult.valueOfObject(contractSaver.getUuid());
    }

    /**
     * 根据流水号获取合同
     *
     * @param contractNo
     * @return
     */
    private Contract selectByContractNo(String contractNo) {
        if (!StringUtils.hasText(contractNo)) {
            return null;
        }

        return contractMapper.selectOne(new LambdaQueryWrapper<Contract>()
            .eq(Contract::getContractNo,contractNo)
                .eq(Contract::getDeleted,Contract.IS_NOT_DELETED)
        );
    }

    /**
     * 新增土地列表
     *
     * @param landList
     * @param user
     * @param contractId
     */
    private void insertLand(List<Land> landList, User user, String contractId) {
        //新增
        if (CollectionUtils.isEmpty(landList)) {
            throw new BusinessException("土地信息不能为空");
        }

        for (Land land : landList) {
            land.manageCreateInfo(user);
            land.setContractId(contractId);
            landMapper.insert(land);
        }
    }

    /**
     * 更新承包方成员信息
     *
     * @param removeList 承包方删除列表
     * @param underId
     * @param underMembersList
     * @return
     */
    private int updateUnderMember(List<String> removeList,
                                  String underId,
                                  List<UnderMembers> underMembersList,
                                  User user
    ) {
        //删除所有承包方人员
        if (removeList.contains(underId) || CollectionUtils.isEmpty(underMembersList)) {
            return this.removeMemberByUnderId(underId,user);
        }

        int rows = 0;
        //更新承包方成员信息
        List<UnderMembers> existMembers = this.selectMemberByUnderId(underId);
        List<String> memberRemoveList = existMembers.stream().map(UnderMembers::getUuid).collect(Collectors.toList());
        for (UnderMembers underMembers : underMembersList) {
            SecureUtils.sensitiveFieldEncrypt(underMembers);

            //编辑
            if (StringUtils.hasText(underMembers.getUuid())) {
                underMembers.manageUpdateInfo(user);
                rows +=underMemberMapper.updateById(underMembers);

                memberRemoveList.remove(underMembers.getUuid());
            } else {
                //新增
                underMembers.manageCreateInfo(user);
                underMembers.setUnderId(underId);
                rows += underMemberMapper.insert(underMembers);
            }

            //删除
            rows +=this.removeMemberByIdList(memberRemoveList,user);
        }

        return rows;
    }

    /**
     * 根据ID删除承包方共有人
     *
     * @param memberRemoveList
     * @return
     */
    private int removeMemberByIdList(List<String> memberRemoveList,User user) {
        if (CollectionUtils.isEmpty(memberRemoveList)) {
            return 0;
        }

        int rows = 0;
        for (String memberId : memberRemoveList) {
            UnderMembers deleter = new UnderMembers();
            deleter.setUuid(memberId);
            deleter.manageDeleteInfo(user);
            rows += underMemberMapper.updateById(deleter);
        }

        return rows;
    }

    /**
     * 根据承包方ID获取承包方成员列表
     *
     * @param underId
     * @return
     */
    private List<UnderMembers> selectMemberByUnderId(String underId) {
        if (!StringUtils.hasText(underId)) {
            return Collections.emptyList();
        }

        List<UnderMembers> underMembers = underMemberMapper.selectList(new LambdaQueryWrapper<UnderMembers>()
                .eq(UnderMembers::getUnderId,underId)
                .eq(UnderMembers::getDeleted,Land.IS_NOT_DELETED)
        );

        if (!CollectionUtils.isEmpty(underMembers)) {
            for (UnderMembers underMember : underMembers) {
                SecureUtils.sensitiveFieldDecrypt(underMember);
            }
        }

        return underMembers;
    }

    /**
     * 删除承包方成员
     *
     * @param underId 承包方ID
     * @return
     */
    private int removeMemberByUnderId(String underId,User user) {
        if (!StringUtils.hasText(underId)) {
            return 0;
        }

        UnderMembers deleter = new UnderMembers();
        deleter.manageDeleteInfo(user);
        return underMemberMapper.update(deleter,new LambdaUpdateWrapper<UnderMembers>()
            .eq(UnderMembers::getUnderId,underId)
        );
    }

    /**
     * 删除承包方信息
     *
     * @param removeList
     * @param user
     * @return
     */
    private int removeUnderByIds(List<String> removeList, User user) {
        if (CollectionUtils.isEmpty(removeList)) {
            return 0;
        }

        int rows = 0;
        for (String id : removeList) {
            Under under = new Under();
            under.manageDeleteInfo(user);
            under.setUuid(id);
            rows += underMapper.updateById(under);

            this.removeMemberByUnderId(id,user);
        }

        return rows;
    }


    /**
     * 添加承包方信息
     *
     * @param upperList
     * @param user
     * @param contractId
     */
    public void insertUpper(List<Upper> upperList,User user,String contractId) {
        if (CollectionUtils.isEmpty(upperList)) {
            throw new BusinessException("发包方信息不能为空");
        }

        for (Upper upper : upperList) {
            upper.manageCreateInfo(user);
            upper.setContractId(contractId);

            upperMapper.insert(upper);
        }
    }

    /**
     * 插入承包方信息
     *
     * @param under
     * @param user
     * @param contractId
     * @param householdId
     */
    public void insertUnder(Under under,User user,String contractId,String householdId) {
        under.manageCreateInfo(user);
        under.setContractId(contractId);
        under.setUnderId(householdId);
        underMapper.insert(under);

        List<UnderMembers> underMembersList = under.getUnderMembersList();
        if (underMembersList != null) {
            for (UnderMembers underMembers : underMembersList) {
                SecureUtils.sensitiveFieldEncrypt(underMembers);

                underMembers.manageCreateInfo(user);
                underMembers.setUnderId(under.getUuid());
                underMemberMapper.insert(underMembers);
            }
        }
    }

    /**
     * 删除发包方信息
     *
     * @param removeList
     * @param user
     * @return
     */
    private int removeUpperByList(List<String> removeList,User user) {
        if (CollectionUtils.isEmpty(removeList)) {
            return 0;
        }

        int rows = 0;
        for (String upperId : removeList) {
            Upper deleter = new Upper();
            deleter.setUuid(upperId);
            deleter.manageDeleteInfo(user);
            rows += upperMapper.updateById(deleter);
        }

        return rows;
    }

    /**
     * 删除土地列表
     *
     * @param removeList
     * @return
     */
    private int removeLandByList(List<String> removeList,User user) {
        if (CollectionUtils.isEmpty(removeList)) {
            return 0;
        }

        int rows = 0;
        for (String landId : removeList) {
            Land deleter = new Land();
            deleter.setUuid(landId);
            deleter.manageDeleteInfo(user);
            rows += landMapper.updateById(deleter);
        }

        return rows;
    }

    /**
     * 获取合同下的所有土地
     *
     * @param contractId
     * @return
     */
    public List<Land> selectLandByContractId(String contractId) {
        if (!StringUtils.hasText(contractId)) {
            return Collections.emptyList();
        }

        return landMapper.selectList(new LambdaQueryWrapper<Land>()
            .eq(Land::getContractId,contractId)
                .eq(Land::getDeleted,Land.IS_NOT_DELETED)
        );
    }

    /**
     * 获取合同下的所有发包房
     *
     * @param contractId
     * @return
     */
    public List<Upper> selectUpperByContractId(String contractId) {
        if (!StringUtils.hasText(contractId)) {
            return Collections.emptyList();
        }

        List<Upper> uppers = upperMapper.selectList(new LambdaQueryWrapper<Upper>()
                .eq(Upper::getContractId, contractId)
                .eq(Upper::getDeleted, Land.IS_NOT_DELETED)
        );

        //敏感数据解密
        if (!CollectionUtils.isEmpty(uppers)) {
            for (Upper upper : uppers) {
                SecureUtils.sensitiveFieldDecrypt(upper);
            }
        }

        return uppers;
    }

    /**
     * 获取所有承包方信息
     *
     * @param contractId
     * @return
     */
    private List<Under> selectUnderByContractId(String contractId) {
        if (!StringUtils.hasText(contractId)) {
            return Collections.emptyList();
        }

        List<Under> unders = underMapper.selectList(new LambdaQueryWrapper<Under>()
                .eq(Under::getContractId,contractId)
                .eq(Under::getDeleted,Land.IS_NOT_DELETED)
        );

        //敏感数据解密
        if (!CollectionUtils.isEmpty(unders)) {
            for (Under under : unders) {
                SecureUtils.sensitiveFieldDecrypt(under);
            }
        }

        return unders;
    }

    /**
     * 合同列表分页查询
     *
     * @param query
     * @return
     */
    public JsonResult getList(ContractQuery query) {
        String type = query.getType();
        CommonUtils.notNull(type,"合同类型不能为空");

        PageHelper.startPage(query.getPageNo(),query.getPageSize());
        List<ContractListVo> result = null;

        //土地流转数据处理
        if (ContractType.LAND_TRANS.getCode().equals(type)) {
            result = contractMapper.getList4Trans(query);

            for (ContractListVo contractListVo : result) {
                SecureUtils.sensitiveFieldDecrypt(contractListVo);
            }
        } else if (ContractType.FAMILY_CONTRACT.getCode().equals(type)) {
             result = contractMapper.getList(query);
        } else {
            throw new BusinessException("合同类型不正确");
        }

        return JsonResult.valueOfObject(new PageInfo<>(result));
    }

    /**
     * 合同删除
     *
     * @param request
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult remove(ContractRequest request, User user) {
        String contractId = request.getUuid();
        CommonUtils.notNull(contractId,"待删除业务件号不能为空");

        //删除合同
        Contract deleter = new Contract();
        deleter.manageDeleteInfo(user);
        deleter.setUuid(contractId);

        if (contractMapper.updateById(deleter) != 1) {
            throw new BusinessException("删除失败");
        }

        //删除土地
        List<Land> lands = this.selectLandByContractId(contractId);
        if (!CollectionUtils.isEmpty(lands)) {
            this.removeLandByList(lands.stream().map(Land::getUuid).collect(Collectors.toList()), user);
        }

        //删除发包方
        List<Upper> uppers = this.selectUpperByContractId(contractId);
        if (!CollectionUtils.isEmpty(uppers)) {
            this.removeUpperByList(uppers.stream().map(Upper::getUuid).collect(Collectors.toList()), user);
        }

        //删除承包方
        List<Under> unders = this.selectUnderByContractId(contractId);
        if (!CollectionUtils.isEmpty(unders)) {
            List<String> underIds = unders.stream().map(Under::getUuid).collect(Collectors.toList());
            this.removeUnderByIds(underIds, user);

            for (String underId : underIds) {
                this.removeMemberByUnderId(underId,user);
            }
        }

        return JsonResult.successMessage("删除成功");
    }

    /**
     * 详情查询
     *
     * @param uuid
     * @return
     */
    public JsonResult info(String uuid) {
        CommonUtils.notNull(uuid,"查询对象不能为空");

        ContractValue result = new ContractValue();
        Contract contract = contractMapper.selectById(uuid);
        if (contract == null || Contract.IS_DELETED.equals(contract.getDeleted())) {
            throw new BusinessException("业务件不存在，请刷新列表");
        }
        BeanUtils.copyProperties(contract,result);

        String type = contract.getType();
        //承包土地类型
        if (ContractType.FAMILY_CONTRACT.getCode().equals(type)) {
            result.setLandList(this.selectLandByContractId(uuid));
            result.setUpperList(this.selectUpperByContractId(uuid));

            List<Under> unders = this.selectUnderByContractId(uuid);
            if (!CollectionUtils.isEmpty(unders)) {
                for (Under under : unders) {
                    under.setUnderMembersList(this.selectMemberByUnderId(under.getUuid()));
                }

                result.setUnderList(unders);
            }
        }
        //土地流转类型
        else if (ContractType.LAND_TRANS.getCode().equals(type)) {
            //获取流转协议
            TransProtocol protocol = this.getTransProtocolByContractId(uuid);
            if (protocol == null) {
                throw new BusinessException("获取土地流转合同失败");
            }
            //是否完成签署
            String signed = BusinessConstant.YES.equals(contract.getSigned()) ? "已完成签约" : "未完成签约";
            result.setSigned(signed);

            //获取土地流转列表
            result.setLandList(realLandRepository.selectLandsByProtocol(protocol.getUuid()));
            //result.setLandList(transProtocolLands);

            //获取发包方信息
            Upper upper = new Upper();
            upper.setUpper(protocol.getPartyA());
            upper.setUpperName(protocol.getPartyA());
            upper.setUpperIdType("居民身份证");
            upper.setUpperPhone(protocol.getbPhone());
            upper.setUpperIdNo(protocol.getaId());
            upper.setUpperLocation(protocol.getaLocation());
            List<Upper> uppers = new ArrayList<>();
            uppers.add(upper);
            //SecureUtils.sensitiveFieldDecrypt(upper);
            result.setUpperList(uppers);

            //获取承包方信息
            Under under = new Under();
            under.setUpper(protocol.getPartyB());
            under.setUnderName(protocol.getbReprese());
            under.setUnderId(protocol.getbRepId());
            under.setUnderIdType("居民身份证");
            under.setUnderNo(protocol.getbId());
            under.setUnderLocation(protocol.getbLocation());
            under.setUnderPhone(protocol.getbPhone());
            under.setRightArea(protocol.getTotalArea()); //总面积
            List<Under> unders = new ArrayList<>();
            unders.add(under);
            result.setUnderList(unders);

            result.setTransProtocol(protocol);
            result.setTransProtocolSup(getTransProtocolSupByContractId(uuid));
        } else {
            throw new BusinessException("合同类型不正确");
        }

        return JsonResult.valueOfObject(result);
    }

    /**
     * 根据合同id获取土地流转协议
     *
     * @param contractId
     * @return
     */
    private TransProtocol getTransProtocolByContractId(String contractId) {
        if (!StringUtils.hasText(contractId)) {
            return null;
        }

        LambdaQueryWrapper<TransProtocol> query = new LambdaQueryWrapper<TransProtocol>()
                .eq(TransProtocol::getContractId, contractId)
                .eq(TransProtocol::getDeleted, Land.IS_NOT_DELETED);


        TransProtocol transProtocol = transProtocolRepository.selectOne(query);
        SecureUtils.sensitiveFieldDecrypt(transProtocol);

        //获取对应土地
        LambdaQueryWrapper<TransProtocolLand> landQuery = new LambdaQueryWrapper<TransProtocolLand>()
                .eq(TransProtocolLand::getProtocolId, transProtocol.getUuid());

        List<TransProtocolLand> transProtocolLands = transProtocolLandRepository.selectList(landQuery);
        transProtocol.setLands(transProtocolLands);

        return transProtocol;
    }

    /**
     * 根据合同id获取土地流转协议补充协议
     *
     * @param contractId
     * @return
     */
    private TransProtocolSup getTransProtocolSupByContractId(String contractId) {
        if (!StringUtils.hasText(contractId)) {
            return null;
        }

        LambdaQueryWrapper<TransProtocolSup> query = new LambdaQueryWrapper<TransProtocolSup>()
                .eq(TransProtocolSup::getContractId, contractId)
                .eq(TransProtocolSup::getDeleted, Land.IS_NOT_DELETED);


        TransProtocolSup transProtocolSup = transProtocolSupRepository.selectOne(query);
        SecureUtils.sensitiveFieldDecrypt(transProtocolSup);
        return transProtocolSup;
    }

    /**
     * 根据户ID获取户承包地信息
     *
     * @param householdId
     * @return
     */
    public JsonResult queryByHousehold(String householdId,String type) {
        CommonUtils.notNull(householdId,"查询对应的户ID不能为空");
        CommonUtils.notNull(type,"查询对应的合同类型不能为空");

        List<HouseHoldContractVo> result = contractMapper.queryLandByHousehold(householdId,type);
        return JsonResult.valueOfObject(result);
    }

    /**
     * 创建土地流转合同
     *
     * @param request
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult createProtocol(ContractRequest request, User user) {
        String type = request.getType();

        CommonUtils.notNull(type,"合同类型不能为空");
        if (!type.equals(ContractType.LAND_TRANS.getCode())) {
            throw new BusinessException("合同类型不正确,只能进行土地流转");
        }

        //生成一个合同主体
        String contractId = CommonUtils.getGuid();
        Contract contract = new Contract();
        contract.manageCreateInfo(user);
        contract.setUuid(contractId);
        contract.setType(ContractType.LAND_TRANS.getCode());
        contract.setContractNo(this.createContractNo());
        contract.setProject(request.getProject()); //归属项目

        //保存协议内容
        String protocolId = CommonUtils.getGuid();
        TransProtocol transProtocol = request.getTransProtocol();
        if (transProtocol == null) {
            throw new BusinessException("协议内容不能为空");
        }
        transProtocol.setContractId(contractId);
        transProtocol.manageCreateInfo(user);
        transProtocol.setUuid(protocolId);

        TransProtocolSup transProtocolSup = request.getTransProtocolSup();
        if (transProtocolSup == null) {
            throw new BusinessException("协议补充内容不能为空");
        }
        transProtocolSup.setContractId(contractId);
        transProtocolSup.manageCreateInfo(user);
        transProtocolSup.setUuid(protocolId);

        //保存关联土地内容
        List<TransProtocolLand> landList = transProtocol.getLands();
        if (CollectionUtils.isEmpty(landList)) {
            throw new BusinessException("请选择关联的土地");
        }
        for (TransProtocolLand transProtocolLand : landList) {
            transProtocolLand.setProtocolId(protocolId);
            transProtocolLandRepository.insert(transProtocolLand);
        }

        //数据脱敏并入库
        SecureUtils.sensitiveFieldEncrypt(transProtocol);
        if (transProtocolRepository.insert(transProtocol) != 1) {
            throw new BusinessException("保存流转协议失败");
        }
        SecureUtils.sensitiveFieldEncrypt(transProtocolSup);
        if (transProtocolSupRepository.insert(transProtocolSup) != 1) {
            throw new BusinessException("保存流转协议补充协议失败");
        }

        if (contractMapper.insert(contract) != 1) {
            throw new BusinessException("保存合同信息失败");
        }

        //生成档案对象
        Document inserter = new Document();
        inserter.manageCreateInfo(user);
        inserter.setDocNo(contract.getContractNo());
        inserter.setPath( BusinessConstant.FILE_PATH + contract.getContractNo());
        inserter.setContractId(contract.getUuid());
        if (documentMapper.insert(inserter) != 1) {
            throw new BusinessException("生成合同档案失败");
        }

        return JsonResult.successMessage("保存成功");
    }

    /**
     * 生成合同号
     * @return
     */
    private String createContractNo(){
        //日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String datePart = dateFormat.format(new Date());

        //序列
        String seqPart = String.format("%04d", realLandRepository.getRealLandNoSec());
        return "TDLZ-" + datePart + seqPart;
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult sureSign(String uuid, String signed, User user) {
        if (BusinessConstant.YES.equals(signed)
                || BusinessConstant.NO.equals(signed)) {
            if (contractMapper.protocolSureSign(uuid,signed,user.getUuid()) != 1) {
                throw new BusinessException("确认签署状态失败");
            }

            //获取该协议下所有土地代码,更新Gis土地信息
            List<String> landNos = this.getLandNoByContractId(uuid);
            if (!CollectionUtils.isEmpty(landNos)) {
                gisLandRepository.landSign(landNos);
            }

        }
        else {
            throw new BusinessException("不正确的确认类型");
        }

        return JsonResult.successMessage("确认完成");
    }

    /**
     * 根据合同ID获取对应地块代码
     * @param contractId
     * @return
     */
    public List<String> getLandNoByContractId(String contractId) {
        if (!StringUtils.hasText(contractId)) {
            return Collections.emptyList();
        }

        return contractMapper.selectTransLandNos(contractId);
    }
}

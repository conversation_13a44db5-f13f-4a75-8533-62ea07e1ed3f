//人员与户信息

import { ElNotification } from 'element-plus'
/**
 * 获取人员与户列表
 * @param {*} data 
 */
export const householdList = (data) => {
    return $http.post("/household/list", data)
}
/**
 * 添加人员与户
 * @param {*} data 
 */
export const householdAdd = (data) => {
    return $http.post("/household/add", data)
}
/**
 * 修改人员与户
 * @param {*} data 
 */
export const householdUpdate = (data) => {
    return $http.post("/household/update", data)
}
/**
 * 删除人员与户
 * @param {*} data 
 */
export const householdRemove = (data) => {
    return $http.post("/household/remove", data).then(res => {
        ElNotification({
            title: '删除成功',
            type: 'success',
        });
        return res
    })
}
/**
 * 获取人员与户信息
 * @param {*} data 
 */
export const householdInfo = (data) => {
    return $http.fetch("/household/info", data)
}
/**
 * 查询家庭成员信息-不分页
 * @param {*} data 
 */
export const householdMembers = (data) => {
    return $http.fetch("/household/homeMembers", data)
}
/**
 * 获取所有家庭户的成员信息
 * @param {*} data 
 */
export const houseMembers = (data) => {
    return $http.post("/household/houseMembers", data)
}
/**
 * 查询户拥有的宅基地
 * @param {*} data 
 */
export const queryByHousehold = (data) => {
    return $http.fetch("/homestead/queryByHousehold", data)
}
/**
 * 查询户拥有的承包流转信息
 * @param {*} data 
 */
export const contractQueryByHousehold = (data) => {
    return $http.fetch("/contract/queryByHousehold", data)
}
/**
 * 户主姓名模糊查询家庭信息
 * @param {*} data 
 */
export const qyeryByHouseholder = (data) => {
    return $http.fetch("/household/qyeryByHouseholder", data)
}
<template>
  <!-- <div class="headBtn"><el-button type="primary">新增</el-button></div> -->
  <div id="FileCurd">
    <funi-curd
      ref="FuniFileCurdRef"
      rowKey="businessFileInstanceInfoId"
      :columns="columns"
      :loading="loading"
      :lodaData="_lodaData"
      :pagination="false"
      default-expand-all
      :header-cell-style="{ 'background-color': '#f9f9f9' }"
      :header-row-style="{ 'background-color': '#f9f9f9' }"
    >
      <template #fileCurd="props">
        <div id="fileCurdView">
          <funi-curd
            rowKey="attachmentInfoId"
            :show-header="false"
            :columns="columns2"
            :data="props.row.attachmentInfos.map(item => Object.assign(item, { index: props.$index }))"
            :pagination="false"
            :stripe="false"
          ></funi-curd>
        </div>
      </template>
    </funi-curd>
  </div>
  <funi-image-view ref="fivRef" :key="imageKey" :urlList="urlList"></funi-image-view>
  <upload
    ref="uploadRef"
    :url="props.uploadFileUrl || uploadFileUrl"
    :remove-file="_deleteFile"
    @on-close="_uploadClose"
  >
  </upload>
</template>

<script setup lang="jsx">
import { nextTick, ref, unref } from 'vue';
import upload from './upload.vue';
import { useAppStore } from '@/stores/useAppStore';
import { ElNotification, ElLoading } from 'element-plus';

defineOptions({
  name: 'FuniFileTable',
  inheritAttrs: false
});

const appStore = useAppStore();
const props = defineProps({
  //仅显示
  onlyShow: {
    type: Boolean,
    default: false
  },
  //隐藏列字段集合
  hideColumns: {
    type: Array,
    default: []
  },
  params: { type: Object, default: {} },
  //附件列表查询接口地址
  fileListUrl: String,
  //删除附件接口地址
  delFileUrl: String,
  //删除附件接口地址
  uploadFileUrl: String,
  //预览附件接口地址
  checkFileUrl: String,
  //预览附件接口参数
  checkFileParams: {
    type: Object,
    default: () => ({})
  },
  //下载附件接口地址
  downloadFileUrl: String,
  //下载附件接口入参
  downloadParams: {
    type: Object,
    default: () => ({})
  },
  isVerification: { type: Boolean, default: true },
  callbackFun: {
    type: Function,
    default: () => {}
  }
});

const fileListUrl = '/bpmn/fileManage/queryBusinessFileInstanceList';
const delFileUrl = '/bpmn/fileManage/deleteAttachment';
const uploadFileUrl = '/bpmn/fileManage/uploadAttachment';
const downloadFileUrl = '/bpmn/businessManage/downloadAttachmentForFile';
const checkFileUrl = '/bpmn/fileManage/downloadAttachment';

const loading = ref(false);
const sysId = ref(props.params.sysId);
const fivRef = ref();
const uploadRef = ref();
const FuniFileCurdRef = ref();
const urlList = ref([]);
const imageKey = ref($utils.guid());

const columns = ref([
  { type: 'expand', slots: { default: 'fileCurd' }, width: '50' },
  {
    label: '附件类型',
    prop: 'businessFileName',
    render: ({ row, index }) => {
      if (row.isNeed == '1' || row.isNeed == 1) {
        return (
          <div>
            <span style="color:red">*</span>
            {row.businessFileName}
          </div>
        );
      } else {
        return row.businessFileName;
      }
    }
  },
  { label: '支持格式', prop: 'fileType', width: '300' },
  {
    label: '是否必收',
    prop: 'isNeed',
    width: '100',
    render: ({ row, index }) => (row.isNeed == '1' || row.isNeed == 1 ? '是' : '否')
  },
  { label: '需要份数', prop: 'needNumber', width: '100' },
  { label: '已传份数', prop: 'attachmentAmount', width: '100' },
  { label: '备注', prop: 'remark', width: '180' }
]);

if (!props.onlyShow) {
  columns.value.push({
    label: '操作',
    prop: 'operList',
    align: 'center',
    // fixed: 'right',
    width: '180px',
    render: ({ row, index }) => {
      return (
        <div>
          <el-button
            type="primary"
            link
            onClick={() => {
              _upload(row);
            }}
          >
            上传
          </el-button>
          {/* <el-button type='primary' link onClick={() => {
                        _edit(row);
                    }}>编辑
                    </el-button> */}
          {/* <el-popconfirm title="确认删除该条数据吗？" onConfirm={() => { _delete(row) }} v-slots={{
                        reference: () => (
                            <el-button type='primary' link>删除</el-button>
                        )
                    }}>
                    </el-popconfirm> */}
        </div>
      );
    }
  });
} else {
  columns.value.push({
    label: '操作',
    prop: 'operList',
    align: 'center',
    // fixed: 'right',
    width: '180px',
    render: ({ row, index }) => {
      return ' ';
    }
  });
}

//过滤隐藏列
columns.value = columns.value.filter(item => !props.hideColumns.includes(item.prop));

const columns2 = ref([
  {
    label: '1',
    prop: '1',
    width: '50',
    render: () => {
      return ' ';
    }
  },
  {
    label: '文件名',
    prop: 'attachmentName',
    render: ({ row, index }) => {
      return (
        <div>
          <el-button
            type="primary"
            link
            onClick={() => {
              _check(row);
            }}
          >
            {row.attachmentName}
          </el-button>
        </div>
      );
    }
  },
  // {
  //   label: '上传时间',
  //   prop: 'uploadTime',
  //   width: '300',
  //   render: ({ row, index }) => {
  //     return `上传时间：${row.uploadTime}`;
  //   }
  // },
  // {
  //   label: '2',
  //   prop: '2',
  //   width: '100',
  //   render: () => {
  //     return ' ';
  //   }
  // },
  // {label: '文件类型', prop: 'attachmentFileType'},
  {
    label: '操作',
    prop: 'operList',
    align: 'center',
    // fixed: 'right',
    width: '180px',
    render: ({ row, index }) => {
      return props.onlyShow ? (
        <div>
          <el-button
            type="primary"
            link
            size="small"
            onClick={() => {
              _check(row);
            }}
          >
            查看
          </el-button>
          <el-button
            type="primary"
            link
            size="small"
            onClick={() => {
              _download(row);
            }}
          >
            下载
          </el-button>
        </div>
      ) : (
        <div>
          <el-button
            type="primary"
            link
            size="small"
            onClick={() => {
              _check(row);
            }}
          >
            查看
          </el-button>
          <el-button
            type="primary"
            link
            size="small"
            onClick={() => {
              _download(row);
            }}
          >
            下载
          </el-button>
          <el-popconfirm
            title="确认删除该条数据吗？"
            onConfirm={() => {
              _deleteFile(row, true);
            }}
            v-slots={{
              reference: () => (
                <el-button type="danger" link size="small">
                  删除
                </el-button>
              )
            }}
          ></el-popconfirm>
        </div>
      );
    }
  }
]);

//获取附件列表
const _lodaData = async () => {
  if (props.params?.businessId) {
    loading.value = true;
    if (!sysId.value) {
      let sysInfo = appStore.system;
      sysId.value = sysInfo.id;
    }
    return $http
      .post(props.fileListUrl || fileListUrl, {
        businessId: props.params.businessId,
        sysId: sysId.value
      })
      .then(res => {
        return res;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

//上传文件
const _upload = row => {
  uploadRef.value.show(
    {
      businessFileInstanceInfoId: row.businessFileInstanceInfoId,
      businessId: props.params?.businessId,
      sysId: sysId.value
    },
    row.fileType,
    row.needNumber
  );
};

//关闭上传弹窗
const _uploadClose = () => {
  FuniFileCurdRef.value.reload();
};

//下载文件
const _download = row => {
  const loading = ElLoading.service({ fullscreen: true });
  let url = props.downloadFileUrl
    ? `${window.location.origin}/${$utils.getTenantID()}${props.downloadFileUrl}`
    : `${window.location.origin}/${$utils.getTenantID()}${downloadFileUrl}?attachmentInfoId=${row.attachmentInfoId}`;
  window.$http
    .downloadFile(
      url,
      props.downloadFileUrl
        ? {
            attachmentInfoId: row.attachmentInfoId,
            ...props.downloadParams
          }
        : {}
    )
    .then(() => {
      loading.close();
    })
    .catch(() => {
      loading.close();
    });
};

//查看文件
const _check = row => {
  console.log('row====>', row);
  let params = {
    attachmentInfoId: row.attachmentInfoId,
    ...props.checkFileParams
  };
  let url = `${window.location.origin}/${$utils.getTenantID()}${props.checkFileUrl || checkFileUrl}?${json2GetParams(
    params
  )}`;
  if (isImage(row.attachmentFileType)) {
    imageKey.value = $utils.guid();
    nextTick(() => {
      urlList.value = [url];
      fivRef.value.showViewer();
    });
  } else {
    window.open(
      `${window.location.href.split('#')[0]}#/common/filePreview?src=${encodeURIComponent(url)}&type=${
        row.attachmentFileType
      }`
    );
  }
};

const json2GetParams = obj =>
  Object.entries(obj)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

//是否图片格式
const isImage = ext => {
  return (
    ['png', 'PNG', 'jpg', 'JPG', 'jpeg', 'JPEG', 'gif', 'GIF', 'webp', 'WEBP', 'svg', 'SVG'].indexOf(
      ext.toLowerCase()
    ) !== -1
  );
};

//删除附件
const _deleteFile = (row, isRefresh) => {
  return $http
    .post(props.delFileUrl || delFileUrl, {
      attachmentInfoId: row.attachmentInfoId,
      businessId: props.params.businessId,
      sysId: sysId.value
    })
    .then(res => {
      if (isRefresh) {
        FuniFileCurdRef.value.reload();
      }
      return true;
    })
    .catch(() => {
      return false;
    });
};

//提供外部调用, 验证必传件是否都有数据
const verification = () => {
  let judge = true;
  const dataList = FuniFileCurdRef.value.tableData;
  dataList &&
    dataList.forEach(item => {
      if (item.isNeed && item.attachmentInfos.length < 1) {
        judge = false;
      }
    });
  return judge;
};

const submit = async () => {
  let flag = true;
  if (props.isVerification) {
    flag = verification();
  }
  if (flag) {
    await props.callbackFun();
  } else {
    ElNotification({
      title: '请上传必传件',
      type: 'warning'
    });
  }
  return Promise.resolve({});
};
defineExpose({
  submit,
  verification
});
</script>

<style lang="scss" scoped>
.headBtn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding-right: 8px;
}

#FileCurd {
  :deep(.funi-curd) {
    .el-table__expanded-cell {
      padding: 0px;
    }
  }
}

#fileCurdView {
  :deep(.funi-curd__header) {
    margin-bottom: 0;
  }

  :deep(.funi-curd) {
    padding: 0 !important;
    // padding-left: 50px !important;
  }
}
</style>

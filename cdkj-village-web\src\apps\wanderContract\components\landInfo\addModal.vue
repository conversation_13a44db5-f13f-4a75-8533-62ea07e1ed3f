<template>
  <div>
    <funiDialog :destroy-on-close="true" v-model="modalConfig.show" v-bind="modalConfig" :onConfirm="onConfirm">
      <funiForm v-bind="formConfig" ref="refForm" :col="2" />
      <template #footer>
        <span class="dialog-footer" v-if="!isDetail">
          <el-button @click="modalConfig.show = false">取消</el-button>
          <el-button type="primary" :disabled="loading" @click="onConfirm">确定</el-button>
        </span>
        <span v-else>
          <el-button @click="modalConfig.show = false">关闭</el-button>
        </span>
      </template>
    </funiDialog>
  </div>
</template>

<script setup lang="jsx">
import { reactive, ref, nextTick, unref, onMounted, computed, watch } from 'vue';
const emit = defineEmits(['updated', 'addCallBack']);
const props = defineProps({
  isDetail: {}
});

const loading = ref(false);
const refForm = ref(null);
const modalConfig = reactive({
  show: false,
  title: ''
});
let isAdd = true; // 判断是新增还是编辑

/**
 * 自定义下拉
 */
const enumeration = reactive({
  GENDER: [], // 性别
  CARD_TYPE: [], // 证件类型
  RELATION: [], // 与户主关系
  MEM_REMARK: [] // 人员备注
});

const dicEnum = {
  isBoolean: [
    {
      label: '是',
      value: '是'
    },
    {
      label: '否',
      value: '否'
    }
  ],
  landType: [
    {
      label: '宅基地',
      value: '宅基地'
    },
    {
      label: '耕地',
      value: '耕地'
    },
    {
      label: '水域',
      value: '水域'
    },
    {
      label: '其他',
      value: '其他'
    }
  ],
  farming: [
    {
      label: '在种',
      value: '在种'
    },
    {
      label: '撂荒',
      value: '撂荒'
    }
  ]
};

onMounted(() => {});

const formConfig = computed(() => {
  let arr = [
    {
      prop: 'landNo',
      label: '地块代码',
      component: 'el-input',
      props: { placeholder: '请输入' }
    },
    {
      prop: 'area',
      label: '地块面积(亩)',
      component: 'funi-input-number'
    },
    {
      prop: 'landType',
      label: '土地类型',
      component: 'FuniSelect',
      props: {
        options: dicEnum.landType
      }
    },
    {
      prop: 'farming',
      label: '种植情况',
      component: 'FuniSelect',
      props: {
        options: dicEnum.farming
      }
    },
    {
      prop: 'contract',
      label: '是否承包',
      component: 'FuniSelect',
      props: {
        options: dicEnum.isBoolean
      }
    },
    {
      prop: 'transfer',
      label: '是否流转',
      component: 'FuniSelect',
      props: {
        options: dicEnum.isBoolean
      }
    },
    {
      prop: 'remark',
      label: '其它备注',
      component: 'el-input',
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '请输入' }
    }
  ];
  if (props.isDetail) {
    arr = arr.map(x => ({
      ...x,
      component: null
    }));
  }
  let detail = [];

  return {
    schema: arr,
    rules: props.isDetail
      ? []
      : {
        landNo: [{ required: true, message: '请输入'}],
        area: [{ required: true, message: '请输入' }],
        landType: [{ required: true, message: '请选择' }],
        farming: [{ required: true, message: '请选择' }],
        contract: [{ required: true, message: '请选择' }],
        transfer: [{ required: true, message: '请选择' }],
        }
  };
});

const show = async item => {
  if (item) {
    isAdd = false;
    modalConfig.title = '编辑地块信息';
  } else {
    isAdd = true;
    modalConfig.title = '新增地块信息';
    item = {};
  }
  if (props.isDetail) {
    modalConfig.title = '查看地块信息';
  }
  modalConfig.show = true;
  nextTick(() => {
    unref(refForm).resetFields();
    unref(refForm).setValues(item);
  });
};
const onConfirm = async () => {
  let res = await refForm.value.validateField();
  if (res.isValid) {
    emit('addCallBack', {
      isAdd,
      data: res.values,
      next: () => {
        modalConfig.show = false;
      }
    });
  }
};
defineExpose({
  show
});
</script>

<style lang="scss" scoped></style>

<template>
  <div>
    <funiGroupTitle title="项目归属"></funiGroupTitle>
    <funiForm v-bind="projectFormConfig" ref="refFormProject" :col="1" />
    <landInfo ref="landInfoRef" :isDetail="isDetail" :landData="landDataTable" @dataListCallBack="dataListCallBack" />
    <funiGroupTitle title="出租方信息"></funiGroupTitle>
    <funiForm v-bind="upperFormConfig" ref="refFormUpper" :col="2" />
    <funiGroupTitle title="承租方信息"></funiGroupTitle>
    <funiForm v-bind="underFormConfig" ref="refFormUnder" :col="2" />
    <ownerInfoModal ref="ownerInfoRef" :isDetail="isDetail" @exportObject="setCollection" />
  </div>
</template>

<script setup lang="jsx">
import { reactive, ref, watch } from "vue";
import { contractManage, contractInfo } from "@/apps/api/contract.js";
import { useRoute } from "vue-router";
import landInfo from "./landInfo/index.vue";
import person from "./person/index.vue";
import ownerInfoModal from "@/apps/baseInfo/homestead/components/ownerInfo/addModal.vue";
import { ElNotification } from "element-plus";

const props = defineProps({
  isDetail: {},
  data: {},
});
// const { id } = useRoute().query;
const landDataTable = ref([]);
const ownerInfoRef = ref();
const personRef = ref();

watch(
  () => props.data,
  () => {
    refFormProject.value.setValues(props.data);
    refFormUpper.value.setValues(props.data.upperList[0]);
    refFormUnder.value.setValues(props.data.underList[0]);
    landDataTable.value = props.data.landList;
    householdId.value = props.data.householdId;
  }
);

// if (id) {
//   contractInfo({ uuid: id }).then(res => {
//     refFormUpper.value.setValues(res.upperList[0]);
//     refFormUnder.value.setValues(res.underList[0]);
//     landDataTable.value = res.landList
//     householdId.value = res.householdId
//   });
// }

const dicEnum = {
  cardType: [
    {
      label: "居民身份证",
      value: "居民身份证",
    },
    {
      label: "军官证",
      value: "军官证",
    },
    {
      label: "行政、企事业单位机构代码证或法人代码证",
      value: "行政、企事业单位机构代码证或法人代码证",
    },
    {
      label: "户口簿",
      value: "户口簿",
    },
    {
      label: "护照",
      value: "护照",
    },
    {
      label: "其他证件",
      value: "其他证件",
    },
  ],
};

const landInfoRef = ref();
const refFormProject = ref();
const refFormUpper = ref();
const refFormUnder = ref();
const householdId = ref();

const projectFormConfig = reactive({
  schema: [
    {
      prop: "project",
      label: "项目归属",
      component: !props.isDetail ? "el-input" : null,
      props: { placeholder: "请输入" },
    },
  ],
});
/**
 * 发包方
 */
const upperFormConfig = reactive({
  schema: [
    {
      prop: "upperName",
      label: "出租方",
      component: !props.isDetail ? "el-input" : null,
      props: { placeholder: "请输入" },
    },
    {
      prop: "upperIdNo",
      label: "身份证号",
      component: !props.isDetail ? "el-input" : null,
      props: { placeholder: "请输入" },
    },
    {
      prop: "upperLocation",
      label: "联系地址",
      component: !props.isDetail ? "el-input" : null,
      props: { placeholder: "请输入" },
    },
    {
      prop: "upperPhone",
      label: "联系电话",
      component: !props.isDetail ? "el-input" : null,
      props: { placeholder: "请输入" },
    },
  ],
  rules: !props.isDetail
    ? {
        upperNo: [{ required: true, message: "请输入" }],
        upper: [{ required: true, message: "请输入" }],
        upperName: [{ required: true, message: "请输入" }],
        upperPhone: [{ required: true, message: "请输入" }],
        upperIdType: [{ required: true, message: "请输入" }],
        upperIdNo: [{ required: true, message: "请输入" }],
      }
    : undefined,
});

/**
 * 承包方
 */
const underFormConfig = reactive({
  schema: [
    {
      prop: "upper",
      label: "承租方",
      component: !props.isDetail ? "el-input" : null,
      props: { placeholder: "请输入" },
    },
    {
      prop: "underNo",
      label: "社会信用代码",
      component: !props.isDetail ? "el-input" : null,
      props: { placeholder: "请输入" },
    },
    {
      prop: "underName",
      label: "法定代表人",
    },
    {
      prop: "underId",
      label: "身份证号码",
    },
    {
      prop: "underLocation",
      label: "联系地址",
    },
    {
      prop: "underPhone",
      label: "联系电话",
    },
  ],
  rules: !props.isDetail
    ? {
        underNo: [{ required: true, message: "请输入" }],
        underName: [{ required: true, message: "请输入" }],
        usage: [{ required: true, message: "请输入" }],
        contractCretNo: [{ required: true, message: "请输入" }],
        rightArea: [{ required: true, message: "请输入" }],
      }
    : undefined,
});

const setCollection = (e) => {
  let row = e.find((x) => x.householder == 1);
  householdId.value = row.householdId;
  refFormUnder.value.setValues({
    underName: row.name,
    underIdType: row.idType,
    underIdNo: row.idCode,
    underLocation: row.location,
    underPhone: row.phone,
    contractType: "家庭承包",
  });
};

/**
 * 提交
 */
async function submit() {
  let landData = await landInfoRef.value.getData();
  if (!landData.length) {
    ElNotification({
      title: "请添加地块信息",
      type: "warning",
    });
    return Promise.reject();
  }
  let resUpper = await refFormUpper.value.validate();
  let resUnder = await refFormUnder.value.validate();
  let arr = personRef.value.getData();
  if (resUpper.isValid && resUnder.isValid) {
    resUnder.values.underMembersList = arr;
    //编辑
    if (id) {
      await contractManage({
        uuid: id,
        type: "土地流转",
        householdId: householdId.value,
        contractNo: resUnder.values.contractCretNo,
        landList: landData,
        upperList: [resUpper.values],
        underList: [resUnder.values],
      });
    }
    //新增
    else {
      await contractManage({
        type: "土地流转",
        householdId: householdId.value,
        landList: landData,
        contractNo: resUnder.values.contractCretNo,
        upperList: [resUpper.values],
        underList: [resUnder.values],
      });
    }
    return Promise.resolve();
  }
  return Promise.reject();
}

defineExpose({
  submit,
});
</script>

<style lang="scss" scoped></style>

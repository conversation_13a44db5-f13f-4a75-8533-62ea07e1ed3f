package cn.fight.village.config;

import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.task.CheckLicenceTask;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * IOC容器加载完成后操作
 */
@Component
public class AfterIOCInitConfig implements ApplicationContextAware {

        @Override
        public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
            //许可证校验
            Boolean aBoolean = SecureUtils.checkLicence();
            if (aBoolean) {
                CheckLicenceTask.setLicenceAvailable(Boolean.TRUE);
            } else {
                CheckLicenceTask.setLicenceAvailable(Boolean.FALSE);
            }

        }
}

package cn.fight.village.domain.contract.value;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseValue;

import java.math.BigDecimal;

/**
 * 合同列表视图对象
 *
 */
public class ContractListVo extends BaseValue {
    //编号
    private String contractNo;

    //承包经营权证书号
    private String  contractCretNo;

    //缺钱面积
    private Double  rightArea;

    //用途
    private String  usage;

    //发包方名称
    private String upper;

    private String upperName;

    //签约状态
    private String signed;

    //合同类型
    private String  contractType;

    //承包方负责人
    private String  underName;

    //承包方地址
    @Sensitive
    private String  underLocation;

    //驱动方式
    private String rightOrg;

    private BigDecimal price;

    //归属项目
    private String project;

    public String getSigned() {
        return signed;
    }

    public void setSigned(String signed) {
        this.signed = signed;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getUpperName() {
        return upperName;
    }

    public void setUpperName(String upperName) {
        this.upperName = upperName;
    }

    public String getRightOrg() {
        return rightOrg;
    }

    public void setRightOrg(String rightOrg) {
        this.rightOrg = rightOrg;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getContractCretNo() {
        return contractCretNo;
    }

    public void setContractCretNo(String contractCretNo) {
        this.contractCretNo = contractCretNo;
    }

    public Double getRightArea() {
        return rightArea;
    }

    public void setRightArea(Double rightArea) {
        this.rightArea = rightArea;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getUpper() {
        return upper;
    }

    public void setUpper(String upper) {
        this.upper = upper;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getUnderName() {
        return underName;
    }

    public void setUnderName(String underName) {
        this.underName = underName;
    }

    public String getUnderLocation() {
        return underLocation;
    }

    public void setUnderLocation(String underLocation) {
        this.underLocation = underLocation;
    }
}

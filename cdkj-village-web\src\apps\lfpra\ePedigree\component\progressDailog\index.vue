<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="small" :show-close="false" :hideFooter="true" title="上传文件夹">
        <el-progress :percentage="percentage" />
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref, onMounted } from 'vue';
import { ElNotification } from 'element-plus';
import { getPercentageHttp } from "../../hooks/api.js";
const props = defineProps({
  callbackFun: {
    type: Function,
    default: () => {
    }
  }
})

const dialogVisible = ref(false); // 控制模态框显示隐藏

const  timer =  ref(null)
const percentage = ref(0)
const show = async () => {
dialogVisible.value = true;
percentage.value = 0
if(timer.value){
  clearInterval(timer.value)
}
setIntervalFunc()
await nextTick();
}
const setIntervalFunc= ()=>{
  timer.value = setInterval(()=>{
    getPercentageHttp({uuid:sessionStorage.getItem('c_id'),isClose:false}).then(res=>{
      // if(res == '100'){
      //   percentage.value = res
      //   getPercentageHttp({uuid:sessionStorage.getItem('c_id'),isClose:true})
      //   clearInterval(timer.value)
      //   dialogVisible.value = false;
      //   props.callbackFun()
      // }else{
        percentage.value = res
      // }
    })
  },500)
}
const hide = async () => {
dialogVisible.value = false;
 getPercentageHttp({uuid:sessionStorage.getItem('c_id'),isClose:true})
if(timer.value){
  clearInterval(timer.value)
}
}

defineExpose({
  show,
  hide
});
</script>
<style scoped></style>

<template>
  <div>
    <funi-detail
      :bizName="bizName"
      :showHead="true"
      :steps="steps"
      :detailHeadOption="detailHeadOption"
    />
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import baseForm from './components/baseForm.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
// 头部数据
const detailHeadOption = reactive({
  title: route.query.title,
  hideStatusBar: true,
});
const steps = [
  {
    title: '宅基地信息',
    preservable: false,
    type: baseForm,
    props: {},
  }
];

</script>

<style lang="scss" scoped></style>

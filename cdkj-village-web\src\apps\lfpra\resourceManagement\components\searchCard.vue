<template>
  <div>
    <el-space wrap>
      <el-select v-model="form.zh" class="m-2" placeholder="请选择组号">
        <el-option v-for="item in options" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
      <el-input v-model="form.name" placeholder="请输入集体组织成员姓名搜索" style="width: 300px" />
      <el-popover placement="bottom" :width="200" trigger="click">
        <template #reference>
          <el-button style="width: 140px">图层管理</el-button>
        </template>
        <el-checkbox-group v-model="patternTypes">
          <el-checkbox label="3">
            <template #default>
              <div class="homestead">宅基地图层</div>
            </template>
          </el-checkbox>
          <el-checkbox label="1">
            <template #default>
              <div class="contract">承包地图层</div>
            </template>
          </el-checkbox>
          <el-checkbox label="2">
            <template #default>
              <div class="forest">坑塘水面图层</div>
            </template>
          </el-checkbox>
          <el-checkbox label="4">
            <template #default>
              <div class="land">国土空间变更</div>
            </template>
          </el-checkbox>
          <el-checkbox label="5">
            <template #default>
              <div class="farmland">永久基本农田</div>
            </template>
          </el-checkbox>
          <el-checkbox label="6">
            <template #default>
              <div class="land_status">土地现状</div>
            </template>
          </el-checkbox>
        </el-checkbox-group>
      </el-popover>
      <el-button type="primary" :disabled="!isDisabled" @click="handleSearch">搜索</el-button>
      <el-button  @click="handleSearch('reset')">重置</el-button>
    </el-space>
  </div>
</template>
<script setup lang="jsx">
import { ref, reactive, watch,computed,onMounted } from 'vue';
import { queryGroupListByComCodeApi } from '../api/index';
import { debounce } from 'lodash-es';
let props = defineProps({
  searchCallBack: {
    type: Function,
    default: () => {}
  },
  searchNameCallBack: {
    type: Function,
    default: () => {}
  }
});
const patternTypes = ref(['3', '1', '2','4','5','6']);
const form = reactive({
  zh: '',
  name: ''
});
// 勾选 宅基地图层 承包地图层 坑塘水面图层 可搜索
const isDisabled = computed(()=>{
  const arr = ['3', '1', '2']
  // console.log(patternTypes.value,'patternTypes.value---');
  let isDisabled = arr.some(item=>patternTypes.value.includes(item))
  return isDisabled
})
const options = ref([]);
// 获取组号
onMounted(async () => {
  let data = await queryGroupListByComCodeApi({ code: 510112005202 });
  options.value = data;
});
// const searchClick = debounce(() => {
//   props.searchNameCallBack(form, patternTypes.value);
// }, 2000);
// 搜索/重置事件
const handleSearch = (e) => {
  if(e =='reset'){
    form.zh=''
    form.name = ''
    patternTypes.value= ['3', '1', '2','4','5','6']
  }
  props.searchCallBack(patternTypes.value);
  props.searchNameCallBack(form, patternTypes.value,e);
};
watch(
  // () => patternTypes,
  // (newVal, oldVal) => {
  //   props.searchCallBack(patternTypes.value);
  //   props.searchNameCallBack(form, patternTypes.value);
  // },
  // { deep: true }
);

// watch(
//   () => form,
//   (newVal, oldVal) => {
//     searchClick();
//   },
//   { deep: true }
// );
</script>
<style lang="scss" scoped>
.homestead {
  margin-left: 20px;
  &::before {
    content: ' ';
    display: block;
    width: 18px;
    height: 18px;
    background-color: #b3b3ff;
    position: absolute;
    top: 6px;
    left: 20px;
  }
}
.contract {
  margin-left: 20px;
  &::before {
    content: ' ';
    display: block;
    width: 18px;
    height: 18px;
    background-color: #348D57;
    position: absolute;
    top: 6px;
    left: 20px;
  }
}
.forest {
  margin-left: 20px;
  &::before {
    content: ' ';
    display: block;
    width: 18px;
    height: 18px;
    background-color: #b4e5fb;
    position: absolute;
    top: 6px;
    left: 20px;
  }
}
.land {
  margin-left: 20px;
  &::before {
    content: ' ';
    display: block;
    width: 18px;
    height: 18px;
    background-color: #FACD91;
    position: absolute;
    top: 6px;
    left: 20px;
  }
}
.farmland {
  margin-left: 20px;
  &::before {
    content: ' ';
    display: block;
    width: 18px;
    height: 18px;
    background-color: #95F204;
    position: absolute;
    top: 6px;
    left: 20px;
  }
}
.land_status {
  margin-left: 20px;
  &::before {
    content: ' ';
    display: block;
    width: 18px;
    height: 18px;
    background-color: #409EFF;
    position: absolute;
    top: 6px;
    left: 20px;
  }
}
::v-deep() {
  .el-checkbox__label {
    color: #606266 !important;
  }
}
</style>

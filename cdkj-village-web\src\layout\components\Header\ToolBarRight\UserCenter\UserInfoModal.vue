<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-01-16 21:31:19
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-03-07 20:02:54
 * @FilePath: /funi-paas-cscas-ui/src/apps/csuc/FuniUserMenu/UserInfoModal.vue
 * @Description:
 * Copyright (c) 2023 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <div>
    <el-dialog
      v-model="dialogFormVisible"
      destroy-on-close
      align-center
      title="个人信息"
      width="450px"
    >
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="form"
        label-width="auto"
      >
        <el-form-item label="用户类型">
          {{ form.userType || '--' }}
        </el-form-item>
        <el-form-item label="用户名">
          {{ form.userName || '--' }}
        </el-form-item>
        <el-form-item v-if="editInfo" prop="nickName" label="昵称">
          <el-input v-model="form.nickName" />
        </el-form-item>
        <el-form-item v-else label="昵称">
          {{ form.nickName || '--' }}
        </el-form-item>
        <el-form-item v-if="editInfo" prop="cellPhone" label="电话">
          <el-input v-model="form.cellPhone" />
        </el-form-item>
        <el-form-item v-else label="电话">
          {{ form.cellPhone || '--' }}
        </el-form-item>
        <el-form-item v-if="editInfo" prop="emailAddress" label="邮箱">
          <el-input v-model="form.emailAddress" />
        </el-form-item>
        <el-form-item v-else label="邮箱">
          {{ form.emailAddress || '--' }}
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';

const props = defineProps({
  user: { type: Object, default: () => ({}) }
});

//个人信息弹窗显示
const dialogFormVisible = ref(false);
//密码弹窗显示
const pwdVisible = ref(false);
//是否编辑个人信息
const editInfo = ref(false);
//个人信息表单
const formRef = ref();
const form = reactive({
  userType: '',
  userName: '',
  nickName: '',
  cellPhone: '',
  emailAddress: ''
});
const formRules = reactive({
  nickName: [{ required: true, message: '请输入昵称', trigger: 'change' }],
  cellPhone: [
    {
      required: true,
      validator: $utils.Validate.isiPhoneTwo,
      trigger: 'change'
    }
  ]
});

//打开个人信息弹窗
const userInfo = reactive({});
const show = async () => {
  dialogFormVisible.value = true;
  editInfo.value = false;
  Object.assign(form, props.user);
  Object.assign(userInfo, props.user);
};

defineExpose({
  show
});
</script>

<style lang="less">
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

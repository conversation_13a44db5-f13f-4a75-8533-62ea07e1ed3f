<template>
  <div>
    <!-- 地块信息 -->
    <massifInfo
      :tableData="tableData"
      :isEdit="props.isEdit"
      :otherPatternIds="patternIds"
      @exMassifData="exMassifData"
      menuName="pitAndPond"
      layerType="cyheal:fragment_ktsm"
    />
    <div v-for="(item, index) in schemaList" :key="index">
      <GroupTitle :title="item.title" />
      <!-- 表单 -->
      <funi-form
        :schema="item.list"
        @get-form="e => setForm(e, index)"
        :rules="props.isEdit ? item.rules : {}"
        :border="false"
        :col="2"
      />
    </div>
    <!-- 选择户主名 -->
    <ChooseCollection ref="chooseModal" @exportObject="setCollection" />
    <!-- 提交成功dailog 跳转列表页 -->
    <SubmitSuccess ref="su" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, inject, nextTick, watch, onMounted, unref, reactive } from 'vue';
import { useSchema } from '../hooks/baseDetails.jsx';
import { ElNotification } from 'element-plus';
import { useRouter } from 'vue-router';
import GroupTitle from '@/apps/lfpra/common/components/groupTitle/index.vue';
import ChooseCollection from '@/apps/lfpra/common/components/ownerInfo/chooseCollection.vue';
import massifInfo from '@/apps/lfpra/common/components/massifInfo/index.vue';
import SubmitSuccess from '@/apps/lfpra/common/components/submit_success/index.vue';
import {
  dictListHttp,
  queryOwnerShipInfoListtHttp,
  getUnderEmployerInfoHttp,
  getUnderContractorInfoHttp
} from '@/apps/lfpra/common/hooks/api';
import { infoHttp, newHttp, getOtherUnderinfoPatternIdsHttp } from '../hooks/api';
const form = ref();
const isContract = ref('');
// 地块信息数据
const tableData = ref([]);
// 接收数据
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true
  },
  id: {
    type: String,
    default: ''
  }
});
const router = useRouter();
//选择户主名弹窗
const chooseModal = ref(null);
const submitObj = reactive({});
const su = ref();
const patternIds = ref([]); //其他已经选择的图斑id集合
const isVillager = ref(true); //是否为村民
const addressObj = reactive({
  //默认的省市区
  province: { label: '四川省', value: '510000' },
  city: { label: '成都市', value: '510100' },
  district: { label: '龙泉驿区', value: '510112' },
  community: {},
  street: {},
  other: '',
  addressFull:''
});
//下来选择框
const selectList = reactive({
  obtainType: [
    { label: '是', value: true },
    { label: '否', value: false }
  ], //是否取证
  cardType: [{}], //证件类型
  // ownerType: [{}], //承包方（代表）名称
  manageType: [{}] //承包经营权取得方式
});
const ownerChangeList = ref(); //选择承包方名称携带的数据
const familyData = reactive({
  familyId: '', //显示共有人
  familySn: '',
  groupNumber: '',
  ownerId: ''
});
//选择户主
const ownerNameChange = () => {
  if (!isVillager.value) return;
  let list = [{ familySn: familyData.familySn }];
  chooseModal.value.show(list, 'familySn');
};
//承包方（代表）名称选择框
const setCollection = e => {
  ownerChangeList.value = e[0];
  for (let key in familyData) {
    familyData[key] = ownerChangeList.value[key];
  }
  let arrStr = ['contractorName', 'dicCardTypeName', 'cerCertificateNo', 'address', 'cerTelephone'];
  let formData = {};
  arrStr.forEach(item => {
    if (item == 'contractorName') {
      formData[item] = ownerChangeList.value.memberName;
    } else {
      formData[item] = ownerChangeList.value[item];
    }
  });
  submitObj.form2.setValues(formData);
};
//发包方代码值失去焦点 获取发包方信息
const dicCardChange = e => {
  getUnderEmployerInfoHttp({ employerCode: e }).then(res => {
    if (res.id) {
      submitObj.form1.setValues(res);
    } else {
      submitObj.form1.setValues({ id: '' });
    }
  });
};
//承包方代码值改变 获取承包方信息
const contractorChange = e => {
  getUnderContractorInfo(e);
};
//是否为村民
const isVillagerChange = e => {
  isVillager.value = e;
  let resSetForm = {
    contractorName: '',
    cerCertificateNo: '',
    cerTelephone: ''
  };
  for (let key in familyData) {
    familyData[key] = '';
  }
  if (!e) {
    addressObj.community = {};
    addressObj.street = {};
    addressObj.other = '';
    addressObj.addressFull = '';
    submitObj.form2.setValues({ dicCardTypeCode: '', address: addressObj, ...resSetForm });
  } else {
    submitObj.form2.setValues({ dicCardTypeName: '', address: '', ...resSetForm });
  }

  // const contractorCode = submitObj.form2.getValues().contractorCode;
  // getUnderContractorInfo(contractorCode || '');
};
// 获取承包方信息
const getUnderContractorInfo = e => {
  getUnderContractorInfoHttp({ contractorCode: e }).then(res => {
    if (res.id) {
      for (let key in familyData) {
        familyData[key] = res[key];
      }
      isVillager.value = res.isVillager;
      if (!res.isVillager) {
        //为否 村民
        getAddressData(res);
        submitObj.form2.setValues(Object.assign({}, res, { address: addressObj }));
      } else {
        submitObj.form2.setValues(Object.assign({}, res));
      }
    } else {
      submitObj.form2.setValues({ id: '' });
    }
  });
};
// 获取地址 处理数据
const getAddressData = e => {
  addressObj.community = { value: e.dicStreetCode, label: e.dicStreetName };
  addressObj.street = { value: e.dicCommunityCode, label: e.dicCommunityName };
  addressObj.other = e.groupNumber;
  addressObj.addressFull = e.detailedAddress;
};
//地块信息 数据
const exMassifData = e => {
  tableData.value = e;
  console.log(tableData.value, '地块信息----');
};
// 配置表单
const schemaList = computed(() => {
  return useSchema({
    isEdit: props.isEdit,
    ...selectList,
    ownerNameChange,
    dicCardChange,
    contractorChange,
    isVillagerChange,
    isVillager: isVillager.value
  });
});
// e：form绑定的值  i:当前表单index
const setForm = (e, i) => {
  submitObj[`form${i + 1}`] = e;
};
onMounted(() => {
  init();
});

// 初始化数据
const init = () => {
  getDictLisType('CARD_TYPE', 'cardType');
  getDictLisType('CER_MANAGE', 'manageType');
  // getOwnerList('ownerType');
  getOtherPatternIds();
  // 未携带id 新增进入
  if (!props.id) {
    submitObj.form2.setValues({ isVillager: true });
    return;
  }
  // 携带id进入 编辑，详情
  getDetails();
};
// 字典查询
const getDictLisType = async (e, i) => {
  let { list } = await dictListHttp({ dictiEnum: e });
  selectList[i] = list;
};

//查询其他地块id
const getOtherPatternIds = async () => {
  let data = await getOtherUnderinfoPatternIdsHttp({ contractId: props.id, cerType: 2 });
  patternIds.value = data;
};
// 获取编辑/详情回显的数据
const getDetails = () => {
  infoHttp({ contractId: props.id }).then(res => {
    tableData.value = res.underInfoVos; //地块信息
    for (let key in familyData) {
      familyData[key] = res.contractorVo[key];
    }
    let otherVoStrArr = ['dicCerManageCode', 'dicCerManageName', 'contractingUse', 'shareOutBonus', 'remark']; //需要单独处理的值
    if (props.isEdit) {
      // 是否为村民 赋值用作表单显示判断
      isVillager.value = res.contractorVo.isVillager;
      if (!res.contractorVo.isVillager) {
        getAddressData(res.contractorVo);
      }
      //编辑时 承包经营权取得方式 下拉框回显
      otherVoStrArr = ['dicCerManageCode', 'contractingUse', 'shareOutBonus', 'remark'];
    }
    let otherVoArr = {};
    otherVoStrArr.forEach(item => {
      otherVoArr[item] = res[item];
    });
    nextTick(() => {
      submitObj.form1.setValues(res.employerVo);
      if (!res.contractorVo.isVillager && props.isEdit) {
        submitObj.form2.setValues(Object.assign({}, res.contractorVo, otherVoArr, { address: addressObj }));
      } else {
        submitObj.form2.setValues(Object.assign({}, res.contractorVo, otherVoArr));
      }
    });
  });
};
// 判断是否添加地块信息
const verificationTable = () => {
  if (tableData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '请添加地块信息',
      type: 'warning'
    });
    return false;
  }
  return true;
};
//处理数据
const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await formValidate('validate');
  if (type == 'ts' || (isValid && verificationTable())) {
    const { dicCerManageCode, contractingUse, shareOutBonus, remark, address, isVillager } =
      submitObj.form2.getValues();
    const otherVoArr = {
      dicCerManageCode,
      contractingUse,
      shareOutBonus,
      remark
    };
    // 处理地址提交数据
    const leveList = reactive({
      dicProvinceCode: '510000',
      dicCityCode: '510100',
      dicDistrictCode: '510112',
      dicCommunityCode: '',
      dicStreetCode: '',
      groupNumber: '',
      detailedAddress: ''
    });
    // 处理地址提交数据
    if (!isVillager) {
      leveList.dicCommunityCode = address.street.value;
      leveList.dicStreetCode = address.community.value;
      leveList.groupNumber = address.other;
      leveList.detailedAddress = address.addressFull;
    }

    let obj = {
      id: props.id || '',
      contractorRequest: Object.assign({}, submitObj.form2.getValues(), familyData, isVillager ? {} : { ...leveList }),
      ...otherVoArr,
      employerRequest: submitObj.form1.getValues(),
      underInfos: tableData.value,
      cerType: 2
    };
    return obj;
  } else {
    return false;
  }
};
// 保存数据 提交接口
const saveDate = async type => {
  let data = await getData(type);
  if (!data) return;
  await newHttp(data).then(res => {
    if (res.dataId) {
      su.value.show();
    }
  });
  await nextTick();
  if (!props.id) return Promise.reject();

  return Promise.resolve({});
};
// 表单的统一验证
const formValidate = async e => {
  let isValid = {};
  if (e) {
    await Promise.all(Object.values(submitObj).map(item => item.validate()))
      .then(res => {
        if (res.every(item => item.isValid)) {
          isValid = { isValid: true };
        } else {
          isValid = { isValid: false };
        }
      })
      .catch(err => {
        isValid = { isValid: false };
      });
    return isValid;
  } else {
    //多个表单getValues() 值
    let valuesList = Object.assign({}, submitObj.form1.getValues(), submitObj.form2.getValues());
    return valuesList;
  }
};

//提交按钮
const submit = () => {
  return saveDate();
};
defineExpose({
  submit
  // ts
});
</script>

#日志级别
logging:
  level:
    root: error

spring:
  #数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **********************************************
    username: postgres
    password: fight@postgres@2025
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000

#系统业务配置
business:
  file-store-path: /usr/local/file_store/
  env: prod
  licence: YMxZQnfSEB9BInDDfuu555CiHhMhBQ7UCitUbV560kWdjVG9CzUEaJy2QzGJapnLFY757ABWmqntzLfB7BDazwtJ4hBnAaAbLVoG+9w7n1a5+9he6NrvgJFSbZqRCN1fJ1TtcBpqy7sPofyFoah/v5Ftw/C8mQaQzbbCEwlvlbw=
  documents_path: /usr/local/tomcat9/webapps/documents
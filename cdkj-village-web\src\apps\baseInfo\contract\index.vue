<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="cardTab" @headBtnClick="headBtnClick" />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { contractList, contractRemove } from '@/apps/api/contract.js';

const router = useRouter();
const listPage = ref();
/**
 * 列表配置
 */
const cardTab = reactive([
  {
    curdOption: {
      reloadOnActive: true,
      lodaData: (pages, parmas) => {
        return contractList({ ...pages, ...parmas, type: '家庭承包' });
      },
      searchConfig: {
        schema: [
          {
            prop: 'contractNo',
            label: '承包合同号',
            component: 'el-input'
          },
          {
            prop: 'upper',
            label: '发包方名称',
            component: 'el-input'
          },
          {
            prop: 'upperName',
            label: '发包方负责人',
            component: 'el-input'
          },
          {
            prop: 'underName',
            label: '承包方代表名',
            component: 'el-input'
          },
          {
            prop: 'contractType',
            label: '承包类型',
            component: 'el-input'
          },
          {
            prop: 'rightOrg',
            label: '经营权取得方式',
            component: 'el-input'
          }
        ]
      },
      btns: [{ key: 'add', label: '新增' }],
      columns: [
        {
          label: '承包经营权证书号',
          prop: 'contractCretNo',
          render: ({ row, index }) => {
            return (
              <el-button type="primary" link onClick={() => detail(row)}>
                {row.contractCretNo}
              </el-button>
            );
          }
        },
        {
          label: '地确权（合同）总面积（亩）',
          prop: 'rightArea'
        },
        { label: '土地承包用途', prop: 'usage' },
        { label: '发包方名称', prop: 'upper' },
        { label: '发包方负责人姓名', prop: 'upperName' },
        { label: '承包方式', prop: 'contractType' },
        { label: '承包方（代表）名称', prop: 'underName' },
        {
          label: '承包方地址',
          prop: 'underLocation'
        },
        {
          label: '操作',
          prop: 'opt',
          align: 'center',
          fixed: 'right',
          render: ({ row, index }) => {
            return (
              <div>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    edit(row);
                  }}
                >
                  编辑
                </el-button>
                <el-popconfirm
                  title="确认删除该条数据吗？"
                  onConfirm={() => {
                    del(row);
                  }}
                  v-slots={{
                    reference: () => (
                      <el-button type="primary" link>
                        删除
                      </el-button>
                    )
                  }}
                ></el-popconfirm>
              </div>
            );
          }
        }
      ]
    }
  }
]);

/**
 * 编辑
 * @param row 数据行
 */
function edit(row) {
  router.push({
    name: 'ContractAdd',
    query: {
      id: row.uuid,
      title: '家庭承包信息编辑',
      bizName: '编辑',
      tab: `家庭承包信息-${row.contractCretNo}-编辑`
    }
  });
}
/**
 * 删除
 * @param row 数据行
 */
function del(row) {
  contractRemove({ uuid: row.uuid }).then(res => {
    listPage.value.reload();
  });
}
/**
 * 详情
 * @param row 数据行
 */
function detail(row) {
  router.push({
    name: 'ContractDetail',
    query: {
      id: row.uuid,
      contractNo:row.contractCretNo,
      title: '家庭承包信息详情',
      bizName: '详情',
      tab: `家庭承包信息-${row.contractCretNo}-详情`
    }
  });
}
/**
 * 按钮点击事件
 */
function headBtnClick(res) {
  switch (res) {
    case 'add':
      router.push({
        name: 'ContractAdd',
        query: {
          title: '家庭承包信息新增',
          bizName: '新建',
          tab: '家庭承包信息-新增'
        }
      });
      break;
    default:
      break;
  }
}
</script>

<style lang="scss" scoped></style>

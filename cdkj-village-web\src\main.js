import { createApp } from 'vue';
import { createPinia } from 'pinia';

import App from '@/App.vue';
import router from '@/router/index';
import '@/styles/index.scss';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import piniaPersist from 'pinia-plugin-persist';
import "mars3d-cesium/Build/Cesium/Widgets/widgets.css"
import "mars3d/mars3d.css"

import utils from '@/utils';
// import directive from '@/utils/directive';
import components from '@/components';


const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

const pinia = createPinia();
pinia.use(piniaPersist);
app
  .use(utils)
  .use(components)
  // .use(directive)
  .use(pinia)
  .use(router)
  .use(ElementPlus)
  .mount('#app');

package cn.fight.village.domain.common.service;

import cn.fight.village.domain.user.entity.User;

import java.util.List;

/**
 * 缓存服务层
 */
public interface CacheService {
    /**
     * 用户登录后存储入缓存
     * @param token
     * @param user
     */
    void userLoginCache(String token, User user);

    /**
     * 登录用户从缓存中获取信息
     * @param token
     * @return
     */
    User getUserLoginCache(String token);

    /**
     * 获取业务缓存
     * @param key
     * @return
     */
    Object getBusinessCache(String key);

    /**
     * 添加业务缓存
     * @param key
     * @param value
     * @return
     */
    void putBusinessCache(String key,Object value,long timeout);

    /**
     * 删除缓存
     * @param key
     */
    void removeCache(String key);
}

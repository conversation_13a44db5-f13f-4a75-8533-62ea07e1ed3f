<template>
  <el-config-provider :locale="locale">
  <router-view />
</el-config-provider>
</template>

<script setup>
import { onMounted } from 'vue';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import useThemeConfigStore from '@/layout/components/theme/hooks/setTheme.js';

const locale = zhCn;
const themeConfigStore = useThemeConfigStore();
themeConfigStore.getInitConfig();

onMounted(() => {
  document.getElementById('loading-mask').style.display = 'none';
});
</script>

<style lang="less">
.size {
  width: 100%;
  height: 100%;
}

html,
body {
  padding: 0 !important;
  margin: 0;
  overflow: hidden;
  .size;
  #app {
    .size;
  }
}

label {
  font-weight: 400;
}

@font-face {
  font-family: "luo";
  src: url("./luo.ttf") format("truetype");
}
</style>

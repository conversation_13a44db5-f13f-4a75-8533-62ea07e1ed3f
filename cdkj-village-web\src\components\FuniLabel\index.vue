<!--
 * @Author: 郑佳 <EMAIL>
 * @Date: 2023-02-08 19:31:37
 * @LastEditors: 郑佳 <EMAIL>
 * @LastEditTime: 2023-10-16 18:23:01
 * @FilePath: \funi-bpaas-as-ui\src\components\FuniLabel\index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div style="width:100%;">
    <div v-if="modelValue"
      style="width:100%;">
      <span v-if="length&&(modelValue+'').length<=length"
        :style="styleObj"
        class="label-content">{{modelValue}}</span>
      <el-tooltip v-else-if="length>0"
        :content="modelValue"
        placement="top">
        <span :style="styleObj">{{(modelValue+'').substring(0,length)}}...</span>
      </el-tooltip>
      <el-tooltip :content="modelValue"
        placement="top"
        v-else-if="tooltipVisible">
        <el-text ref="elTextRef"
          :style="styleObj"
          truncated>
          {{modelValue}}
        </el-text>
      </el-tooltip>
      <el-text v-else
        ref="elTextRef"
        :style="styleObj"
        truncated>
        {{modelValue}}
      </el-text>
    </div>
    <span v-else>--</span>
  </div>
</template>
<script setup>
import { computed, ref, watch } from "vue";

const props = defineProps({
  //默认自适应容器
  length: {
    type: [Number]
  },
  modelValue: {
    type: [String, Number],
    default: ''
  },
  styleObj: {
    type: Object,
    default: () => {
      return new Object();
    }
  }
})

const elTextRef = ref(null);

const tooltipVisible = ref(false);

watch(() => props.modelValue, () => {
  setTimeout(() => {
    if (elTextRef.value && elTextRef.value.$el) {
      if (elTextRef.value.$el.offsetWidth < elTextRef.value.$el.scrollWidth) {
        tooltipVisible.value = true;
      }
    }
  }, 100);
}, { immediate: true })

</script>
<style lang='scss' scoped>
.label-content {
  color: var(--el-text-color-regular);
}
:deep(.el-text.is-truncated) {
  display: block;
}
</style>
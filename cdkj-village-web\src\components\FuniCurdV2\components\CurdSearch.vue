<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-07-19 14:58:33
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-09-01 16:12:06
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniCurdV2/components/CurdSearch.vue
 * @Description:
 * Copyright (c) 2023 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <div style="background-color: white">
    <funi-search-form-v3
      v-if="useSearchV2 && env.useSearchV2"
      v-bind="searchConfig"
      :queryFields="queryFields"
      :colNumber="colNumber"
      @search="handleSearch"
      @reset="() => handleSearch({})"
    >
      <template v-for="(_, slot) in $slots" #[slot]="params">
        <slot :name="slot" v-bind="params || {}" />
      </template>
    </funi-search-form-v3>
    <funi-search-form
      v-else
      v-bind="searchConfig"
      @search="handleSearch"
      :colNumber="props.colNumber"
      @reset="() => handleSearch({})"
    >
      <template v-for="slotName in searchFormSlots" #[slotName]="params">
        <slot :name="slotName" v-bind="params"></slot>
      </template>
    </funi-search-form>
  </div>
</template>

<script setup>
import env from '@/utils/env';
import { computed } from 'vue';

defineOptions({
  name: 'CurdSearch'
});

const props = defineProps({
  /** 是用4.0模式高级查询 */
  useSearchV2: { type: Boolean, default: true },
  searchConfig: { type: Object, default: () => ({}) },
  colNumber: { type: Number, default: 4 },
  queryFields: { type: Array, default: () => [] }
});

const emit = defineEmits(['search']);
const searchFormSlots = computed(() => {
  if (!props.searchConfig.schema) return [];

  const slotNames = props.searchConfig.schema.map(item => Object.values(item.slots || {})).flat();
  return Array.from(new Set(slotNames));
});

const handleSearch = params => {
  emit('search', params);
};
</script>

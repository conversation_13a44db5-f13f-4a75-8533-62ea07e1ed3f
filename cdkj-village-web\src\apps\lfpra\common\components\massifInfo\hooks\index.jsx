import Hyperlink from '@/apps/lfpra/common/components/hyperlinkTable/index.vue';
import NumberInput from '@/apps/lfpra/common/components/numberInput/numberInput.vue';
import LetterNumInput from '@/apps/lfpra/common/components/letterNumInput/index.vue';
import { moreBtnRender } from '@/apps/lfpra/common/hooks/utils.jsx';

export const userTableColumns = ({ seeDateils, editFunc = () => {}, delFunc = () => {},isEdit,menuName }) => {
  return [
    {
      label:menuName == 'pitAndPond'? '坑塘代码':'地块代码',
      prop: 'landCode'
    },
    {
      label: menuName == 'pitAndPond'?'坑塘面积(亩)':'地块面积(亩)',
      prop: 'landArea'
    },
    {
      label: '土地类型',
      prop: 'dicLandName'
    },
    // 家庭承包 显示
    {
      label: '种植情况',
      prop: 'dicCropCondName',
      hidden:menuName == 'pitAndPond'
    },
    {
      label: '是否承包',
      prop: 'isContractedLand',
      render:({row})=>{
        if (row.isContractedLand) {
          return '是';
        } else {
          return '否';
        }
      }
    },
    // 家庭承包 显示
    {
      label: '是否流转',
      prop: 'isTransfer',
      hidden:menuName == 'pitAndPond',
      render:({row})=>{
        if (row.isTransfer) {
          return '是';
        } else {
          return '否';
        }
      }
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = {}
        if(isEdit){
        operationBtn = {
          EDIT: (
            <Hyperlink
              row={row}
              index={index}
              func={editFunc}
              text={'编辑'}
            />
          ),
          DEL: (
            <el-popconfirm
              title="确定删除当前项？"
              width="220"
              onConfirm={() => {
                delFunc(row,index);
              }}
            >
              {{
                reference: () => <Hyperlink  text={'删除'}></Hyperlink>
              }}
            </el-popconfirm>
          )
        };
        }else{
          operationBtn = {
            DETAILS: (
            <Hyperlink
              row={row}
              index={index}
              func={seeDateils}
              text={'查看'}
            />
          ),
        };
        }

        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {moreBtnRender(['EDIT','DEL','DETAILS'], operationBtn)}
          </div>
        );
      }
    }
  ];
}
//select 自定义组件
const getSelectComponent = e => {
  return (<el-select style="width:100%" onChange={i => {e.onChange?e.onChange(i):{}}}>
      {e.list.map(item => (
        <el-option key={item[e.value]} label={item[e.label]} value={item[e.value]} />
      ))}
    </el-select>)

};
// 表单配置
export const useSchema = params => {
  let arr = [
    {
      label: params.menuName == 'pitAndPond'?'坑塘代码':'地块代码',
      component: ()=><LetterNumInput/>,
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'landCode'
    },
    {
      label:params.menuName == 'pitAndPond'?'坑塘面积（亩）':'地块面积（亩）',
      component:()=> <NumberInput/>,
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'landArea'
    },
    {
      label: '土地类型',
      component: () => getSelectComponent({ list: params.LandType, value: 'code', label: 'name' }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'dicLandCode'
    },
    // 家庭承包 显示
    {
      label: '种植情况',
      hidden:params.menuName == 'pitAndPond',
      component: () => getSelectComponent({ list: params.cropType, value: 'code', label: 'name' }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'dicCropCondCode'
    },
    {
      label: '是否承包',
      component: () => getSelectComponent({ list: params.obtainType, value: 'value', label: 'label' }),
      props: {
        placeholder: '请输入',
        maxlength: 50,
        disabled:true
      },
      prop: 'isContractedLand'
    },
    // 家庭承包 显示
    {
      label: '是否流转',
      hidden:params.menuName == 'pitAndPond',
      component: () => getSelectComponent({ list: params.obtainType, value: 'value', label: 'label' }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'isTransfer'
    },
    {
      label: '备注',
      component:'el-input',
      hidden: false,
      props: {
        placeholder: '请输入',
        maxlength: 200,
        type: 'textarea'
      },
      colProps: {
        span: 24
      },
      prop: 'remark'
    }
  ];
  let detail = [];
  // 查看详情 文本显示
  if (!params.isEdit) {
    arr.map(item => {
      let obj = {
        label: item.label,
        prop: item.prop,
        colProps: item.colProps,
        hidden:item.hidden || false
      };
      //用于多选框 赋值
      if(['isTransfer','isContractedLand'].includes(item.prop)){
        obj.prop = item.prop + 'Name'
      }
      if (['dicLandCode','dicCropCondCode'].includes(item.prop)) {
        obj.prop = item.prop.replace('Code', 'Name');
      }
      detail.push(obj);
    });
  }
  return params.isEdit? arr : detail
};
// 表单验证
export const useRules = params => {
  if (params.isEdit) {
    if(params.menuName == 'pitAndPond'){
      return {
        landCode: [ { required: true, message: '必填',trigger: 'change' }],
        landArea: [{ required: true, message: '必填', trigger: 'change' }],
        dicLandCode: [{ required: true, message: '必填', trigger: 'change' }],
        isContractedLand: [{ required: true, message: '必填', trigger: 'change' }],
      };
    }else{
      // 家庭承包 显示
      return {
        landCode: [ { required: true, message: '必填',trigger: 'change' }],
        landArea: [{ required: true, message: '必填', trigger: 'change' }],
        dicLandCode: [{ required: true, message: '必填', trigger: 'change' }],
        dicCropCondCode: [{ required: true, message: '必填', trigger: 'change' }],
        isContractedLand: [{ required: true, message: '必填', trigger: 'change' }],
        isTransfer: [{ required: true, message: '必填', trigger: 'change' }],
      };
    }

  } else {
    return {};
  }
};


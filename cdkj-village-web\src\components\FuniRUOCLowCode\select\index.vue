<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-23 19:34:47
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-08-24 15:01:04
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCodeV2/select/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <el-popover
    ref="popoverRef"
    :popper-style="{
      padding: 0
    }"
    :width="width"
    :virtual-ref="selectRef"
    trigger="click"
    virtual-triggering
  >
    <div class="el-select-dropdown" style="max-height: 200px">
      <el-scrollbar max-height="200px">
        <div class="el-select-dropdown__wrap">
          <ul class="el-scrollbar__view el-select-dropdown__list">
            <li
              class="el-select-dropdown__item"
              :class="{
                hover: item.id == mk,
                selected: setSelected(item)
              }"
              :key="item.id"
              @mousemove="mk = item.id"
              @click="clickFun(item)"
              v-for="item in selectList"
            >
              {{ item.name }}
            </li>
          </ul>
        </div>
      </el-scrollbar>
    </div>
  </el-popover>
</template>
<script setup>
import { onMounted, ref, watch, computed } from 'vue';
import { getUserInfo } from './../dialog/user/hooks/oneself';
import { getUserOrgInfo } from './../dialog/org/hooks/oneself';
const props = defineProps({
  selectRef: Object,
  propsConfig: Object,
  value: {
    type: Array,
    default: []
  }
});
const width = ref('150px');
const selectList = ref([]);
const mk = ref('');
const valueList = ref([]);
const popoverRef = ref();
const emit = defineEmits(['updateValue']);
watch(
  () => props.selectRef,
  newVal => {
    width.value = props.selectRef ? props.selectRef.offsetWidth + 'px' : '150px';
  },
  {
    immediate: true,
    deep: true
  }
);
watch(
  () => props.value,
  newVal => {
    valueList.value = newVal;
  },
  {
    immediate: true,
    deep: true
  }
);
onMounted(() => {
  getSelectOption();
});

//  mode: 'multiple',
const clickFun = obj => {
  let index = valueList.value.findIndex(item => item.id === obj.id);
  if (props.propsConfig.mode === 'multiple') {
    if (index > -1) {
      valueList.value.splice(index, 1);
    } else {
      valueList.value.push({
        type: props.propsConfig.type,
        id: obj.id,
        name: obj.name
      });
    }
  } else {
    valueList.value = [
      {
        type: props.propsConfig.type,
        id: obj.id,
        name: obj.name
      }
    ];
    popoverRef.value.hide();
  }
  emit('updateValue', valueList.value);
};

const setSelected = computed(() => {
  return item => {
    let index = valueList.value.findIndex(el => el.id === item.id);
    return index > -1;
  };
});

const getSelectOption = async () => {
  let { type } = props.propsConfig;
  let list = [];
  let keyName = '';
  if (type === 'user') {
    list = await getUserInfo({});
    keyName = 'nickName';
  } else if (type === 'org') {
    list = await getUserOrgInfo({});
    keyName = 'orgName';
  }

  selectList.value = list.map(item => {
    let { id, [keyName]: name } = item;
    return {
      id,
      name
    };
  });
};
</script>
<style scoped>
:deep(.el-popover.el-popper) {
  padding: 0;
}
</style>

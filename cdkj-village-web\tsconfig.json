{"compilerOptions": {"outDir": "./", "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "baseUrl": ".", "allowJs": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "noImplicitAny": false, "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types/", "./types"], "types": ["element-plus/global"]}, "include": ["src/**/*"], "exclude": ["dist", "node_modules"]}
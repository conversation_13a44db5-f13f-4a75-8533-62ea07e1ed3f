package cn.fight.village.domain.statistics.entity;

import lombok.Data;

import java.util.List;

@Data
public class StatisticsResult {
    //发包地块数
    private Integer contractLandCount = 0;

    //发包地块面积
    private Double contractLandArea = 0.0d;

    private String contractLands;

    //发包总户数
    private Integer contractHouseholderCount = 0;

    //流转地块数
    private Integer transLandCount = 0;

    //流转地块面积
    private Double transLandArea = 0.0d;

    //流转总户数
    private Integer transLandHousehold = 0;

    private String transLands;
}

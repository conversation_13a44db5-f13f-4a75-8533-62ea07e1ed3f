<!-- 成员编号-成员信息 -->
<template>
  <div>
    <GroupTitle title="户信息" />
    <funiForm v-bind="formConfig" ref="refForm" :col="2">
      <template #label="scope">
        <span>{{ scope.row }}</span>
      </template>
    </funiForm>
    <GroupTitle title="人员信息" />
    <BaseTable :columusOptions="useFamilyColumus({ seeDateils })" :detailData="props.detailData.familyMemberVos" />
    <AddModal ref="addModalRef" :isExamine="isExamine" />
  </div>
</template>

<script setup lang="jsx">
import { reactive, ref, unref, onUpdated, watch, computed } from 'vue';
import GroupTitle from '@/apps/lfpra/common/components/groupTitle/index.vue';
import BaseTable from './baseTable.vue';
import { useFamilyColumus } from '../../hooks/detailsColumus';
import AddModal from '../../components/familyMember/addModal.vue';
const props = defineProps({
  detailData: {
    type: Object,
    default: function () {
      return {};
    }
  }
});
const refForm = ref();
const addModalRef = ref(null);
const isExamine = ref(true);
const formConfig = computed(() => {
  return {
    schema: [
      { prop: 'memberCode', label: '户编号', colProps: { span: 22 } },
      { prop: 'address', label: '家庭住址', colProps: { span: 22 } },
      {
        prop: 'isPoverty',
        label: ' 是否贫困户'
      },
      {
        prop: 'isAllowance',
        label: ' 是否低保户'
      },
      { prop: 'remark', label: '备注', colProps: { span: 22 } }
    ]
  };
});

const seeDateils = res => {
  isExamine.value = true;
  addModalRef.value.show(res,isExamine.value);
};

watch(
  () => props.detailData,
  () => {
    let data = {
      memberCode: props.detailData.memberCode,
      address: props.detailData.address,
      isPoverty: props.detailData.isPoverty ? '是' : '否',
      isAllowance: props.detailData.isAllowance ? '是' : '否',
      remark: props.detailData.remark
    };
    refForm.value && refForm.value.setValues(data);
  },
  { immediate: true, deep: true }
);
</script>

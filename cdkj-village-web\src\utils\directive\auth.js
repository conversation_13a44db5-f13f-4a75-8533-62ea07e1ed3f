/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-03-01 09:50:05
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-11-08 16:47:27
 * @FilePath: /funi-paas-cs-web-cli/src/utils/directive/auth.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

import { usePermissionStore } from '@/stores/usePermissionStore';

export default (el, binding, vnode, prevVnode) => {
  const bindingValue = binding.value;
  if (!bindingValue) return;

  const authCodes = bindingValue.toString().split(',') || [];
  const { permissionsOfParentMenu, permissionsInCurrentPage } = usePermissionStore();
  const permissions = (binding.modifiers?.menu ? permissionsOfParentMenu : permissionsInCurrentPage) || [];

  /**
   * Node.isConnected - https://developer.mozilla.org/zh-CN/docs/Web/API/Node/isConnected
   * 判断元素是否与dom树相连
   * 使用该属性可以有效避免即将被缓存的元素被错误移除
   */
  if (el.isConnected && !authCodes.find(item => permissions.includes(item))) {
    // el.parentNode ? el.parentNode.removeChild(el) : (el.style.display = true);
    el.style.display = 'none';
  } else {
    el.style.display = '';
  }
};

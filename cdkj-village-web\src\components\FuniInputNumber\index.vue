<template>
  <el-input
    ref="input"
    class="funi-input-number"
    v-bind="$attrs"
    type="number"
    :model-value="displayValue"
    :max="max"
    :min="min"
    :validate-event="false"
    :clearable="true"
    :placeholder="placeholder"
    @wheel.prevent
    @input="handleInput"
    @change="handleInputChange"
  >
    <template v-for="(_, slot) in $slots" #[slot]="params">
      <slot :name="slot" v-bind="params || {}" />
    </template>
  </el-input>
</template>
<script setup>
import { watch, computed, onMounted, reactive, ref } from 'vue';

defineOptions({ name: 'FuniInputNumber', inheritAttrs: false });

const props = defineProps({
  /**
   * @description binding value
   */
  modelValue: Number,
  /**
   * @description the maximum allowed value
   */
  max: {
    type: Number,
    default: Number.POSITIVE_INFINITY
  },
  /**
   * @description the minimum allowed value
   */
  min: {
    type: Number,
    default: Number.NEGATIVE_INFINITY
  },
  /**
   * @description precision of input value
   */
  precision: {
    type: Number,
    default: 0,
    validator: val => val >= 0 && val === Number.parseInt(`${val}`, 10)
  },
  placeholder: {
    type: String,
    default: '请输入'
  }
});
const emit = defineEmits(['update:modelValue', 'change', 'input']);

const input = ref();
const data = reactive({
  currentValue: props.modelValue,
  userInput: null
});

const displayValue = computed(() => {
  if (data.userInput !== null) return data.userInput;

  let currentValue = data.currentValue;
  if ($utils.isNil(currentValue)) return '';
  if ($utils.isNumber(currentValue)) {
    if (Number.isNaN(currentValue)) return '';
    if (!$utils.isUndefined(props.precision)) {
      currentValue = currentValue.toFixed(props.precision);
    }
  }
  return currentValue;
});

const toPrecision = (num, pre) => {
  if (pre === 0) return Math.round(num);
  let snum = String(num);
  const pointPos = snum.indexOf('.');
  if (pointPos === -1) return num;
  const nums = snum.replace('.', '').split('');
  const datum = nums[pointPos + pre];
  if (!datum) return num;
  const length = snum.length;
  if (snum.charAt(length - 1) === '5') {
    snum = `${snum.slice(0, Math.max(0, length - 1))}6`;
  }
  return Number.parseFloat(Number(snum).toFixed(pre));
};

const verifyValue = (value, update) => {
  const { max, min, precision } = props;
  if (max < min) {
    throwError('InputNumber', 'min should not be greater than max.');
  }
  let newVal = Number(value);
  if ($utils.isNil(value) || Number.isNaN(newVal)) {
    return null;
  }
  if (value === '') {
    return null;
  }

  if (!$utils.isUndefined(precision)) {
    newVal = toPrecision(newVal, precision);
  }
  if (newVal > max || newVal < min) {
    newVal = newVal > max ? max : min;
    update && emit('update:modelValue', newVal);
  }
  return newVal;
};

const setCurrentValue = (value, emitChange = true) => {
  const oldVal = data.currentValue;
  const newVal = verifyValue(value);
  if (!emitChange) {
    emit('update:modelValue', newVal);
    return;
  }
  if (oldVal === newVal) return;
  data.userInput = null;
  emit('update:modelValue', newVal);
  emit('change', newVal, oldVal);
  data.currentValue = newVal;
};

const handleInput = value => {
  data.userInput = value;
  const newVal = value === '' ? null : Number(value);
  emit('input', newVal);
  setCurrentValue(newVal, false);
};

const handleInputChange = value => {
  const newVal = value !== '' ? Number(value) : '';
  if (($utils.isNumber(newVal) && !Number.isNaN(newVal)) || value === '') {
    setCurrentValue(newVal);
  }
  data.userInput = null;
};

const focus = () => input.value?.focus?.();
const blur = () => input.value?.blur?.();

watch(
  () => props.modelValue,
  value => {
    const userInput = verifyValue(data.userInput);
    const newValue = verifyValue(value, true);
    if (!$utils.isNumber(userInput) && (!userInput || userInput !== newValue)) {
      data.currentValue = newValue;
      data.userInput = null;
    }
  },
  { immediate: true }
);

onMounted(() => {
  const { modelValue } = props;
  if (!$utils.isNumber(modelValue) && modelValue != null) {
    let val = Number(modelValue);
    if (Number.isNaN(val)) {
      val = null;
    }
    emit('update:modelValue', val);
  }
});

defineExpose({
  /** @description get focus the input component */
  focus,
  /** @description remove focus the input component */
  blur
});
</script>

<style lang="scss" scoped>
.funi-input-number {
  :deep() {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none !important;
      margin: 0;
    }

    .el-input__inner {
      -webkit-appearance: none;
      -moz-appearance: textfield;
      line-height: 1;
    }
  }
}
</style>

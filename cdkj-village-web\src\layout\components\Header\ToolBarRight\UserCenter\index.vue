<!--
 * @Author: 古加文 <EMAIL>
 * @Date: 2022-12-21 15:23:18
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-10-16 19:48:49
 * @FilePath: /funi-paas-cs-web-cli/src/layout/components/Header/ToolBarRight/UserCenter/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="user-menu-view">
    <el-dropdown trigger="click">
      <div class="user-drop">
        <img :src="ic_user" alt="" />
        <el-button class="btn" link>{{ store.userName }}</el-button>
      </div>
      <template #dropdown>
        <el-dropdown-menu class="menu">
          <template v-for="(childItem, index) in getMenuList" :key="index">
            <el-divider v-if="index > 0" style="margin: 7px 0" />
            <el-dropdown-item class="menu-item">
              <img class="menu-item-icon" :src="childItem.icon" />
              <div class="menu-item-text" @click="() => handleItemAction(childItem)">
                {{ childItem.label }}
              </div>
            </el-dropdown-item>
          </template>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!--  -->
    <UserInfoModalEdit v-if="showUserInfoModal" ref="uimeRef" />
    <component
      v-for="item in registerComponents"
      :key="item.key"
      :is="componentRender(item)"
      :ref="(el) => (registerRef[item.key] = el)"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, getCurrentInstance } from "vue";
import UserInfoModalEdit from "./UserInfoModalEdit.vue";
import { logStore } from "./store/logStore";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import ic_user from "./assets/icon/ic_user.png";

const props = defineProps({
  option: { type: Object, default: () => ({}) },
});

const showUserInfoModal = ref(false);
const store = logStore();

const getImageUrl = (name) => new URL(`./assets/icon/${name}.png`, import.meta.url).href;

onMounted(() => {});

//默认提供的下拉菜单项
const getMenuList = [{ key: "LOGOUT", label: "退出登录", icon: getImageUrl("ic_logout_pressed") }];

const handleItemAction = (item) => {
  switch (item.key) {
    case "USER_INFO": //用户信息
      openUserInfo();
      break;
    case "USER_CENTER": //用户中心
      navToUserCenter(item);
      break;
    case "APP_MANAGE": //应用管理
      navToAppManage();
      break;
    case "ONLINE_DOC": //在线文档
      navToOnlineDoc();
      break;
    case "LOGOUT": //退出登录
      logout();
      break;
    case "REC_RECORD": //录制记录
      navToUserRecord();
      break;
    case "LOG": //我的日志
      navToUserLog();
      break;
    default:
      const registedItem = register.get(item.key);
      if (!!registedItem && $utils.isFunction(registedItem.action)) {
        registedItem.action(item, registerRef[item.key]);
      }
      break;
  }
};

function componentRender(item) {
  if ($utils.isFunction(item.component)) {
    return item.component(item);
  }
  return item.component;
}

/**
 * 跳转应用管理
 */
const navToAppManage = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/ccsapp/#/appManager/views/userAppList`);
};

/**
 * 跳转在线文档
 */
const navToOnlineDoc = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/site/doc/#/`);
};

/**
 * 跳转用户中心
 */
const navToUserCenter = (item) => {
  // sessionStorage.removeItem('sysCode');
  // sessionStorage.removeItem('token');
  sessionStorage.clear();
  window.location.href = `${window.location.origin}/${$utils.getTenantID()}/casapp/#/${item.path || "home"}`;
};

/**
 * 跳转录制记录
 */
const navToUserRecord = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/casapp/#/recordList`);
};

/**
 * 跳转我的日志
 */
const navToUserLog = () => {
  window.open(`${window.location.origin}/${$utils.getTenantID()}/casapp/#/logList`);
};

/**
 * 退出登录
 */
const { push } = useRouter();
const logout = () => {
  ElMessageBox.confirm("确定是否退出登录？", "退出登录", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  }).then(async () => {
    sessionStorage.removeItem("token");
    sessionStorage.removeItem("multi-tabs")
    push({
      name: "Login",
    });
  });
};
/**
 * 个人信息
 */
const openUserInfo = () => {
  uimeRef.value.show();
};
</script>

<style lang="less" scoped>
.other-link {
  display: flex;
  align-items: center;
  margin-right: 16px;

  .btn {
    line-height: 22px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }

  img {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    filter: grayscale(100%) brightness(200%);
  }
}

.user-drop {
  display: flex;
  align-items: center;
  padding: 0 10px;
  min-width: 80px;

  img {
    height: 24px;
    width: 24px;
    margin-right: 4px;
  }

  .btn {
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    line-height: 20px;
  }
}

.user-menu-view {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.menu {
  padding-left: 8px;
  padding-right: 8px;
}

.menu-item:hover img {
  filter: grayscale(0%);
}

.menu-item:hover div {
  color: var(--el-menu-active-color);
}

.menu-item-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  filter: grayscale(100%);
}

.menu-item-text {
  font-size: 14px;
  font-weight: 400;
  color: var(--el-text-color-primary);
  line-height: 26px;
}
</style>

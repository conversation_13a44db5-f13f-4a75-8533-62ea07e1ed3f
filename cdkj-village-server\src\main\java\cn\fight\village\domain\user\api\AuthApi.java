package cn.fight.village.domain.user.api;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.user.request.LoginRequest;
import cn.fight.village.domain.user.service.UserService;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * 身份认证api
 */
@RestController
@RequestMapping("auth")
public class AuthApi {

    @Resource
    private UserService userService;

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 用户登录
     *
     * @return
     */
    @PostMapping("login")
    public JsonResult login(@RequestBody LoginRequest request) {
        return userService.userLogin(request);
    }

    /**
     * 获取验证码
     *
     * @param response
     * @return
     * @throws IOException
     */
    @GetMapping("/captchaImage")
    public JsonResult getCode(HttpServletResponse response) throws IOException {
        return userService.getCaptchaImage(response);
    }
}

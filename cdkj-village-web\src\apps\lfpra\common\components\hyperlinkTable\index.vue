<template>
  <span v-auth="auth" style="color: var(--el-color-primary); cursor: pointer" @click="func(row, index, name)">{{ text
  }}</span>
</template>

<script setup>
const props = defineProps({
  row: { type: Object, default: () => { } },
  index: { type: Number, default: 0 },
  name: { type: String, default: '' },
  func: { type: Function, default: () => { } },
  text: { type: String, default: '' },
  auth: { type: String, default: '' }
});
</script>

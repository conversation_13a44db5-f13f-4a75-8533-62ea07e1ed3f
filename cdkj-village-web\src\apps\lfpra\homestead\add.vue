<!-- 新增/编辑 -->
<template>
  <div>
    <funi-detail :bizName="bizName" :showHead="true" :auditButtons="buttons" :steps="steps" :detailHeadOption="detailHeadOption || {}" />

  </div>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import BaseDetails from './component/baseDetails.vue';
const route = useRoute();
const bizName = ref(route.query.bizName);
const type = ref(route.query.type);
const id = ref(route.query.id);
const detail = ref({});

const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    hideStatusBar: false,
    statusName:' 宅基地数据状态',
    status:'有效',
    serialName: '宅基地编号',
    hideStatusName: true,
    no: route.query.no || '--'
  };
  return obj;
});

const steps = computed(() => {
  let arr = [
    {
      title: '宅基地信息',
      preservable: false,
      type: BaseDetails,
      props: {
        id: id.value,
        isEdit: !['info'].includes(route.query.type)
      }
    }
  ];
  return arr;
});
</script>

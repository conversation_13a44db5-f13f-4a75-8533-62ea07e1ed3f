package cn.fight.village.domain.common.util;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.task.CheckLicenceTask;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

import cn.hutool.jwt.JWTUtil;
import cn.hutool.jwt.signers.JWTSigner;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * 安全工具
 */
@Slf4j
public final class SecureUtils {

    private SecureUtils() {}

    private static final String MD5_PREFIX = "Z~l2QeSF_H[4*wu@";

    private static final byte[] SYM_KEY = "6Vge*DvM4BVy$xMN".getBytes();

    //JWT-私钥
    private static final String JWT_PREFIX = "2WS!BHhF3Xpb&@Eu";

    //JWT-签名器
    private static final JWTSigner JWT_SIGNER = JWTSignerUtil.hs256("rz9&x5Pt".getBytes());

    //JWT-新的算法
    private static final Algorithm JWT_ALGORITHM = Algorithm.HMAC256(JWT_PREFIX);

    private static final Digester SM3_DIGESTER = DigestUtil.digester("sm3");

    private static  final  SymmetricCrypto AES = new SymmetricCrypto(SymmetricAlgorithm.AES, SYM_KEY);

    private static final String RSA_PUB_KEY
            = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDKJuzoN3njc7XNePvmqqagYp+KgaPIsZADEvtx1I4eiV3ztm/hgksi/+PRdApXlFVaDlX7W2QPbsBQoJfFjaVvU1/GbeqLpS6Xhm1u0XpN734s7SOIFXAzWXXxCtYddfjetKZvoSroI0F27j+B6tZjSWUL33heYanu0JtLrcE5TwIDAQAB";
    private static final RSA LICENCE_RSA = new RSA(null, RSA_PUB_KEY);

    public static void init() {
        log.info("初始化安全工具");
    }
    /**
     * 摘要加密
     * @return
     */
    public static String digestEncode(String text) {
        if (StringUtils.hasText(text)) {
            return SM3_DIGESTER.digestHex(MD5_PREFIX + text);
        }
        return text;
    }

    /**
     * 对称加密
     * @param input
     * @return
     */
    public  static String stringEncode(String input) {
        try {
           input =  getAES().encryptHex(input, CharsetUtil.UTF_8);
        } catch (Exception e) {
            throw new BusinessException("加密失败");
        }

        return input;
    }

    private static synchronized SymmetricCrypto getAES() {
        return AES;
    }

    private static synchronized JWTSigner getJwtSigner() {
        return JWT_SIGNER;
    }

    /**
     * 对称解密
     * @param input
     * @return
     */
    public static String stringDecode(String input) {
        try {
            input = getAES().decryptStr(input, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("解密失败: " + input);
        }

        return input;
    }

    /**
     * 生成用户令牌
     * @param user
     * @return
     */
    @Deprecated
    public static String createToken(User user) {
        byte[] key = JWT_PREFIX.getBytes();

        return cn.hutool.jwt.JWT.create()
                .setSigner(getJwtSigner())
                .setPayload("account", user.getAccount())
                .setPayload("name", user.getUsername())
                .setPayload("type", user.getUserType())
                .setPayload("create", CommonUtils.currentDate().getTime())
                .setKey(key)
                .sign();
    }

    /**
     * 创建token
     * @param user
     * @return
     */
    public static String generateToken(User user) {
        //载荷
        Map<String, Object> payload= new HashMap<>();
        payload.put("account", user.getAccount());
        payload.put("name", user.getUsername());
        payload.put("type", user.getUserType());
        payload.put("create", CommonUtils.currentDate().getTime());

        //生成token
        String token = null;
        try {
             token = JWT.create()
                    .withIssuer("auth0") // 标准声明
                    .withIssuedAt(new Date()) // 签发时间
                    .withExpiresAt(new Date(System.currentTimeMillis() + 30 * 60 * 1000)) // 过期时间（半小时后）
                    .withPayload(payload) // 添加Payload
                    .sign(JWT_ALGORITHM); // 签名
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new BusinessException("生成token失败");
        }

        return token;
    }

    /**
     * 校验解析token
     * @param token
     * @return
     */
    public static DecodedJWT verifyToken(String token) {
        return JWT.require(JWT_ALGORITHM).build().verify(token);
    }

    /**
     * 用户令牌验签
     * @param jwt
     * @return
     */
    @Deprecated
    public static boolean verifySigner(cn.hutool.jwt.JWT jwt) {
        return jwt.verify(getJwtSigner());
    }

    /**
     * 用户令牌验签
     * @param token
     * @return
     */
    @Deprecated
    public static boolean verifySigner(String token) {
            return JWTUtil.verify(token, getJwtSigner());
    }

    /**
     * 敏感字段加密
     * @param object
     */
    public static void  sensitiveFieldEncrypt(Object object) {
        sensitiveFieldManage(true,object);
    }

    /**
     * 敏感字段解密
     * @param object
     */
    public static void  sensitiveFieldDecrypt(Object object) {
        sensitiveFieldManage(false,object);
    }

    /**
     * 敏感字段加密
     * 只适用于字符串加解密
     * @param object 目标对象
     * @param isEncrypt 是否为加密
     * @param
     */
    private static void  sensitiveFieldManage(boolean isEncrypt,Object object) {
        try {
            Class<?> clazz = object.getClass();
            BeanInfo beanInfo = Introspector.getBeanInfo(clazz);

            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            if (ArrayUtil.isNotEmpty(propertyDescriptors)) {
                for (PropertyDescriptor pd : propertyDescriptors) {
                    //跳过 Class 属性
                    if ("class".equals(pd.getName())) {
                        continue;
                    }

                    //获取对应的字段，判断字段是否存在敏感字段注解
                    Field field = null;
                    try {
                        field = clazz.getDeclaredField(pd.getName());
                    } catch (NoSuchFieldException e) {
                        log.debug(e.getMessage());
                        continue;
                    }

                    if (field.isAnnotationPresent(Sensitive.class)) {
                        //set方法
                        Method setter = pd.getWriteMethod();
                        Method getter = pd.getReadMethod();

                        Object orgString = getter.invoke(object);

                        //非空
                        if (orgString == null) {
                            continue;
                        }

                        //非空字符
                        String string = orgString.toString();
                        if (!StringUtils.hasText(string)) {
                            continue;
                        }

                        //字段加密
                        if (isEncrypt) {
                           String enString = SecureUtils.stringEncode(string);
                           setter.invoke(object, enString);
                        }

                        //字段解密
                        else {
                            String deString = SecureUtils.stringDecode(string);
                            setter.invoke(object, deString);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            String errMsg = isEncrypt ? "敏感数据对象加密失败" : "敏感数据对象解密失败";
            throw new BusinessException(errMsg);
        }

    }

    /**
     * 获取激活许可证请求信息
     */
    public static String getLicenceRequest() {
        HashMap<String, Object> result = new HashMap<>();
        result.put("macCode",NetUtil.getLocalMacAddress());
        String reqParams = JSON.toJSONString(result);
        byte[] encrypt = LICENCE_RSA.encrypt(reqParams, CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);
        return Base64.encode(encrypt);
    }

    /**
     * 验证许可证
     * @return
     */
    public static Boolean checkLicence() {
        try {
            String licenceCode = readLicence();
            byte[] licenceByteArr = LICENCE_RSA.decrypt(licenceCode, KeyType.PublicKey);
            String licenceStr = StrUtil.str(licenceByteArr, CharsetUtil.CHARSET_UTF_8);
            JSONObject licence = JSON.parseObject(licenceStr);
            String localMacAddress = NetUtil.getLocalMacAddress();
            String macCode = (String)licence.get("macCode");
            if (localMacAddress.equals(macCode)) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Boolean.FALSE;
    }

    /**
     * 激活许可证
     * @param licenceCode
     */
    public static String activeLicence(String licenceCode) throws IOException {
        writeLicence(licenceCode);

        if (checkLicence()) {
            System.out.println("应用已激活");
           CheckLicenceTask.setLicenceAvailable(Boolean.TRUE);
            return "激活成功";
        }

        return "激活失败";
    }

   public static void writeLicence(String licence) throws IOException {
       org.springframework.core.io.ClassPathResource resource = new ClassPathResource("licence");
       String path = resource.getFile().getPath();
       try (FileWriter writer = new FileWriter(path)) {
           // 将字符串写入文件
           writer.write(licence);
       } catch (IOException e) {
          e.printStackTrace();
       }
   }

   public static String readLicence() throws IOException {
       org.springframework.core.io.ClassPathResource resource = new ClassPathResource("licence");
       String path = resource.getFile().getPath();
       Path filePath = Paths.get(path); // 指定要读取的文件路径

       StringBuilder contentBuilder = new StringBuilder(); // 创建一个 StringBuilder 对象，用于保存文件内容
       try (BufferedReader reader = Files.newBufferedReader(filePath)) { // 打开文件进行逐行读取
           String line;

           while ((line = reader.readLine()) != null) { // 当有新行时，不断追加到 StringBuilder 上
               contentBuilder.append(line).append("\n");
           }
       } catch (IOException e) {
           e.printStackTrace();
       }

       return contentBuilder.toString();
   }

    //生成许可证内容方法，不可外泄，不可编译
    public static String getLicenceCode(String requestCode) {
        //私钥RSA，不可外泄
        String privetKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMom7Og3eeNztc14++aqpqBin4qBo8ixkAMS+3HUjh6JXfO2b+GCSyL/49F0CleUVVoOVftbZA9uwFCgl8WNpW9TX8Zt6oulLpeGbW7Rek3vfiztI4gVcDNZdfEK1h11+N60pm+hKugjQXbuP4Hq1mNJZQvfeF5hqe7Qm0utwTlPAgMBAAECgYAogG0Fx5ulrRi3Pq6lk/pHdwsE+p1gh+bVHtf/Afmpd5c5zYsT8QFx1TgFTo0F6APDw8yJTFG7X9KrNMnDvHHxBPIYKD2ocHmWgPDrfA9ESHpOFvgI11bJiXIYi2BReLERryUXn3nokKf/YOnlKzIQQisEeJz5YmSShOJr1PwosQJBAPuZlgNLGPSrRM5BLovJ6pIgI7Zoc340lID90aMjJj/vI7aFHwAVBfJnEkyQUgVdXiiCqt/JaL4YlcwwXM2T3F8CQQDNr/YEhdXxrgfkFKbiGHRK8PsBU8MO9/0+WJjNGAJ9WxKYsZghtDHwvMHxqevheK4DXu4fTtdkf0QvnRL5t8kRAkEAhX/K/1hbl4dA7QfdAMNUudBf4qutjGut2HvVPnCqHQZwtqoP9uUw0JwsM3/oZXxTN7+Nl0yxTRySb1PCCjrIlQJAEvQO8HXAtCd8NKkug5ELTkiMaJ/mTn/Nhyw00FlRCWoV+ZoL0bdADtXl7TXiNYGgT1E1Eg96y5jqmJSZxgcIcQJAAxMtu70IgqYfno6hlu4EPaG5dbSsHx2yqu9t69L6GuJXzDzkGI+Mv3i/s9F/qauUW6N1+XJYkXpvjLVEWytfkQ==";
        RSA rsa = new RSA(privetKey, null);

        //base64解码
        byte[] reqCodeBA = Base64.decode(requestCode);
        String decode = StrUtil.str(rsa.decrypt(reqCodeBA, KeyType.PrivateKey), CharsetUtil.CHARSET_UTF_8); //私钥解密
        JSONObject jsonObject = JSON.parseObject(decode);
        String macCode = (String)jsonObject.get("macCode");

        //获取到的mac地址再使用私钥加密
        Map<String, Object> result = new HashMap<>();
        result.put("macCode",macCode);
        String resultJson = JSON.toJSONString(result);
        byte[] encode = rsa.encrypt(resultJson, KeyType.PrivateKey);

        //密文转换为base64编码
        return Base64.encode(encode);
    }

    public static void main(String[] args) {
        String input = "ZBF4xDSX0tUOmthx6ePQSeHTHUZzLcgRSQc+3h1x1HbeP9XLoOP0bGLjhm33VLuvVKucSq0LQHn7MD6sWDQb8x36V7J9w4gIlPVBBhpjHC6zRfRYhWn/ywaEWwjyAt1Py2kzPdkDc47AdYq0x9M3/fT0mcYltRaC1gZCk/VyxMM=";
        System.out.println("许可证号 = " + getLicenceCode(input));
    }
}

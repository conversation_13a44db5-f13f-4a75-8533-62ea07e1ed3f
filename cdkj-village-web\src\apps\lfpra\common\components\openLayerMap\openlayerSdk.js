import 'ol/ol.css'
import { Map, View } from "ol";//地图,视图
import { defaults as defaultControls } from 'ol/control'; // 引入默认控件集合
import Zoom from 'ol/control/Zoom';
import XYZ from "ol/source/XYZ";
import Overlay from 'ol/Overlay.js';
import { TileWMS, OSM, Vector as VectorSource } from "ol/source";
import { Tile as TileLayer, Vector as VectorLayer, Image as ImageLayer } from 'ol/layer.js';
// import GeoJSON from "ol/format/GeoJSON";
import { GeoJSON, WFS } from 'ol/format.js';
import { Style, Stroke, Fill } from "ol/style";
import ImageWMS from 'ol/source/ImageWMS.js';

class openlayerSdk {
    //map实例
    map = ""
    init(target) {
        // 创建OpenLayers地图
        this.map = new Map({
            target,
            // layers: [tdtImgLayer, wmsLayer_cbd, wmsLayer_ktsm, wmsLayer_zjd, vector],
            // 其他配置选项
            controls: defaultControls().extend([
                new Zoom({ showZoomLabel: true })
            ]),
            view: new View({
                projection: 'EPSG:4326', // 使用WGS84坐标系
                center: [104.24, 30.63],   // 初始中心点经纬度
                zoom: 15                 // 初始缩放级别
            }),
        });

    }
    //添加矢量图层
    addVectorLayer(params) {
        let vectorSource = new VectorSource();
        let layer = new VectorLayer({
            source: vectorSource,
            style: params || new Style({
                stroke: new Stroke({
                    color: '#80e3f6',
                    width: 3,
                }),
            }),
        });
        this.map.addLayer(layer)
        return layer;
    }
    //渲染矢量图层数据
    renderRenderVectorLayer(vectorLayer,json,isClear=true){
        let vectorSource=vectorLayer.getSource();
        if(isClear){
            vectorSource.clear()
        }
        const features = new GeoJSON().readFeatures(json);
        vectorSource.addFeatures(features);
    }
    //添加图层
    addTileLayer(layerParams) {
        var layer = new TileLayer(layerParams);
        this.map.addLayer(layer)
        return layer;
    }
    //添加wms服务
    addImageWMS(params) {
        let image_wms = new ImageWMS(params)
        let layer = new ImageLayer({
            // extent: [-13884991, 2870341, -7455066, 6338219],
            source: image_wms
        });
        this.map.addLayer(layer)
        return layer;
    }
    addOverlay(dom){
        const overlay = new Overlay({
            element: dom,
            autoPan: {
                animation: {
                duration: 250,
                },
            },
        });
        this.map.addOverlay(overlay)
        return overlay;
    }
    getMap() {
        return this.map;
    }
}
export default openlayerSdk

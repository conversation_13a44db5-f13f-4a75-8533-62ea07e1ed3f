<template>
  <div class="statistic-content">
    <div class="search-box">
      <el-input v-model="keyword" clearable style="width: 240px" placeholder="请输入户主姓名查询">
        <template #append>
          <el-button @click="search">查询</el-button>
        </template>
      </el-input>
      <el-button
        style="margin-left: 12px"
        :icon="isShowSearch ? ArrowDown : ArrowLeft"
        @click="isShowSearch = !isShowSearch"
      ></el-button>
    </div>
    <div v-show="isShowSearch">
      <div class="group-box" v-show="!householder">
        <el-button v-show="false"></el-button>
        <el-button :type="currntGroup == '' ? 'primary' : ''" style="margin-left: 12px" @click="searhByGroup('')"
          >全部</el-button
        >
        <el-button :type="currntGroup == item ? 'primary' : ''" v-for="item in groups" @click="searhByGroup(item)">{{
          item
        }}</el-button>
      </div>
      <div class="statistic-info" v-loading="loading" v-show="!householder">
        <div>{{ currntGroup }} 统计信息：</div>
        <div class="list-box">
          <div class="list-item">
            <div class="value">{{ info.contractLandCount }}</div>
            <div class="label">发包地块数量</div>
          </div>
          <div class="list-item">
            <div class="value">{{ info.contractLandArea }}亩</div>
            <div class="label">发包地块总面积</div>
          </div>
          <div class="list-item">
            <div class="value">{{ info.contractHouseholderCount }}</div>
            <div class="label">发包总户数</div>
          </div>
        </div>
        <div class="list-box">
          <div class="list-item">
            <div class="value">{{ info.transLandCount }}</div>
            <div class="label">流转地块数量</div>
          </div>
          <div class="list-item">
            <div class="value">{{ info.transLandArea }}</div>
            <div class="label">流转地块总面积</div>
          </div>
          <div class="list-item">
            <div class="value">{{ info.transLandHousehold }}</div>
            <div class="label">流转总户数</div>
          </div>
        </div>
      </div>
      <div class="householder-info" v-loading="loading" v-show="householder">
        <div>户主 {{ householder }} 统计信息：</div>
        <div class="list-box">
          <div class="list-item">
            <div class="value">{{ info.contractLandCount }}</div>
            <div class="label">承包地块数量</div>
          </div>
          <div class="list-item">
            <div class="value">{{ info.contractLandArea }}亩</div>
            <div class="label">承包总面积</div>
          </div>
        </div>
        <div class="list-box">
          <div class="list-item">
            <div class="value">{{ info.transLandCount }}</div>
            <div class="label">流转地块数量</div>
          </div>
          <div class="list-item">
            <div class="value">{{ info.transLandArea }}亩</div>
            <div class="label">流转总面积</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { ArrowLeft } from "@element-plus/icons-vue";
import { getStatistic, getVillageTeams } from "@/apps/api/land.js";

const props = defineProps({
  mapRef: {},
});

const keyword = ref();
const householder = ref();
const info = ref({});
const currntGroup = ref("");
const groups = ref([]);
const loading = ref(false);
const isShowSearch = ref(true);
/**
 * 查询
 */
function search() {
  loading.value = true;
  householder.value = keyword.value;
  props.mapRef?.highLightLayerClear();
  getStatistic({ householder: householder.value })
    .then((res) => {
      info.value = res;
      if (res.contractLands && householder.value) {
        let lands = res.contractLands.split(",");
        let wfsFilter = lands.map((x) => {
          return { type: "=", key: "dkbm", value: x };
        });
        props.mapRef?.highLightByFilter({ wfsFilter, condition: "or" });
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function searhByGroup(group) {
  currntGroup.value = group;
  props.mapRef?.highLightLayerClear();
  loading.value = true;
  if (group) {
    props.mapRef?.highLightByFilter({ wfsFilter: [{ type: "=", key: "sszb", value: group }] });
  } else {
    props.mapRef?.mapFit([104.26375079369181, 30.60808395476756, 104.29375913652264, 30.627273810432357]);
  }
  getStatistic({ group })
    .then((res) => {
      info.value = res;
    })
    .finally(() => {
      loading.value = false;
    });
}

function getGroups() {
  getVillageTeams().then((res) => {
    groups.value = res.sort((a, b) => {
      // 提取数字部分（匹配开头的数字）
      const numA = parseInt(a);
      const numB = parseInt(b);

      return numA - numB;
    });
  });
  searhByGroup("");
}
getGroups();
</script>

<style lang="scss" scoped>
.statistic-content {
  .search-box {
    display: flex;
    align-items: center;
  }
  .group-box {
    width: 400px;
    margin: 16px 0;
    margin-left: -12px;
    button {
      margin-bottom: 12px;
    }
  }
  .statistic-info {
    width: 400px;
    padding: 16px;
    background-color: #fff;
  }
  .householder-info {
    width: 340px;
    margin-top: 16px;
    padding: 16px;
    background-color: #fff;
  }
  .list-box {
    display: flex;
    .list-item {
      flex: 1;
      text-align: center;
      padding: 16px 0;
      .value {
        font-weight: bold;
        font-size: 24px;
        white-space: nowrap;
      }
      .label {
        font-size: 13px;
      }
    }
  }
}
</style>

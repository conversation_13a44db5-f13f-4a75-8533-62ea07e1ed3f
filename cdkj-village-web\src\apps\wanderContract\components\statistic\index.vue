<template>
  <div class="statistic-content">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-box">
        <el-input
          v-model="keyword"
          clearable
          class="search-input"
          placeholder="请输入户主姓名查询"
          @keyup.enter="search"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="search">查询</el-button>
          </template>
        </el-input>
        <el-button
          class="toggle-btn"
          circle
          :icon="isShowSearch ? ArrowDown : ArrowRight"
          @click="isShowSearch = !isShowSearch"
        ></el-button>
      </div>
    </div>

    <!-- 展开内容区域 -->
    <el-collapse-transition>
      <div v-show="isShowSearch" class="content-section">
        <!-- 分组选择器 -->
        <div class="group-section" v-show="!householder">
          <div class="section-title">
            <el-icon><Location /></el-icon>
            <span>选择村组</span>
          </div>
          <div class="group-buttons">
            <el-button
              :type="currntGroup == '' ? 'primary' : ''"
              class="group-btn"
              @click="searhByGroup('')"
            >
              全部
            </el-button>
            <el-button
              :type="currntGroup == item ? 'primary' : ''"
              v-for="item in groups"
              :key="item"
              class="group-btn"
              @click="searhByGroup(item)"
            >
              {{ item }}
            </el-button>
          </div>
        </div>

        <!-- 统计信息卡片 -->
        <div class="statistics-cards" v-loading="loading" v-show="!householder">
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>{{ currntGroup || '全部' }} 统计信息</span>
          </div>

          <div class="cards-container">
            <!-- 发包信息卡片 -->
            <div class="info-card contract-card">
              <div class="card-title">
                <el-icon class="card-icon"><Document /></el-icon>
                <span>发包信息</span>
              </div>
              <div class="card-content">
                <div class="stat-item">
                  <div class="stat-value">{{ info.contractLandCount || 0 }}</div>
                  <div class="stat-label">地块数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ info.contractLandArea || 0 }}<span class="unit">亩</span></div>
                  <div class="stat-label">总面积</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ info.contractHouseholderCount || 0 }}</div>
                  <div class="stat-label">总户数</div>
                </div>
              </div>
            </div>

            <!-- 流转信息卡片 -->
            <div class="info-card transfer-card">
              <div class="card-title">
                <el-icon class="card-icon"><Switch /></el-icon>
                <span>流转信息</span>
              </div>
              <div class="card-content">
                <div class="stat-item">
                  <div class="stat-value">{{ info.transLandCount || 0 }}</div>
                  <div class="stat-label">地块数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ info.transLandArea || 0 }}<span class="unit">亩</span></div>
                  <div class="stat-label">总面积</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ info.transLandHousehold || 0 }}</div>
                  <div class="stat-label">总户数</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 户主信息卡片 -->
        <div class="householder-section" v-loading="loading" v-show="householder">
          <div class="card-header">
            <el-icon><User /></el-icon>
            <span>户主 {{ householder }} 统计信息</span>
          </div>

          <div class="cards-container">
            <!-- 承包信息卡片 -->
            <div class="info-card contract-card">
              <div class="card-title">
                <el-icon class="card-icon"><Document /></el-icon>
                <span>承包信息</span>
              </div>
              <div class="card-content">
                <div class="stat-item">
                  <div class="stat-value">{{ info.contractLandCount || 0 }}</div>
                  <div class="stat-label">地块数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ info.contractLandArea || 0 }}<span class="unit">亩</span></div>
                  <div class="stat-label">总面积</div>
                </div>
              </div>
            </div>

            <!-- 流转信息卡片 -->
            <div class="info-card transfer-card">
              <div class="card-title">
                <el-icon class="card-icon"><Switch /></el-icon>
                <span>流转信息</span>
              </div>
              <div class="card-content">
                <div class="stat-item">
                  <div class="stat-value">{{ info.transLandCount || 0 }}</div>
                  <div class="stat-label">地块数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ info.transLandArea || 0 }}<span class="unit">亩</span></div>
                  <div class="stat-label">总面积</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
import { ref } from "vue";
import {
  ArrowDown,
  ArrowRight,
  Search,
  Location,
  DataAnalysis,
  Document,
  Switch,
  User
} from "@element-plus/icons-vue";
import { getStatistic, getVillageTeams } from "@/apps/api/land.js";

const props = defineProps({
  mapRef: {},
});

const keyword = ref();
const householder = ref();
const info = ref({});
const currntGroup = ref("");
const groups = ref([]);
const loading = ref(false);
const isShowSearch = ref(true);
/**
 * 查询
 */
function search() {
  loading.value = true;
  householder.value = keyword.value;
  props.mapRef?.highLightLayerClear();
  getStatistic({ householder: householder.value })
    .then((res) => {
      info.value = res;
      if (res.contractLands && householder.value) {
        let lands = res.contractLands.split(",");
        let wfsFilter = lands.map((x) => {
          return { type: "=", key: "dkbm", value: x };
        });
        props.mapRef?.highLightByFilter({ wfsFilter, condition: "or" });
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function searhByGroup(group) {
  currntGroup.value = group;
  props.mapRef?.highLightLayerClear();
  loading.value = true;
  if (group) {
    props.mapRef?.highLightByFilter({ wfsFilter: [{ type: "=", key: "sszb", value: group }] });
  } else {
    props.mapRef?.mapFit([104.26375079369181, 30.60808395476756, 104.29375913652264, 30.627273810432357]);
  }
  getStatistic({ group })
    .then((res) => {
      info.value = res;
    })
    .finally(() => {
      loading.value = false;
    });
}

function getGroups() {
  getVillageTeams().then((res) => {
    groups.value = res.sort((a, b) => {
      // 提取数字部分（匹配开头的数字）
      const numA = parseInt(a);
      const numB = parseInt(b);

      return numA - numB;
    });
  });
  searhByGroup("");
}
getGroups();
</script>

<style lang="scss" scoped>
.statistic-content {
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  // 搜索区域
  .search-section {

    .search-box {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-input {
        width: 320px;

        :deep(.el-input__wrapper) {
          border-radius: 25px 0 0 25px;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          }
        }

        :deep(.el-input-group__append) {
          border-radius: 0 25px 25px 0;
          border-left: none;

          .el-button {
            border-radius: 0 23px 23px 0;
            background: linear-gradient(45deg, #409eff, #67c23a);
            border: none;

            &:hover {
              background: linear-gradient(45deg, #337ecc, #529b2e);
            }
          }
        }
      }

      .toggle-btn {
        background: linear-gradient(45deg, #409eff, #67c23a);
        border: none;
        color: white;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(45deg, #337ecc, #529b2e);
          transform: scale(1.1);
        }
      }
    }
  }

  // 内容区域
  .content-section {
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;

      .el-icon {
        color: #409eff;
        font-size: 18px;
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 2px solid #e4e7ed;

      .el-icon {
        color: #409eff;
        font-size: 20px;
      }
    }
  }

  // 分组选择区域
  .group-section {
    margin-bottom: 16px;
    margin-top: 16px;
    padding: 16px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .group-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .group-btn {
        border-radius: 20px;
        padding: 8px 16px;
        transition: all 0.3s ease;
        border: 2px solid #e4e7ed;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.el-button--primary {
          background: linear-gradient(45deg, #409eff, #67c23a);
          border-color: transparent;

          &:hover {
            background: linear-gradient(45deg, #337ecc, #529b2e);
          }
        }
      }
    }
  }

  // 统计卡片容器
  .statistics-cards,
  .householder-section {
    margin-top: 16px;
    padding: 16px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .cards-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
    }
  }

  // 信息卡片
  .info-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #409eff, #67c23a);
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &.contract-card::before {
      background: linear-gradient(90deg, #409eff, #5dade2);
    }

    &.transfer-card::before {
      background: linear-gradient(90deg, #67c23a, #58d68d);
    }

    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16x;

      .card-icon {
        font-size: 18px;
        color: #409eff;
      }
    }

    .card-content {
      display: flex;
      justify-content: space-around;
      gap: 16px;
    }
  }

  // 统计项
  .stat-item {
    text-align: center;
    flex: 1;

    .stat-value {
      font-size: 28px;
      font-weight: 700;
      color: #303133;
      margin-bottom: 8px;
      background: linear-gradient(45deg, #409eff, #67c23a);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      .unit {
        font-size: 16px;
        color: #909399;
        margin-left: 4px;
      }
    }

    .stat-label {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
    }
  }
}

// 加载动画优化
:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

:deep(.el-loading-spinner) {
  .circular {
    width: 50px;
    height: 50px;
  }
}
</style>

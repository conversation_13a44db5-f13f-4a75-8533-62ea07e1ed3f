<template>
  <!-- 统计报表 -->
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" @headBtnClick="headBtnClick" />
  </div>
</template>
<script setup>
import { ref, computed } from 'vue';
import { queryResourcesStatList, apiUrl } from './api/index';
import { expotrFunction } from '@/apps/lfpra/common/hooks/utils.jsx';
const listPage = ref();
const query = ref();
// 获取列表数据
const lodaData = async (pages, parmas) => {
  query.value = pages;
  let data = await queryResourcesStatList({
    ...pages,
    ...parmas
  });
  return data;
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        lodaData: lodaData,
        isShowSearch: false,
        header:'资源基础信息管理统计报表',
        btns: [{ key: 'export', label: '导出' }],
        columns: [
          { label: '集体组织', prop: 'dicCommunityName' },
          {
            label: '宅基地数量',
            prop: 'siteNum'
          },
          { label: '宅基地总面积（㎡）', prop: 'siteArea' },
          { label: '承包地数量', prop: 'contNum' },
          { label: '承包地总面积（亩）', prop: 'contArea' },
          { label: '坑塘数量', prop: 'swagNum' },
          { label: '坑塘总面积（亩）', prop: 'swagArea' }
        ]
      }
    }
  ];
});

const headBtnClick = res => {
  if (res === 'export') deriveClick();
};

// 导出
const deriveClick = () => {
  expotrFunction({ url: apiUrl.queryForestInfoListExport, params: query.value, FileName: '统计报表' });
};
</script>

<style lang="csss" scoped></style>

package cn.fight.village.domain.contract.request;

import cn.fight.village.domain.common.entity.BaseQuery;

/**
 * 合同查询对象
 *
 */
public class ContractQuery extends BaseQuery {
    //合同信息
    private String type;

    private String upperName;

    private String contractNo;

    private String upper;

    private String underName;

    private String contractType;

    //
    private String rightOrg;

    //所属项目
    private String project;

    //是否签约完成
    private String signed;

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getSigned() {
        return signed;
    }

    public void setSigned(String signed) {
        this.signed = signed;
    }

    public String getRightOrg() {
        return rightOrg;
    }

    public void setRightOrg(String rightOrg) {
        this.rightOrg = rightOrg;
    }

    public String getUpperName() {
        return upperName;
    }

    public void setUpperName(String upperName) {
        this.upperName = upperName;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getUpper() {
        return upper;
    }

    public void setUpper(String upper) {
        this.upper = upper;
    }

    public String getUnderName() {
        return underName;
    }

    public void setUnderName(String underName) {
        this.underName = underName;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}

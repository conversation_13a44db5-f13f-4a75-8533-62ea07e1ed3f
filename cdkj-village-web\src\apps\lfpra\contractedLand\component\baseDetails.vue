<template>
  <div>
    <!-- 地块信息 -->
    <!-- <massifInfo
      :tableData="tableData"
      :isEdit="props.isEdit"
      :otherPatternIds="patternIds"
      @exMassifData="exMassifData"
      layerType="cyheal:fragment_cbd"
    /> -->
    <div v-for="(item, index) in schemaList" :key="index">
      <div>
        <GroupTitle :title="item.title" />
        <!-- 表单 -->
        <funi-form
          :schema="item.list"
          @get-form="e => setForm(e, index)"
          :rules="props.isEdit ? item.rules : {}"
          :border="false"
          :col="2"
        />
      </div>
    </div>
    <!-- 选择户主名 -->
    <ChooseCollection ref="chooseModal" @exportObject="setCollection" />
    <!-- 提交成功dailog 跳转列表页 -->
    <SubmitSuccess ref="su" />
  </div>
</template>
<script setup lang="jsx">
import { ref, reactive, computed, inject, nextTick, watch, onMounted, unref, provide } from 'vue';
import { getCurrentInstance } from 'vue';
import GroupTitle from '@/apps/lfpra/common/components/groupTitle/index.vue';
import ChooseCollection from '@/apps/lfpra/common/components/ownerInfo/chooseCollection.vue';
import { useSchema } from '../hooks/baseDetails.jsx';
import { useRouter } from 'vue-router';
import { ElNotification } from 'element-plus';

// import massifInfo from '@/apps/lfpra/common/components/massifInfo/index.vue';
import SubmitSuccess from '@/apps/lfpra/common/components/submit_success/index.vue';
import {
  dictListHttp,
  queryOwnerShipInfoListtHttp,
  getUnderEmployerInfoHttp,
  getUnderContractorInfoHttp
} from '@/apps/lfpra/common/hooks/api';
import { infoHttp, newHttp, getOtherUnderinfoPatternIdsHttp } from '../hooks/api';
// 接收数据
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true
  },
  id: {
    type: String,
    default: ''
  }
});
const router = useRouter();
//选择户主名弹窗
const chooseModal = ref(null);
// 地块信息数据
const tableData = ref([]);
//共有人信息
const ownerTableData = ref([]);
//存储的一份共有人信息 切换承包方时赋值
const ownerTableDataClone = ref([]);
// 绑定表单实例
const submitObj = reactive({});
const patternIds = ref([]); //其他已经选择的图斑id集合


//下来选择框
const selectList = reactive({
  cardType: [{}] //证件类型
});
const su = ref();
const ownerChangeList = ref(); //选择承包方名称携带的数据

const familyData = reactive({
  familyId: '', //显示共有人
  familySn: '',
  groupNumber: '',
  ownerId: ''
});
//新增 共有人消息列表数据
const ownerChang = e => {
  ownerTableData.value = e;
};
//选择户主
const ownerNameChange = () => {
  let list = [{ familySn: familyData.familySn }];
  chooseModal.value.show(list, 'familySn');
};

// 承包方（代表）名称选择框
const setCollection = e => {
  console.log('执行----');
  // 编辑进入 切换时 保存上一次的
  if (familyData.ownerId == e[0].ownerId) {
    ownerTableData.value = ownerTableDataClone.value;
  } else {
    ownerTableData.value = [];
  }
  ownerChangeList.value = e[0];
  for (let key in familyData) {
    familyData[key] = ownerChangeList.value[key];
  }
  let arrStr = ['contractorName', 'dicCardTypeName', 'cerCertificateNo', 'address', 'cerTelephone'];
  let formData = {};
  arrStr.forEach(item => {
    if (item == 'contractorName') {
      formData[item] = ownerChangeList.value.memberName;
    } else {
      formData[item] = ownerChangeList.value[item];
    }
  });
  formData.dicConModeName = '家庭承包';
  submitObj.form2.setValues(formData);
};
//发包方代码值失去焦点 获取发包方信息
const dicCardChange = e => {
  getUnderEmployerInfoHttp({ employerCode: e }).then(res => {
    if (res.id) {
      submitObj.form1.setValues(res);
    } else {
      submitObj.form1.setValues({ id: '' });
    }
  });
};
//承包方代码值失去焦点 获取承包方信息
const contractorChange = e => {
  getUnderContractorInfoHttp({ contractorCode: e }).then(res => {
    if (res.id) {
      ownerTableData.value = [];
      for (let key in familyData) {
        familyData[key] = res[key];
      }
      submitObj.form2.setValues(Object.assign({}, res, { dicConModeName: '家庭承包' }));
    } else {
      submitObj.form2.setValues({ id: '' });
    }
  });
};

//地块信息 数据
const exMassifData = e => {
  tableData.value = e;
  console.log(tableData.value, '地块信息----');
};
// 配置表单
const schemaList = computed(() => {
  return useSchema({
    isEdit: props.isEdit,
    ...selectList,
    ownerChang,
    ownerTableData: ownerTableData.value, //共有人消息列表数据
    ownerNameChange,
    dicCardChange,
    contractorChange,
    familyId: familyData.familyId, // 权属人信息
  });
});
// e：form绑定的值  i:当前表单index
const setForm = (e, i) => {
  submitObj[`form${i + 1}`] = e;
};
onMounted(() => {
  init();
});
// 初始化数据
const init = async () => {
  getDictLisType('CARD_TYPE', 'cardType');
  // getOwnerList('ownerType');
  getOtherPatternIds();
  // 未携带id 新增进入
  if (!props.id) return
  // 携带id进入 编辑，详情
  getDetails();
};
// 字典查询
const getDictLisType = async (e, i) => {
  let { list } = await dictListHttp({ dictiEnum: e });
  selectList[i] = list;
};
// 查询 承包方（代表）名称
// const getOwnerList = async e => {
//   let { list } = await queryOwnerShipInfoListtHttp({ flag: false });
//   selectList[e] = list;
// };
//查询其他地块id
const getOtherPatternIds = async () => {
  let data = await getOtherUnderinfoPatternIdsHttp({ contractId: props.id, cerType: 1 });
  patternIds.value = data;
};
// 获取编辑/详情回显的数据
const getDetails = () => {
  infoHttp({ contractId: props.id }).then(res => {
    tableData.value = res.underInfoVos; //地块信息
    ownerTableDataClone.value = res.familyMemberVos; //共有人信息 拷贝数据
    ownerTableData.value = res.familyMemberVos; //共有人信息
    // ownerId.value = res.contractorVo.ownerId;
    for (let key in familyData) {
      familyData[key] = res.contractorVo[key];
    }
    let otherVoStrArr = ['dicConModeName', 'contractingUse', 'cerNumber', 'awardCertArea', 'remark']; //需要单独处理的值
    let otherVoArr = {};
    otherVoStrArr.forEach(item => {
      otherVoArr[item] = res[item];
    });
    nextTick(() => {
      submitObj.form1.setValues(res.employerVo);
      submitObj.form2.setValues(Object.assign({}, res.contractorVo, otherVoArr));
    });
  });
};

// 判断是否添加地块信息
const verificationTable = () => {
  if (tableData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '请添加地块信息',
      type: 'warning'
    });
    return false;
  }
  return true;
};
// 判断是否添加共有人信息
const verifiOwnerTable = () => {
  if (ownerTableData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '请添加共有人信息',
      type: 'warning'
    });
    return false;
  }
  return true;
};
// 总面积等于 地块信息的总面积
const verifiAwardCertArea = () => {
  let allCertArea = submitObj.form2.getValues().awardCertArea; //获取输入的当前总面积
  let sum = 0;
  tableData.value.map(item => (sum += parseFloat(item.landArea)));
  if (parseFloat(allCertArea) !== sum) {
    ElNotification({
      title: '提示',
      message: '总面积等于地块总面积',
      type: 'warning'
    });
    return false;
  }
  return true;
};
//处理数据
const getData = async type => {
  let { isValid } = type == 'ts' ? { isValid: true } : await formValidate('validate');
  if (type == 'ts' || (isValid && verificationTable() && verifiAwardCertArea())) {
    // let fromData = await formValidate()
    let memberIds = ownerTableData.value.map(item => item.id);
    const { dicConModeName, contractingUse, cerNumber, awardCertArea, remark } = submitObj.form2.getValues();
    const otherVoArr = {
      dicConModeName,
      contractingUse,
      cerNumber,
      awardCertArea,
      remark
    };
    let obj = {
      id: props.id || '',
      contractorRequest: Object.assign({}, submitObj.form2.getValues(), familyData, {}),
      ...otherVoArr,
      employerRequest: submitObj.form1.getValues(),
      underInfos: tableData.value,
      memberIds,
      cerType: 1,
      dicConModeCode: 1
    };
    return obj;
  } else {
    return false;
  }
};
// 保存数据 提交接口
const saveDate = async type => {
  let data = await getData(type);
  if (!data) return;
  await newHttp(data).then(res => {
    if (res.dataId) {
      su.value.show();
    }
  });

  await nextTick();
  if (!props.id) return Promise.reject();
  // if (type == 'ts') {
  //   ElNotification({
  //     title: '暂存成功',
  //     type: 'success'
  //   });
  // }
  return Promise.resolve({});
};
// 表单的统一验证
const formValidate = async e => {
  let isValid = {};
  if (e) {
    await Promise.all(Object.values(submitObj).map(item => item.validate()))
      .then(res => {
        if (res.every(item => item.isValid)) {
          isValid = { isValid: true };
        } else {
          isValid = { isValid: false };
        }
      })
      .catch(err => {
        isValid = { isValid: false };
      });
    return isValid;
  } else {
    //多个表单getValues() 值
    let valuesList = Object.assign({}, submitObj.form1.getValues(), submitObj.form2.getValues());
    console.log(valuesList, 'valuesList----');
    return valuesList;
  }
};
//提交按钮
const submit = () => {
  return saveDate();
};
defineExpose({
  submit
  // ts
});
</script>

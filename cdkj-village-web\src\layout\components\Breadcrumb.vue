<template>
  <div class="funi-layout__breadcrumb">
    <el-breadcrumb :separator-icon="ArrowRightBold" class="flex items-center wh-full pl-[20px]">
      <TransitionGroup appear enter-active-class="animate__animated animate__fadeInRight">
        <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="item.id">
          {{ item.name || '' }}
        </el-breadcrumb-item>
      </TransitionGroup>
    </el-breadcrumb>
    <div class="funi-actions"></div>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ArrowRightBold } from '@element-plus/icons-vue';

const { currentRoute } = useRouter();
const breadcrumbList = ref([]);

watch(
  currentRoute,
  newRoute => {
    const { menus, breadcrumb } = newRoute.meta || {};
    if (!!breadcrumb && !!breadcrumb.length) {
      breadcrumbList.value = breadcrumb.map(item => ({
        id: $utils.guid(),
        name: item
      }));
    } else {
      const [menuId] = menus || [];
      // const activeMenu = permissionStore.menuGroupById[menuId] || {};
      // const parentMenu = permissionStore.menuGroupById[activeMenu.pid] || {};
      // breadcrumbList.value = [parentMenu, activeMenu].filter(menu => menu.pid !== '1' && !!menu.id);
    }
  },
  { immediate: true }
);
</script>
<style lang="less" scoped>
.funi-layout__breadcrumb {
  border-bottom: 1px solid #ebedf2;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  height: 48px;

  .funi-actions {
    display: flex;
    align-items: stretch;
    padding-right: 20px;
  }
}
</style>

<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-02-14 12:19:08
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-04-24 14:13:47
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/groupTitle.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--项目分组标题-->
<!--输入: title分组标题 -->
<template>
  <div class="group-title">
    <div v-if="isShowTitle" class="group-title-content">
      <div class="title"> <span v-if="isSymbol" class="asterisk">*</span> {{ title }}</div>
      <div v-if="isShowDesc" class="group-title-desc" :style="descStyle">
        {{ desc }}
      </div>
      <slot name="slotIcon"></slot>
    </div>
    <div v-else class="flexible"></div>
    <div class="extends">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FuniFormGroupTitle',
  props: {
    title: {
      type: String,
      default: ''
    },
    isShowTitle: {
      type: Boolean,
      default: true
    },
    isSymbol:  {
      type: Boolean,
      default: false
    },
    desc: {
      type: String,
      default: ''
    },
    descStyle: {
      type: Object,
      default: () => ({
        color: 'red'
      })
    },
    isShowDesc: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped lang="less">
.group-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
  margin-top: 15px;
  & &-content {
    display: flex;
    align-items: center;
    .group-title-desc {
      margin-left: 5px;
    }
  }
}
.asterisk{
  color: red;
}
.flexible {
  width: 1px;
}
.extends {
  display: flex;
  align-items: center;
}

.title {
  color: var(--el-color-primary);
  font-size: 16px;
  padding-left: 10px;
  position: relative;
}
.title:after {
  position: absolute;
  content: '';
  width: 4px;
  height: 20px;
  background: var(--el-color-primary);
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
</style>

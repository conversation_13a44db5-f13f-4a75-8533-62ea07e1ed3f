package cn.fight.village.domain.file.service.impl;


import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.file.entity.BusinessFile;
import cn.fight.village.domain.file.repository.BusinessFileRepository;
import cn.fight.village.domain.file.service.BusinessFileService;
import cn.fight.village.domain.file.vo.BusinessFileVo;
import cn.fight.village.domain.user.entity.User;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

/**
 * 业务文件信息服务层
 */
@Service
public class BusinessFileServiceImpl implements BusinessFileService {

    @Resource
    private BusinessFileRepository businessFileRepository;

    @Value("${business.file-store-path}")
    private String fileStorePath;

    @Override
    public JsonResult uploadFile(User user, MultipartFile file) throws IOException {
        if (file == null)
            return JsonResult.failMessage("获取文件信息失败");

        //生成文件信息
        String storeId = IdUtil.simpleUUID(); //文件保存ID

        String filePath = fileStorePath + storeId;
        BusinessFile uploader = new BusinessFile();
        uploader.setStoreId(storeId);
        uploader.setFileName(file.getOriginalFilename());
        uploader.setFileType(file.getContentType());
        uploader.setPath(filePath);
        uploader.manageCreateInfo(user);

        //保存文件到文件仓库
        File newFile = new File(filePath);
        file.transferTo(newFile);

        //保存文件信息
        if (businessFileRepository.insert(uploader) != 1) {
            //todo:文件删除
            throw new BusinessException("文件上传失败");
        }

        BusinessFileVo result = new BusinessFileVo();
        result.setStoreId(storeId);
        result.setPath(filePath);
        result.setUuid(uploader.getUuid());
        return JsonResult.valueOfObject(result);
    }

    @Override
    public void downloadFile(String fileStoreId, HttpServletResponse response) {
        if (StrUtil.isEmpty(fileStoreId))
            throw new BusinessException("文件存储ID不能为空");

        BusinessFile businessFile = this.getBusinessFileByStoreId(fileStoreId);
        if (businessFile == null)
            throw new BusinessException("文件不存在或者已被删除");

        //获取文件
        ServletOutputStream outputStream = null;
        BufferedOutputStream bos = null;
        BufferedInputStream bis = null;
        try {
            String filePath = this.fileStorePath + fileStoreId;
            String fileName = businessFile.getFileName();

            FileInputStream fileInputStream = new FileInputStream(filePath);
            //设置请求头
            response.reset();
            response.setContentType(businessFile.getFileType() + ";charset=utf-8");
            response.setContentType("application/octet-stream;charset=utf-8");
            response.setHeader("content-disposition",
                    "attachment;fileName="+ URLEncoder.encode(fileName,"utf-8")
                            + ";filename*=utf-8''" + URLEncoder.encode(fileName, "utf-8"));

            outputStream = response.getOutputStream();
            bis = new BufferedInputStream(fileInputStream);
            bos = new BufferedOutputStream(outputStream);

            byte[] b = new byte[1024];
            int length = 0;
            while ((length = bis.read(b)) != -1){
                bos.write(b,0,length);
            }
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (bis != null)
                    bis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

            try {
                if (bos != null)
                    bos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

            try {
                if(outputStream != null)
                 outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    //根据文件存储ID获取文件信息
    private BusinessFile getBusinessFileByStoreId(String storeId) {
        return businessFileRepository.selectOne(new LambdaQueryWrapper<BusinessFile>().eq(BusinessFile::getStoreId,storeId)
                .eq(BusinessFile::getDeleted,User.IS_NOT_DELETED));
    }
}

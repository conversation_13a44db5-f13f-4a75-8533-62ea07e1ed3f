<template>
  <div>
    <funi-detail
      :bizName="bizName"
      :showHead="true"
      :steps="steps"
      :detailHeadOption="detailHeadOption"
      @clickBtnEvent="headBtnClick"
    />
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { BaseDetails } from './components';
const route = useRoute();
const detail = ref({});
// let familyInfoVoData = ref({});
const bizName = ref(route.query.bizName);
const id = ref(route.query.id);
// 头部数据
const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    hideStatusBar: false,
    statusName: '户数据状态',
    status: '有效',
    serialName: '户编号',
    no: route.query.no || '--',
    hideStatusName: true
  };
  return obj;
});

const steps = computed(() => {
  let arr = [
    {
      title: '承包地信息',
      preservable: false,
      type: BaseDetails,
      props: {
        id: id.value
      },
      on: {
        clickBtnEvent: v => {
          console.log(1111);
        }
      }
    }
  ];
  return arr;
});

const headBtnClick = res => {
  console.log(1111);
};
// //提交按钮
// const submit = () => {
//   console.log(1111);
//   //  return saveDate()
// };
// defineExpose({
//   submit
//   // ts
// });
</script>

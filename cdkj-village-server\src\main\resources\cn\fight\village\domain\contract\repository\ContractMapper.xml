<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.contract.repository.ContractMapper">
    <select id="getList" resultType="cn.fight.village.domain.contract.value.ContractListVo">
        select
            c.uuid,
            c.contract_no contractNo,
            ud.contract_cret_no contractCretNo,
            ud.right_area rightArea,
            ud.usage,
            ud.contract_type contractType,
            up.upper,
            up.upper_name upperName,
            ud.under_name underName,
            ud.under_location underLocation,
            ud.price,
            ud.right_org rightOrg
        from public.rlams_contract c
       /* left join village.rlams_land l on c.uuid = l.contract_id and l.deleted = 0*/
        left join public.rlams_upper up on c.uuid = up.contract_id and up.deleted = 0
        left join public.rlams_under ud on c.uuid = ud.contract_id and ud.deleted = 0
        where c.deleted = 0 and c.type = #{type}
        <if test="upperName != null and upperName != ''">
            and up.upper_name like '%' || #{upperName} || '%'
        </if>
        <if test="contractNo != null and contractNo != ''">
            and c.contract_no = #{contractNo}
        </if>
        <if test="upper != null and upper != ''">
            and up.upper like '%' || #{upper} || '%'
        </if>
        <if test="underName != null and underName != ''">
            and ud.under_name like '%' ||  #{underName} || '%'
        </if>
        <if test="contractType != null and contractType != ''">
            and ud.contract_type = #{contractType}
        </if>
        <if test="rightOrg != null and rightOrg != ''">
            and ud.right_org = #{rightOrg}
        </if>
        order by c.create_time desc
    </select>

    <select id="queryLandByHousehold" resultType="cn.fight.village.domain.contract.value.HouseHoldContractVo">
        select
            c.uuid,
            c.type,
            l.land_no landNo,
            l.land_type landType,
            l.contract,
            l.area,
            l.farming,
            l.transfer,
            u.contract_cret_no contractCretNo
        from public.rlams_contract c
        inner join public.rlams_land l on c.uuid = l.contract_id and l.deleted = 0
        inner join public.rlams_under u on c.uuid = u.contract_id and u.deleted = 0
        where c.household_id = #{householdId} and c.type = #{type} and c.deleted = 0
    </select>

    <select id="getList4Trans" resultType="cn.fight.village.domain.contract.value.ContractListVo">
        select
            c.uuid,
            c.contract_no contractNo,
            c.project,
            p.total_area rightArea,
            p.usage,
            p.party_a upper,
            p.party_a upperName,
            p.party_b underName,
            p.b_location underLocation,
            c.project,
            '土地流转'  contractType,
            case
                when c.signed = '1' then '已完成签约'
                when c.signed is null then '未完成签约'
            end as signed
        from public.rlams_contract c
        inner join public.rlams_land_protocol p on p.contract_id = c.uuid and p.deleted = 0
        where c.deleted = 0 and c.type = #{type}
        <if test="upperName != null and upperName != ''">
            and p.party_a like '%' || #{upperName} || '%'
        </if>
        <if test="contractNo != null and contractNo != ''">
            and c.contract_no = #{contractNo}
        </if>
        <if test="upper != null and upper != ''">
            and p.party_a like '%' || #{upper} || '%'
        </if>
        <if test="underName != null and underName != ''">
            and p.party_b like '%' ||  #{underName} || '%'
        </if>
        <if test="project != null">
            and c.project like '%' || #{project} || '%'
        </if>
        <if test="signed != null and signed == '1'">
            and c.signed = '1'
        </if>
        <if test="signed != null and signed == '2'">
            and c.signed is null
        </if>
        order by c.contract_no desc,c.create_time desc
    </select>
    <select id="selectTransLandNos" resultType="java.lang.String">
        select
            l.land_no
        from public.rlams_contract c
        inner join rlams_land_protocol p on p.contract_id = c.uuid
        inner join rlams_protocol_land l on l.protocol_id = p.uuid
        where c.type = '土地流转' and c.uuid = #{contractId}
    </select>

    <update id="protocolSureSign">
        update public.rlams_contract
            set
        <choose>
            <when  test="signed == 1">
                 signed = '1',
            </when>
            <when test="signed == 2">
                 signed = null,
            </when>
        </choose>
        <if test="userId != null">
            updater_id = #{userId},
        </if>
        update_time = now()
        where uuid = #{uuid}
    </update>

</mapper>
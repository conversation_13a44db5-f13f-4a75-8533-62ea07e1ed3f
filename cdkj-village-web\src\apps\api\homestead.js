//宅基地信息

import { ElNotification } from 'element-plus'
/**
 * 获取宅基地列表
 * @param {*} data 
 */
export const homesteadList = (data) => {
    return $http.post("/homestead/list", data)
}
/**
 * 添加宅基地
 * @param {*} data 
 */
export const homesteadAdd = (data) => {
    return $http.post("/homestead/add", data)
}
/**
 * 修改宅基地
 * @param {*} data 
 */
export const homesteadUpdate = (data) => {
    return $http.post("/homestead/update", data)
}
/**
 * 删除宅基地
 * @param {*} data 
 */
export const homesteadRemove = (data) => {
    return $http.post("/homestead/remove", data).then(res => {
        ElNotification({
            title: '删除成功',
            type: 'success',
        });
        return res
    })
}
/**
 * 获取宅基地信息
 * @param {*} data 
 */
export const homesteadInfo = (data) => {
    return $http.fetch("/homestead/info", data)
}
/**
 * 查询户拥有的宅基地
 * @param {*} data 
 */
export const queryByHousehold = (data) => {
    return $http.fetch("/homestead/queryByHousehold", data)
}
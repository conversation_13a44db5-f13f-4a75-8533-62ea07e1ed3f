<template>
  <!-- 集体组织成员管理 -->
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" @headBtnClick="headBtnClick" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed } from 'vue';
import { useColumnsOptions } from './hooks/index.jsx';
import { queryFamilyInfoList, apiUrl, deleteApi, familyInfoListExcelApi } from './api/index';
import { expotrFunction, getNowTime } from '@/apps/lfpra/common/hooks/utils.jsx';
import { useRouter } from 'vue-router';
import Upload from '@/apps/lfpra/common/components/upload/upload.vue';
import { apiUrlCommon } from '@/apps/lfpra/common/hooks/api.js';
import { ElMessage } from 'element-plus';

const router = useRouter();
const listPage = ref(null);
const query = ref();
// 获取列表数据
const lodaData = async (pages, parmas) => {
  query.value = parmas;
  let data = await queryFamilyInfoList({
    ...pages,
    ...parmas
  });
  return data;
};
// 查看详情
const seeDateils = row => {
  router.push({
    name: 'collective_organization_look_member',
    query: {
      title: '人员与户信息详情',
      bizName: '详情',
      type: 'info',
      id: row.id,
      familySn: row.familySn,
      no: row.memberCode,
      tab: ['人员与户信息', row.memberCode, '详情'].join('-')
    }
  });
};
// 删除家庭信息回调
const deleteClick = res => {
  let data = { familyInfoId: res.id };
  deleteApi(data).then(res => {
    listPage.value.reload();
    ElMessage(res.message || '删除成功');
  });
};
// 编辑
const _edit = row => {
  router.push({
    name: 'collective_organization_new_edit',
    query: {
      title: '人员与户信息编辑',
      bizName: '编辑',
      type: 'edit',
      id: row.id,
      no: row.memberCode,
      tab: ['人员与户信息', row.memberCode, '编辑'].join('-')
    }
  });
};

const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        lodaData: lodaData,
        btns: [
          { key: 'add', label: '新增' },
          {
            component: () => (
              <Upload
                dc={apiUrlCommon.downloadImportTemplate}
                dcParams={{ downloadTemplate: 'FAMILY_INFO' }}
                url={apiUrl.importFamilyInfoListExcel}
                callbackFun={importClick}
                templateName="人员与户信息模板"
                title="批量导入"
                type="primary"
              >
                导入
              </Upload>
            )
          },
          { key: 'export', label: '导出' }
        ],
        reloadOnActive: true,
        fixedButtons: true,
        checkOnRowClick: true,
        columns: useColumnsOptions({ seeDateils, deleteClick, _edit })
      }
    }
  ];
});

const headBtnClick = res => {
  switch (res) {
    case 'export':
      deriveClick();
      break;
    case 'add':
      addClick();
      break;
    default:
      break;
  }
};

//新增
const addClick = () => {
  router.push({
    path: 'household/add',
    query: {
      title: '人员与户信息新增',
      bizName: '新建',
      type: 'add',
      tab: '人员与户信息-新增'
    }
  });
};
// 导入
const importClick = () => {};
// 导出
const deriveClick = () => {
  expotrFunction({
    url: apiUrl.queryFamilyInfoListExport,
    params: query.value,
    FileName: '人员与户信息管理' + getNowTime()
  });
};
</script>

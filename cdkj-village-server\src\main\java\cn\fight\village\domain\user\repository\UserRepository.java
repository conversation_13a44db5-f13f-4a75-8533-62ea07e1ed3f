package cn.fight.village.domain.user.repository;

import cn.fight.village.domain.user.entity.User;
import cn.fight.village.domain.user.request.UserQuery;
import cn.fight.village.domain.user.vo.UserVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 系统用户数据层
 */
public interface UserRepository extends BaseMapper<User> {

    int logicDeleteById(String userId);

    List<UserVo> selectUserList(UserQuery userQuery);
}

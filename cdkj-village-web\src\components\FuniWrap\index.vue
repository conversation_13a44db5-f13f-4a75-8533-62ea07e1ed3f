<script lang="jsx">
import { Fragment, h, isVNode, resolveComponent } from 'vue';

export default {
  name: 'FuniWrap',
  props: {
    type: String,
    useWrap: Boolean,
    wrapProps: Object
  },
  setup(props, { slots }) {
    return () => {
      if (props.useWrap) {
        return h(resolveComponent(props.type || 'div'), props.wrapProps, slots);
      } else {
        return <Fragment>{!!slots.default ? slots.default() : null}</Fragment>;
      }
    };
  }
};
</script>

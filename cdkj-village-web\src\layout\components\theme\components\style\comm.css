.ww-theme__menu,
.ww-theme__table,
.ww-theme__form,
.ww-theme__color {
    display: flex;
    gap: 16px;
}

.ww-theme__menu__item,
.ww-theme__form__item,
.ww-theme__table__item {
    width: max-content;
    border-radius: 8px;
    overflow: hidden;
    line-height: 0;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
    transition: all .2s;
    position: relative;
}



.ww-theme__menu__item:hover,
.ww-theme__form__item:hover,
.ww-theme__table__item:hover {
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.4);
    transition: all .2s;
}

.ww-theme__color {
    flex-wrap: wrap;
}

.ww-theme__color__item {
    width: 30px;
    height: 30px;
    cursor: pointer;
    border-radius: 50%;

}

.title-name {
    display: inline-block;
    margin-top: 10px;
    width: 100%;
    text-align: center;
    color: #606266;
    font-size: 12px;
}

.selected::before {
    display: inline-block;
    width: 20px;
    height: 20px;
    content: '';
    background-image: url('./../svg/selected.png');
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
    top: calc((100% - 20px) / 2);
    left: calc((100% - 20px) / 2)
}
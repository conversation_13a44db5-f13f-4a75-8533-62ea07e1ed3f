<template>
  <LoginLayout>
    <funiForm ref="loginForm" :schema="schema" :status-icon="false" :hide-required-asterisk="true" @keyup.enter="login">
    </funiForm>
    <el-button type="primary" style="width: 100%" size="large" :loading="loading" class="login_btn" @click="login"
      >登录</el-button
    >
  </LoginLayout>
</template>

<script setup lang="tsx">
import LoginLayout from '@/apps/components/loginLayout/index.vue';
import { logStore } from '@/layout/components/Header/ToolBarRight/UserCenter/store/logStore.js';
import { ref, reactive,onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { loginApi } from '@/apps/api/login.js';
const route = useRouter();
const loading = ref(false);
const loginForm = ref();

const store = new logStore()
/** 表单配置 */
const schema = reactive([
  {
    prop: 'account',
    component: 'el-input',
    labelHidden: true,
    rules: [{ required: true, message: '请输入账号或手机号' }],
    props: {
      placeholder: '请输入账号或手机号',
      prefixIcon: 'User',
      clearable: true,
      size: 'large'
    }
  },
  {
    prop: 'password',
    component: 'el-input',
    labelHidden: true,
    rules: [{ required: true, message: '请输入登录密码' }],
    props: {
      placeholder: '请输入登录密码',
      prefixIcon: 'Lock',
      type: 'password',
      clearable: true,
      size: 'large',
      showPassword: true
    },
    on: {
      // paste(event) {
      //   event.preventDefault();
      //   return false;
      // },
      // copy(event) {
      //   event.preventDefault();
      //   return false;
      // },
      // cut(event) {
      //   event.preventDefault();
      //   return false;
      // }
    }
  }
]);

onMounted(()=>{
  loginForm.value.setValues({
    account:"",
    password:""
  })
})

/**
 * 登录
 */
function login() {
  loginForm.value.validate().then(({ isValid, values }) => {
    if (isValid) {
      loginApi(values).then(res => {
        sessionStorage.setItem("token",res)
        store.updateUserName(values.account)
        route.push({path:'/system'});
      });
    }
  });
}
</script>

<style lang="scss" scoped>
.other_btn_box {
  display: flex;
  justify-content: space-between;
}

:deep(.el-radio-button__inner) {
  width: 100%;
}

:deep(.el-radio-button) {
  width: 50%;
}
</style>

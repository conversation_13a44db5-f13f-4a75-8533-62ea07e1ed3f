package cn.fight.village.aop;

import cn.fight.village.domain.common.service.CommonService;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求异常日志记录
 */
@Slf4j
@Aspect
@Component
public class RequestExceptionAspect implements Ordered {

    @Override
    public int getOrder() {
        return 1;
    }

    @Resource
    private CommonService commonService;

    //切点
    @Pointcut("execution(public * cn.fight.village.domain.*.api.*.*(..))")
    public void point(){}

    //记录错误日志
    @AfterThrowing(throwing = "ex",pointcut = "point()")
    public void doThrowingLogBack(JoinPoint joinPoint,Throwable ex) {
        try {
            String userId = "testUserId-008";

            //获取当前请求对象
            String requestURL = null;
            String requestParam = null;
            String requestMethod = null;

            //获取方法相关内容
            Signature signature = joinPoint.getSignature();
            MethodSignature methodSignature = (MethodSignature) signature;
            Method method = methodSignature.getMethod();
            String methodName = method.getName();
            String exMessage = ex.getMessage();
            String exceptionName = ex.getClass().getName();

            ServletRequestAttributes attributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                requestURL = request.getRequestURI();
                requestMethod = request.getMethod();
                request.getMethod();

                //获取请求参数
                String[] parameterNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
                Object[] paramValues = joinPoint.getArgs();
                if (ArrayUtil.isNotEmpty(parameterNames) && ArrayUtil.isNotEmpty(paramValues)) {
                    requestParam = JSON.toJSONString(assembleParameter(parameterNames, paramValues));
                }
            }

        } catch (Exception e) {
            log.error("插入请求异常日志记录失败,失败记录插入失败{}",e.getMessage());
        }
    }

    //拼接参数名与参数值
    private Map<String, Object> assembleParameter(String[] parameterNames, Object[] parameterValues) {
        Map<String, Object> parameterNameAndValues = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            parameterNameAndValues.put(parameterNames[i], parameterValues[i]);
        }
        return parameterNameAndValues;
    }
}

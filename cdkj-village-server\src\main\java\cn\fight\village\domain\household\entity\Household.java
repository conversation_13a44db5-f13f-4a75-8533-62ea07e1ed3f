package cn.fight.village.domain.household.entity;

import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 户信息表
 *
 */
@TableName("public.rlams_household")
public class Household extends BaseEntity {
    //户主编号
    private String householderId;

    //户编号
    private String householdCode;

    //位置
    private String location ;

    //是否贫困户
    private String poor;

    //是否低保户
    private String lower ;

    //备注
    private String backup;

    public String getHouseholderId() {
        return householderId;
    }

    public void setHouseholderId(String householderId) {
        this.householderId = householderId;
    }

    public String getHouseholdCode() {
        return householdCode;
    }

    public void setHouseholdCode(String householdCode) {
        this.householdCode = householdCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPoor() {
        return poor;
    }

    public void setPoor(String poor) {
        this.poor = poor;
    }

    public String getLower() {
        return lower;
    }

    public void setLower(String lower) {
        this.lower = lower;
    }

    public String getBackup() {
        return backup;
    }

    public void setBackup(String backup) {
        this.backup = backup;
    }
}

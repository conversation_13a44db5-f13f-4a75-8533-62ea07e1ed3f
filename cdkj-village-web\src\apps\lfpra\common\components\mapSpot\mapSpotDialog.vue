<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" title="关联图斑">
      <polygonMap @exportMap="exportMap" :layerType="dialogData.layerType" :isEdit="dialogData.isEdit" :id="dialogData.id" idNum="2"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :disabled="selection.length == 0" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">

import { ref, computed, nextTick, unref,reactive, onMounted } from 'vue';
import polygonMap from '@/apps/lfpra/common/components/polygonMap/index.vue'

const dialogData = reactive({
  layerType:{},
  isEdit: true,
  id:''
});
// 表单查询
const dialogVisible = ref(false); // 控制模态框显示隐藏
const selection = ref([]);
const loading = ref(false);

const emit = defineEmits(['exportObject']);

//显示dailog框
const show = async (layerType,isEdit,id) => {
  console.log(layerType,'layerType-----');
  selection.value = [];
  dialogData.layerType = layerType
  dialogData.isEdit = isEdit
  dialogData.id = id
  console.log(dialogData.id,'dialogData.id-----');
  dialogVisible.value = true;
  await nextTick();

};
// 选择的图斑数据
const exportMap = (e)=>{
  selection.value = e
}
//  确认按钮
const confirmFunc = () => {
  console.log(selection.value,'selection----21');
  emit('exportObject', unref(selection));
  cancelClick();
};
//  取消按钮
const cancelClick = () => {
  dialogVisible.value = false;
  selection.value = [];
};
// 更新图斑
// const updetaMap = (e)=>{
//   console.log('更新图斑-----');
//   polygonMapModel.value.initMap(e)
// }
defineExpose({
  show,
  // updetaMap
});
</script>
<style scoped>

</style>

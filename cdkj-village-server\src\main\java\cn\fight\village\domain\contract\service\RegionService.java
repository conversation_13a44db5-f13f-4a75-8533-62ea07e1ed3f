package cn.fight.village.domain.contract.service;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.entity.Region;
import cn.fight.village.domain.contract.repository.RegionMapper;
import cn.fight.village.domain.contract.repository.UserRegionMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class RegionService {

    @Resource
    private RegionMapper regionMapper;

    @Resource
    private UserRegionMapper userRegionMapper;

    public JsonResult getRegions(String regionCode) {
        if (StringUtils.isEmpty(regionCode)) {
            regionCode = "0"; //根区域，省
        }

        Region result = regionMapper.selectByCode(regionCode);
        return JsonResult.valueOfObject(result);
    }

    public void getRegion(Region region) {
        //获取子区域列表
        List<Region> childes = regionMapper.selectByParent(region.getCode());
        if (!CollectionUtils.isEmpty(childes)) {
            for (Region childe : childes) {
                this.getRegion(childe);
            }
        }

        region.setChildes(childes);
    }
}

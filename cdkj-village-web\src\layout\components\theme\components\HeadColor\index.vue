<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-12-27 14:31:10
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-08 18:06:40
 * @FilePath: /ww-cloud-web-gsbms/src/layout/components/theme/components/HeadColor/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div>
    <ThemeTitle>导航风格</ThemeTitle>
    <div class="ww-theme__menu">
      <div v-for="item in group">
        <div
          class="ww-theme__menu__item ww-theme__cursor"
          :class="{
            selected: modelValue == item.value
          }"
          @click="check(item.value)"
        >
          <component :is="item.components" :name="item.value"></component>
        </div>
        <span class="title-name">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="jsx">
import { ref } from 'vue';
import headColor from './../svg/headColor.svg.vue';
import ThemeTitle from './../Title/index.vue';
import useThemeConfigStore from './../../hooks/setTheme.js';
const themeConfigStore = useThemeConfigStore();

const group = ref([
  {
    components: headColor,
    value: 'black',
    name: '深色顶栏'
  },
  {
    components: headColor,
    value: 'white',
    name: '浅色顶栏'
  },
  {
    components: headColor,
    value: 'blue',
    name: '蓝色顶栏'
  }
]);
const props = defineProps({
  modelValue: {
    type: String,
    default: 'black'
  }
});
const emit = defineEmits(['update:modelValue']);

const check = type => {
  emit('update:modelValue', type);
  themeConfigStore.setHeadColor(type, true);
};
</script>
<style scoped>
@import url('./../style/comm.css');
</style>

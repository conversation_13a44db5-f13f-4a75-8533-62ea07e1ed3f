package cn.fight.village.domain.user.service;


import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.user.entity.User;
import cn.fight.village.domain.user.request.LoginRequest;
import cn.fight.village.domain.user.request.UserQuery;
import cn.fight.village.domain.user.request.UserRequest;

import javax.servlet.http.HttpServletResponse;

/**
 * 系统用户服务层
 */
public interface UserService {

    /**
     * 用户登录
     * @param request
     * @return
     */
    JsonResult userLogin(LoginRequest request);

    /**
     * 创建用户
     * @param user
     * @param userRequest
     * @return
     */
    JsonResult addUser(User user, UserRequest userRequest);

    /**
     * 删除用户
     * @param userId
     * @return
     */
    JsonResult removeUser(User user,String userId);

    /**
     * 用户列表
     * @param userQuery
     * @return
     */
    JsonResult getUserList(UserQuery userQuery);

    /**
     * 用户信息
     * @param userId
     * @return
     */
    JsonResult getUserInfo(String userId);

    /**
     * 修改用户
     * @param user
     * @param userRequest
     * @return
     */
    JsonResult updateUser(User user, UserRequest userRequest);

    /**
     * 获取验证码图片
     *
     * @param response
     * @return
     */
    JsonResult getCaptchaImage(HttpServletResponse response);
}

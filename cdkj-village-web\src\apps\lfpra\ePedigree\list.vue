<!-- 电子档案管理 -->
<template>
  <div>
    <div class="form-search flex-box">
      <div class="form-search-content flex-box">
        <el-input v-model="fileName" placeholder="请输入文件名" />
        <el-button type="primary" class="search-btn" @click="handleSearch('search')">查询</el-button>
        <el-button @click="handleSearch('reset')">重置</el-button>
      </div>
      <div class="btns flex-box">
        <el-button type="primary" v-for="item in btnsList" :key="item.key" @click="item.btnFanc">{{
          item.name
        }}</el-button>
      </div>
    </div>
    <div class="arrange-content flex-box ">
      <span>电子档案管理</span>
      <el-icon size="30" v-if="activeTab" @click="handleTab"><Menu /></el-icon>
      <funi-svg name="stripe" v-else style="width: 30px; height: 30px" @click="handleTab" />
    </div>
    <!-- <funi-list-page-v2 ref="listPage"  :isShowSearch="false" :cardTab="useCardTab"/> -->

    <funi-curd-v2
      v-if="activeTab"
      ref="listPage"
      :lodaData="lodaData"
      :columns="columns"
      :loading="loading"
      :stripe="false"
      border
    />
    <archivesInfo :btns="fileObj.btns" :treeDataList="fileObj.treeDataList" :headBtnClick="headTabBtnClick" v-else />
    <!-- 关联户口 -->
    <ChooseCollection ref="chooseModal" @exportObject="setCollection" />
    <!-- 新建文件夹名/重命名 -->
    <AddFolder ref="addFolderModal" :title="addDioLog.title" @handleConfirm="handleConfirm" />
    <!-- 上传文件 -->
    <Upload
      ref="uploadModal"
      :url="apiUrl.importSiteInfo"
      :onlyUpload="true"
      :callbackFun="completeImportFunc"
      title="导入"
      type="primary"
    />
    <!-- 上传文件夹显示进度 -->
    <progressDailog ref="progressModal" :callbackFun="completeImportFunc"/>
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, reactive, ref } from 'vue';
import { userTableColumns, useBtnsConfig } from './hooks/index.jsx';
import { apiUrl, queryListHttp, deleteHttp, newHttp, relevanceOrCancelHolderHttp } from './hooks/api.js';
import { expotrFunction } from '@/apps/lfpra/common/hooks/utils.jsx';
import { useRouter } from 'vue-router';
import { ElNotification } from 'element-plus';
import archivesInfo from '@/apps/lfpra/common/components/archivesFileView';
import ChooseCollection from '@/apps/lfpra/common/components/ownerInfo/chooseCollection.vue';
import AddFolder from './component/addFolder.vue';
import Upload from './component/upload/upload.vue';
import progressDailog from './component/progressDailog/index.vue';
const router = useRouter();
const listPage = ref();
const query = ref();
const chooseModal = ref(null); //关联户口弹窗
const chooseData = ref([]);
const addFolderModal = ref(null);
const uploadModal = ref(null); //文件上传弹窗
const progressModal = ref(null) //文件夹上传显示进度
const fileName = ref(''); //查询参数
const pagesObj = ref();
const fileInfoId = ref(''); //当前点击行的id
const btnsList = reactive([
  //按钮
  { key: 'addFile', name: '新建文件夹', btnFanc: () => addFunc() },
  { key: 'upLodeFolder', name: '上传文件夹', btnFanc: () => folderFunc() },
  { key: 'upLodeFile', name: '上传文件', btnFanc: () => importFunc() },
  { key: 'exportFile', name: '下载', btnFanc: () => exportFunc() }
]);
const activeTab = ref(true);
const loading = ref(false);
const tableData = ref([]);
const fileObj = reactive({
  btns: [
    { key: 'RENAME', label: '重命名' },
    { key: 'ASSOCIA', label: '关联户主' },
    { key: 'DOW', label: '下载' },
    { key: 'ADD', label: '新建文件夹' },
    { key: 'FOLDER', label: '上传文件夹' },
    { key: 'FILE', label: '上传文件' },
    { key: 'UNFOLLOW', label: '取消关联' },
    { key: 'DEL', label: '删除' }
  ],
  treeDataList: []
});
//新建文件夹名/重命名
const addDioLog = reactive({
  title: ''
});
// 获取列表数据
const lodaData = async (pages, parmas) => {
  loading.value = true;
  pagesObj.value = pages;
  let data = await queryListHttp({
    ...pages,
    fileName: fileName.value
  });

  loading.value = false;
  fileObj.treeDataList = data.list;
  return data;
};

onMounted(() => {});
const columns = computed(() => {
  return userTableColumns({
    renameFunc,
    associationFunc,
    unfollowFunc,
    addRowFunc,
    folderRowFunc,
    importRowFunc,
    delFunc,
    exportRowFunc
  });
});

//查询按钮
const handleSearch = e => {
  if (e !== 'search') {
    //重置
    fileName.value = '';
  }
  resetReload(e);
};
// 重置查询
const resetSearch = () => {
  let pagesParams = {
    pageIndex: 1,
    pageNo: 1,
    pageSize: 10,
    flag:false
  };
  lodaData(pagesParams);
};
// 刷新页面数据 视图  e：列表刷新页数
const resetReload = (e) => {
  if (activeTab.value) {
    //列表刷新
    if(e){
      listPage.value && listPage.value.reload({ resetPage: true });
    }else{
      listPage.value && listPage.value.reload({ resetPage: false });
    }
  } else {
    //宫格刷新
    resetSearch();
  }
};
//切换排列方式
const handleTab = () => {
  activeTab.value = !activeTab.value;
  resetReload();
};
//四宫格排列 文件操作事件
const headTabBtnClick = (res, val) => {
  console.log(res, val, 'val');
  switch (val) {
    case 'RENAME':
      renameFunc(res);
      break;
    case 'ASSOCIA':
      associationFunc(res);
      break;
    case 'DOW':
      exportRowFunc(res);
      break;
    case 'ADD':
      addRowFunc(res);
      break;
    case 'FOLDER':
      folderRowFunc(res);
      break;
    case 'FILE':
      importRowFunc(res);
      break;
    case 'UNFOLLOW':
      unfollowFunc(res);
      break;
    case 'DEL':
      delFunc(res);
      break;
  }
};
// 重命名
const renameFunc = row => {
  addDioLog.title = '重命名';
  addFolderModal.value.show(row.fileName, row.id);
};
// 关联户主
const associationFunc = row => {
  console.log(row,'row------');
  let list = [{ familySn: row.familySn }];
  fileInfoId.value = row.id;
  chooseModal.value.show(list, 'familySn');
};
// 下载
const exportRowFunc = row => {
  let fileNameStr = row.fileName.indexOf('.') !== -1 ? row.fileName.split('.')[0] : row.fileName;
  let fileTypeStr = row.fileName.indexOf('.') !== -1 ? row.fileName.split('.')[1] : 'zip';
  expotrFunction({
    url: apiUrl.queryExport,
    params: { fileInfoId: row.id },
    FileName: fileNameStr,
    FileType: fileTypeStr
  });
};
// 删除
const delFunc = async row => {
  await deleteHttp({ fileInfoId: row.id });
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  resetReload();
};
//列表中新建子文件夹
const addRowFunc = async row => {
  addDioLog.title = '新建文件夹';
  addFolderModal.value.show('', '', row.id);
};
//列表中上传文件
const importRowFunc = async row => {
  uploadModal.value.show(row.id);

};
//列表中上传文件夹
const folderRowFunc = async row => {
  folderFunc(row.id)
};
//新建文件夹
const addFunc = () => {
  addDioLog.title = '新建文件夹';
  addFolderModal.value.show();
};
//上传文件夹
const folderFunc = (id) => {
  const input = document.createElement('input');
  input.type = 'file';
  input.setAttribute('allowdirs', 'true');
  input.setAttribute('directory', 'true');
  input.setAttribute('webkitdirectory', 'true');
  input.multiple = true;
  document.querySelector('body').appendChild(input);
  // todo 这里增加了input标签，可以给它删掉
  input.click();

  input.onchange = async function (e) {
    const formData = new FormData();
    const file = e.target['files'];
    for (let i = 0; i < file.length; i++) {
      formData.append('file', file[i], file[i].webkitRelativePath);
    }
    progressModal.value.show()
    await $http.upload(`${apiUrl.importSiteInfo}?pid=${id || ''}&uuid=${sessionStorage.getItem('c_id')}`, formData).then(res=>{
      progressModal.value.hide()
      if (res && res.fileName) {
        ElNotification({
          title: '成功',
          message: `${res.fileName}上传成功`,
          type: 'success'
        });
      } else {
        ElNotification({
          title: '成功',
          message: '导入成功',
          type: 'success'
        });
      }
    }).catch(err=>{
      progressModal.value.hide()
    })

    document.querySelector('body').removeChild(input);
    resetReload();
  };

};
//上传文件夹
const importFunc = () => {
  uploadModal.value.show();
};

// 取消关联
const unfollowFunc = async row => {
  await relevanceOrCancelHolderHttp({ fileInfoId: row.id, familySn: '' });
  ElNotification({
    title: '取消关联成功',
    type: 'success'
  });
  resetReload();
};
// 下载
const exportFunc = () => {
  expotrFunction({
    url: apiUrl.queryExport,
    params: { flag: false, fileName: fileName.value, ...pagesObj.value },
    FileName: '电子档案',
    FileType: 'zip'
  });
};
// 选择后的关联表格数据
const setCollection = async e => {
  console.log(fileInfoId.value,'fileInfoId.value-----');
  await relevanceOrCancelHolderHttp({ familySn: e[0].familySn, fileInfoId: fileInfoId.value });
  ElNotification({
    title: '关联成功',
    type: 'success'
  });
  resetReload();
};
//新建文件夹名 确认按钮
const handleConfirm = async e => {
  await newHttp({ fileName: e.fileName, id: e.id, parentId: e.parentId });
  ElNotification({
    title: '操作成功',
    type: 'success'
  });
  resetReload();
};
// 上传文件结束
const completeImportFunc = () => {
  resetReload();
};

</script>
<style lang="scss" scoped>
::v-deep.funi_list_page-v2.teleported[data-v-191ac702] {
  height: 80%;
}
.flex-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-search {
  width: 100%;
  padding: 16px 10px;
  margin-bottom: 12px;
  box-sizing: border-box;
  background-color: #fff;
  .search-btn {
    margin: 0 10px;
  }
}
.arrange-content {
  width: 100%;
  padding: 10px 9px;
  box-sizing: border-box;
  background-color: #fff;
}
</style>

package cn.fight.village.domain.doc.entity;

import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 档案
 *
 */
@TableName("public.rlams_document")
public class Document extends BaseEntity {
    //合同ID
    private String contractId;

    //流水号
    private String docNo;

    //合同路径
    private String path;

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}

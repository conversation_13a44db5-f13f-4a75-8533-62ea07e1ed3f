export const apiUrl = {
  queryList: '/lfpra/fileInfoList/queryFileInfoList',
  delete: '/lfpra/fileInfo/delete',
  importSiteInfo:'/lfpra/fileInfo/uploadFiles',//上传文件夹
  queryExport:'/lfpra/fileInfoList/queryFileInfoListExport',//下载导出
  new:'/lfpra/fileInfo/new', //新建文件夹/子文件夹/重命名
  relevanceOrCancelHolder:'/lfpra/fileInfo/relevanceOrCancelHolder',//关联/取消关联户主
  getPercentage:'/lfpra/fileInfo/getPercentage' //获取文件上传百分比
};

// 获取预算项目列表
export const queryListHttp = params => {
  return $http.post(apiUrl.queryList, params);
};

// 删除
export const deleteHttp = params => {
  return $http.fetch(apiUrl.delete, params);
};

export const newHttp = params => {
  return $http.post(apiUrl.new, params);
};
export const relevanceOrCancelHolderHttp = params => {
  return $http.fetch(apiUrl.relevanceOrCancelHolder, params);
};
export const getPercentageHttp = params => {
  return $http.fetch(apiUrl.getPercentage, params);
};


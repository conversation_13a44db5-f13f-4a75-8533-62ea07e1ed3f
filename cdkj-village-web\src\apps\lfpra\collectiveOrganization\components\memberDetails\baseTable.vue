<template>
  <div>
    <funi-curd
      ref="FuniCurdRef"
      :columns="cardTab.columns"
      :lodaData="props.lodaData"
      :data="cardTab.dataList"
      :pagination="props.isPagination"
    >
    </funi-curd>
  </div>
</template>

<script setup>
import { ref, watch, reactive } from 'vue';
const props = defineProps({
  detailData: {
    type: Object,
    default: function () {
      return {};
    }
  },
  columusOptions: {
    type: Array,
    default: function () {
      return [];
    }
  },
  // 是否分页
  isPagination: {
    type: Boolean,
    default: false
  },
  lodaData: {
    type: Function,
    default: null
  }
});
const cardTab = reactive({
  dataList: [],
  columns: []
});

watch(
  () => props.detailData,
  async () => {
    cardTab.columns = props.columusOptions;
    if (Object.keys(props.detailData).length === 0) return;
    cardTab.dataList = props.detailData;
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped></style>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.statistics.repository.StatisticsRepository">

    <select id="contractStatistics" resultType="cn.fight.village.domain.statistics.entity.StatisticsResult">
        select COALESCE(sum(cbd.scmjm),0) contractLandArea,
               count(cbd.gid) contractLandCount,
               count(distinct t.uuid) contractHouseholderCount,
               STRING_AGG(cbd.dkbm, ',') contractLands
        from public.rlams_contract t
            inner join public.rlams_land l on l.contract_id = t.uuid
            inner join public.lyr_cbd cbd on l.land_no = cbd.dkbm
            left join public.rlams_household h on t.household_id = h.uuid
            left join public.rlams_member m on h.householder_id = m.uuid
        where  t.deleted = 0 and t.type = '家庭承包'
        <if test="group != null and group != '全部' and group != ''">
            and cbd.sszb = #{group}
        </if>
        <if test="householder != null and householder !=''">
            and m.name = #{householder}
        </if>
    </select>

    <select id="transStatistics" resultType="cn.fight.village.domain.statistics.entity.StatisticsResult">
        select COALESCE(sum(dk.scmjm),0) transLandArea,
               count(dk.gid) transLandCount,
               count(distinct p.uuid) transLandHousehold,
               STRING_AGG(dk.dkbm, ',') transLands
        from public.rlams_contract t
            inner join public.rlams_land_protocol p on t.uuid = p.contract_id
            inner join public.rlams_protocol_land l on l.protocol_id = p.uuid
            inner join public.lyr_dk dk on dk.dkbm = l.land_no
        where t.type = '土地流转'
        <if test="group != null and group != '全部' and group != ''">
            and dk.sszb = #{group}
        </if>
        <if test="householder != null and householder !=''">
            and p.party_a = #{householder}
        </if>
    </select>

    <select id="getVillageTeams" resultType="java.lang.String">
        select distinct sszb
        from public.lyr_dk d
        where sszb is not null
    </select>
</mapper>
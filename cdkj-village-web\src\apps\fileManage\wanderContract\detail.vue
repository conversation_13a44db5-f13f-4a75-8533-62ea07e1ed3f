<template>
  <funi-teleport to=".layout-content__wrap">
    <div class="boxs">
      <div class="flex flex-1 p-16 bg-white">
        <baseTable
          class="flex-1"
          title="档案信息"
          :columns="columns"
          :lodaData="lodaData"
          :span="8"
        ></baseTable>

        <cardBaseTable
          title="档案影像"
          class="flex-2"
          :data="fileArr"
        ></cardBaseTable>
      </div>
    </div>
  </funi-teleport>
</template>

<script setup lang="jsx">
import { useRoute } from 'vue-router';
import baseTable from '../contract/components/baseTable.vue';
import cardBaseTable from '../contract/components/cardBaseTable.vue';
import { selectByContract } from "@/apps/api/fileManage.js"
import { ref } from 'vue';


const route = useRoute()
const fileArr = ref([])
const lodaData = () => {
  return selectByContract({contractId:route.query.id}).then(res=>{
    fileArr.value = []
    res.documents.forEach(item => {
      fileArr.value.push({
        name:item.split("/").slice(-1)[0],
        url:item
      })
    });
    return res
  })
};

const columns = [
  {
    label: '档案流水号',
    prop: 'docNo'
  },
  {
    label: '合同编号',
    prop: 'contractNo'
  },
  {
    label: '发包方',
    prop: 'upper'
  },
  {
    label: '发包方负责人',
    prop: 'upperName'
  },
  {
    label: '承包方',
    prop: 'under'
  },
  {
    label: '承包方负责人',
    prop: 'underName'
  },
  {
    label: '承包方地址',
    prop: 'location'
  }
];
</script>

<style lang="scss" scoped>
.boxs {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--el-fill-color-blank);
  padding: 0 16px;
}
.bg-white {
  gap: 16px;
  // flex-wrap: wrap;
}
</style>

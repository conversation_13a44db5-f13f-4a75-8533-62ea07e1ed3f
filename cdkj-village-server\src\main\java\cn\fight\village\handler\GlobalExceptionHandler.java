package cn.fight.village.handler;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常类处理
     * @param ex 业务异常对象
     * @return
     */
    @ExceptionHandler
    public JsonResult businessExceptionHandler(BusinessException ex) {
        log.error(ex.getMessage());
        ex.printStackTrace();
        return JsonResult.failMessage(ex.getCode(),ex.getMessage());
    }

    /**
     * 请求方法异常类处理
     * @param ex 业务异常对象
     * @return
     */
    @ExceptionHandler
    public JsonResult businessExceptionHandler(HttpRequestMethodNotSupportedException ex) {
        log.error(ex.getMessage());
        ex.printStackTrace();
        return JsonResult.failMessage("请求方法不支持");
    }

    /**
     * 系统异常类处理
     * @param e 系统异常对象
     * @return
     */
    @ExceptionHandler
    public JsonResult exceptionHandler(Exception e) {
        log.error(e.getMessage());
        e.printStackTrace();
        return JsonResult.defaultError();
    }

    /**
     *异常基类处理
     * @param tr
     * @return
     */
    @ExceptionHandler
    public JsonResult throwableHandler(Throwable tr) {
        log.error(tr.getMessage());
        tr.printStackTrace();
        return JsonResult.defaultError();
    }

    /**
     * 记录错误日志
     * @param e 异常对象
     */
    private void exceptionLogging(Exception e) {

    }
}

<template>
  <el-tooltip content="列配置" placement="top">
    <el-button ref="buttonRef" link>
      <funi-svg name="setting" />
    </el-button>
  </el-tooltip>
  <el-popover
    width="200"
    trigger="click"
    ref="popoverRef"
    virtual-triggering
    placement="bottom-start"
    popper-class="column-setting"
    :virtual-ref="buttonRef"
  >
    <div class="column-setting__header">
      <el-checkbox label="列展示" v-model="checkedAll" :indeterminate="indeterminate" />
    </div>
    <div class="column-setting__list">
      <el-checkbox-group v-model="checkedValues" @change="handleConfirm">
        <el-space direction="vertical" alignment="flex-start" :size="0">
          <el-checkbox v-for="item in checkboxList" :key="item.prop" :label="item.prop" :disabled="item.disabled">
            <span class="inline-block w-[120px] truncate hover:text-text_color_primary">
              {{ item.label }}
            </span>
          </el-checkbox>
        </el-space>
      </el-checkbox-group>
    </div>
    <!-- <div class="column-setting__footer">
      <el-button type="primary" link @click="checkedAll = true">重置</el-button>
      <el-button type="primary" link @click="handleConfirm">确定</el-button>
    </div> -->
  </el-popover>
</template>

<script setup>
import { computed, ref, watch } from 'vue';

const emit = defineEmits(['col-setting', 'update:colSettings']);
const props = defineProps({
  colSettings: { type: Array, default: () => [] }
});

const buttonRef = ref();
const popoverRef = ref();

const checkedValues = ref([]);
const checkboxList = ref([]);

const checkedAll = computed({
  get: () => !!checkedValues.value.length && checkedValues.value.length === checkboxList.value.length,
  set: checked => {
    checkedValues.value = checked ? checkboxList.value.map(item => item.prop) : [];
    handleConfirm();
  }
});

const indeterminate = computed(() => !!checkedValues.value.length && !checkedAll.value);

watch(
  () => props.colSettings,
  () => {
    checkboxList.value = $utils.clone(props.colSettings).filter(i => !!i.prop && !i.fixed);
    checkedValues.value = checkboxList.value.filter(i => i.hidden !== true).map(i => i.prop);
  },
  { immediate: true, deep: true }
);

function handleConfirm() {
  const checkboxListMap = $utils.groupBy(checkboxList.value, 'prop');
  const colSettings = props.colSettings.map(item => {
    const checkboxItem = checkboxListMap[item.prop]?.[0];
    if (!!checkboxItem) {
      checkboxItem.hidden = !checkedValues.value.includes(checkboxItem.prop);
      return checkboxItem;
    }
    return item;
  });
  emit('update:colSettings', colSettings);
  emit('col-setting', colSettings);
  // popoverRef.value.hide();
}
</script>

<style lang="scss">
@use 'element-plus/theme-chalk/src/mixins/mixins' as *;

.column-setting {
  &__header {
    display: flex;
    justify-content: space-between;
    padding: 0px 11px;
    border-bottom: 1px solid #dcdfe6;
  }

  &__list {
    padding: 6px 0 0 11px;
    max-height: 45vh;
    overflow-y: auto;
  }

  &__footer {
    display: flex;
    padding: 12px 11px 0px;
    border-top: 1px solid #dcdfe6;
  }

  @include b(checkbox) {
    @include set-css-var-value('checkbox-border-radius', 3px);
    @include set-css-var-value('checkbox-input-width', 16px);
    @include set-css-var-value('checkbox-input-height', 16px);

    &__inner:after {
      border-width: 2px;
      left: 4.5px;
    }
  }
}
</style>

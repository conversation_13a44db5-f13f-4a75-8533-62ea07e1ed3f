<template>
  <div>
    <funiForm v-bind="formConfig" ref="refForm" />
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { userAdd, userInfo,userUpdate } from '@/apps/api/user.js';
import { useRoute } from 'vue-router';

const props =defineProps({
  isDetail:{}
})
const { userId } = useRoute().query;
if (userId) {
  userInfo({ userId }).then(res => {
    refForm.value.setValues(res);
  });
}
const refForm = ref();
const formConfig = reactive({
  schema: [
    { prop: 'account', label: '账号', component: !props.isDetail?'el-input':null, style: 'width:50%' },
    { prop: 'username', label: '用户名', component: !props.isDetail?'el-input':null, style: 'width:50%' },
    {
      prop: 'password',
      label: '密码',
      component: 'el-input',
      style: 'width:50%',
      hidden:true,
      props: {
        type: 'password',
        clearable: true,
        showPassword: true
      }
    },
    {
      prop: 'userType',
      label: '用户类型',
      component: !props.isDetail?'funi-select':null,
      style: 'width:50%',
      props: {
        placeholder: '请选择',
        options: [
          {
            value: 1,
            label: '管理员'
          },
          {
            value: 2,
            label: '普通用户'
          }
        ]
      }
    }
  ],
  rules: !props.isDetail?{
    account: [{ required: true, message: '请输入', trigger: 'change' }],
    username: [{ required: true, message: '请输入', trigger: 'change' }],
    password: [{ required: true, message: '请输入', trigger: 'change' }],
    userType: [{ required: true, message: '请选择', trigger: 'change' }]
  }:undefined
});

/**
 * 提交
 */
async function submit() {
  let res = await refForm.value.validate();
  if (res.isValid) {
    //编辑
    if(userId){
      userUpdate(res.values)
    }
    //新增
    else{
      await userAdd(res.values);
    }
    return Promise.resolve();
  }
}

defineExpose({
  submit
});
</script>

<style lang="scss" scoped></style>

<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" :title="props.title">
      <funi-form :schema="schema" @get-form="setForm" :rules="[]" :border="false" :col="2"/>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary"  @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref } from 'vue';
import { ElNotification } from 'element-plus';
const props = defineProps({
  title:{type:String,default:''},
})
const emit = defineEmits(['exportObject']);
const dialogVisible = ref(false); // 控制模态框显示隐藏
const form = ref(null);
const  id = ref('')
const parentId = ref('') //新建子文件夹 所需要的父id
const schema = computed(() => {
  return [
  {
      label: '文件夹名称',
      component: 'el-input',
      props: {
        placeholder: '请输入文件夹名称',
        maxlength: 50
      },
      prop: 'fileName'
    },
  ];
});
const setForm = e => (form.value = e);
//显示dailog框

const show = async (e,i,pId) => {
dialogVisible.value = true;
id.value = i || ''
parentId.value = pId || ''
setTimeout(() => {
  if(e){
    form.value.setValues({fileName:e})
  }
}, 0);
await nextTick();
}
//  确认按钮
const confirmFunc = () => {
  let formData = {
    fileName:form.value.getValues().fileName,
    id:id.value,
    parentId:parentId.value
  }
  console.log(formData,'formData----');
  if(formData.fileName){
    emit('handleConfirm',formData);
    cancelClick();
  }else{
    ElNotification({
    title: '请输入',
    type: 'warning'
  });
  }

};
//  取消按钮
const cancelClick = () => {
  dialogVisible.value = false;
};

defineExpose({
  show
});
</script>
<style scoped></style>

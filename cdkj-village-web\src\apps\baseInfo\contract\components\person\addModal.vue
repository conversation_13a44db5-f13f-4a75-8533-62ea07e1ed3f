<template>
    <div>
      <funi-dialog v-model="dialogVisible" size="large" title="关联集体组织成员家庭（使用人）">
        <div>
          <FuniCurd
            ref="funiCurd"
            height="calc(50vh - 40px)"
            :columns="columnsProject"
            :useSearchV2="false"
            :data="tableData"
            :loading="loading"
            @row-click="getData"
            :rowKey="keyType"
            size="small"
          />
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="cancelClick">取消</el-button>
            <el-button type="primary" :disabled="selection.length == 0" @click="confirmFunc"> 确定 </el-button>
          </span>
        </template>
      </funi-dialog>
    </div>
  </template>
  <script setup lang="jsx">
  import { ref, computed, nextTick, unref } from 'vue';
  import { householdMembers } from "@/apps/api/household.js"
  
  const dialogVisible = ref(false); // 控制模态框显示隐藏
  const tableData = ref([])
  const funiCurd = ref(void 0);
  const householdId = ref()
  const selection = ref([]);
  
  const keyType = ref('uuid');
  const loading = ref(false);
  
  // 模态框表格配置
  const columnsProject = computed(() => {
    return [
      {
        type: 'selection',
        width: '55px',
        fixed: 'left'
      },
      {
        label: '姓名',
        prop: 'name'
      },
      {
        label: '性别',
        prop: 'gender'
      },
      {
        label: '证件类型',
        prop: 'idType'
      },
      {
        label: '证件号码',
        prop: 'idCode'
      },
      {
        label: '家庭关系',
        prop: 'relation'
      }
    ];
  });

  const emit = defineEmits(['exportObject']);
  // 选择表格项
  const getData = ({column,row, selection:selcetRows}) => {
    selection.value= selcetRows
  };
  //显示dailog框
  const show = async (id,selectRows = []) => {
    householdId.value = id
    selection.value = selectRows;
    dialogVisible.value = true;
    lodaData()
  };
  //获取列表数据
  const lodaData = async () => {
    loading.value = true;
    let res = await householdMembers({ householdId:householdId.value,exHouseholder:1}).finally(()=>{
      loading.value = false;
    })
    tableData.value =res
    await nextTick()
    selection.value.forEach(item=>{
      funiCurd.value.toggleSelection(item)
    })
  };
  
  //  确认按钮
  const confirmFunc = () => {
    emit('exportObject', unref(selection).map(x=>({
      name:x.name,
      gender:x.gender,
      idType:x.idType,
      idCode:x.idCode,
      relation:x.relation,
    })));
    cancelClick();
  };
  //  取消按钮
  const cancelClick = () => {
    dialogVisible.value = false;
    selection.value = [];
  };
  
  defineExpose({
    show
  });
  </script>
  <style scoped>
  
  </style>
  
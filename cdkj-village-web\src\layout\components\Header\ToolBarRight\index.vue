<template>
  <div class="flex items-stretch">
    <router-view name="extension">
      <template #default="{ Component, route }">
        <component v-if="!!Component" :is="Component" />
      </template>
    </router-view>
    <Shortcuts />
    <UserCenter :option="userMenuOption" :isShowUserInfo="!pubUser"></UserCenter>
  </div>
</template>
<script setup>
import Shortcuts from './Shortcuts.vue';
import UserCenter from './UserCenter/index.vue';

const props = defineProps({
  pubUser: Boolean,
  userMenuOption: { type: Object, default: () => ({}) }
});
</script>
<style lang="scss">
.flex{
  display: flex;
}
</style>

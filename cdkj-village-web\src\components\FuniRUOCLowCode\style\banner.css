.org-banner {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    border: 1px solid var(--funi-ruoc-lc-border-color);
    box-sizing: border-box;
    padding: 5px 0;
}

.banner {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border: 1px solid var(--funi-ruoc-lc-border-color);
    box-sizing: border-box;
}


.oneself-banner {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    border: 1px solid var(--funi-ruoc-lc-border-color);
    box-sizing: border-box;
    padding: 0 5px;
}

.banner :deep(.el-form[validate-status="success"] .el-input__wrapper),
.org-banner :deep(.el-form[validate-status="success"] .el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-input-border-color);
}
<template>
  <div>
    <funiGroupTitle title="户信息"></funiGroupTitle>
    <funiForm v-bind="formConfig" ref="refForm" :col="2" />
    <FamilyMember ref="familyRef" :isDetail="isDetail" :personnelData="personnelData" @dataListCallBack="dataListCallBack" />
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { householdAdd, householdInfo, householdUpdate } from '@/apps/api/household.js';
import { useRoute } from 'vue-router';
import FamilyMember from './familyMember/index.vue';
import { ElNotification } from 'element-plus';

const props = defineProps({
  isDetail: {}
});
const { id } = useRoute().query;
if (id) {
  householdInfo({ uuid: id }).then(res => {
    refForm.value.setValues(res);
    personnelData.value = res.members;
  });
}
const refForm = ref();
const familyRef = ref();
const personnelData = ref([]);
const formConfig = reactive({
  schema: [
    {
      prop: 'householdCode',
      label: '户编号',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      props: { placeholder: '请输入' }
    },
    {
      prop: 'location',
      label: '家庭住址',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      props: { placeholder: '请输入' }
    },
    {
      prop: 'poor',
      label: '是否贫困户',
      component: !props.isDetail ? 'funi-select' : null,
      props: {
        options: [
          {
            label: '是',
            value: '是'
          },
          {
            label: '否',
            value: '否'
          }
        ]
      }
    },
    {
      prop: 'lower',
      label: '是否低保户',
      component: !props.isDetail ? 'funi-select' : null,
      props: {
        options: [
          {
            label: '是',
            value: '是'
          },
          {
            label: '否',
            value: '否'
          }
        ]
      }
    },
    {
      prop: 'backup',
      label: '备注信息',
      component: !props.isDetail ? 'el-input' : null,
      colProps: { span: 24 },
      props: { type: 'textarea', placeholder: '输入内容' }
    }
  ],
  rules: !props.isDetail
    ? {
        householdCode: [{ required: true, message: '请输入', trigger: 'change' }],
        location: [{ required: true, message: '请输入', trigger: 'change' }],
        poor: [{ required: true, message: '请输入', trigger: 'change' }],
        lower: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    : undefined
});

/**
 * 提交
 */
async function submit() {
  let res = await refForm.value.validate();
  if (res.isValid) {
    let personData = await familyRef.value.familyMemberRequests();
    if (!personData.length) {
      ElNotification({
        title: '请添加人员信息',
        type: 'warning'
      });
    } else if (!personData.find(x => x.householder == 1)) {
      ElNotification({
        title: '人员信息缺少户主!',
        type: 'warning'
      });
    } else {
      //编辑
      if (id) {
        await householdUpdate({...res.values,uuid:id});
      }
      //新增
      else {
        await householdAdd({ ...res.values, members: personData });
      }
      return Promise.resolve();
    }
  }
  return Promise.reject();
}

defineExpose({
  submit
});
</script>

<style lang="scss" scoped></style>

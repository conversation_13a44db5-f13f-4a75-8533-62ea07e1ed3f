<template>
  <div class="collect-container" :style="{ display: patternType.length === 0 ? 'none' : 'block' }">
    <el-space direction="vertical" :size="size" style="align-items: flex-start">
      <section v-if="patternType.includes('3')">
        <div>宅基地总数</div>
        <div class="value">{{ props?.geoList?.siteNum || '--' }}</div>
      </section>
      <section v-if="patternType.includes('3')">
        <div>宅基地总面积（㎡）</div>
        <div class="value">{{ props?.geoList?.siteArea || '--' }}</div>
      </section>
      <section v-if="patternType.includes('1')">
        <div>承包地总数</div>
        <div class="value">{{ props?.geoList?.contNum || '--' }}</div>
      </section>
      <section v-if="patternType.includes('1')">
        <div>承包地总面积（亩）</div>
        <div class="value">{{ props?.geoList?.contArea || '--' }}</div>
      </section>
      <section v-if="patternType.includes('2')">
        <div>坑塘总数</div>
        <div class="value">{{ props?.geoList?.swagNum || '--' }}</div>
      </section>
      <section v-if="patternType.includes('2')">
        <div>坑塘总面积（亩）</div>
        <div class="value">{{ props?.geoList?.swagArea || '--' }}</div>
      </section>
    </el-space>
  </div>
</template>
<script setup lang="jsx">
import { ref, watch } from 'vue';
const props = defineProps({
  searchForm: {
    type: Object,
    default: {}
  },
  geoList: {
    type: Object,
    default: {}
  }
});
let size = ref(20);
const patternType = ref(['3', '1', '2']);
watch(
  () => props.searchForm,
  () => {
    patternType.value = props.searchForm;
  },
  {
    deep: true
  }
);
</script>
<style lang="scss" scoped>
.collect-container {
  color: #fff;
  padding: 30px;
  background-color: rgba(0, 0, 0, 0.5);
}
.value {
  font-size: 30px;
  font-weight: bold;
}
</style>

<template>
  <div class="box-conentet">
    <div class="title">
      <div class="icon"></div>
      <div>{{ title }}</div>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: "",
});
</script>

<style lang="scss" scoped>
.box-conentet {
  width: 360px;
  .title {
    height: 30px;
    width: 100%;
    background-image: url(@/assets/mir/box-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-family: YouShe, serif;
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    color: #eff6ff;
    text-shadow: 0 2px 4px rgba(2, 7, 17, 0.8);
    padding-left: 24px;
    position: relative;
    font-family: luo;
    .icon {
      background-image: url(@/assets/mir/box-icon.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: -12px;
      left: 0;
      width: 24px;
      height: 24px;
    }
  }
  .content {
    padding: 20px;
  }
}
</style>

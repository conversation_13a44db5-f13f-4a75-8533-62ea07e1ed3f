package cn.fight.village.domain.contract.entity;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
/**
 *
 * 土地流转协议补充协议
 */
@TableName("public.rlams_land_protocol_supp")
public class TransProtocolSup extends BaseEntity {

    private String contractId;

    @TableField("party_a")
    private String partyA;

    @Sensitive
    @TableField("a_id")
    private String paId;

    @Sensitive
    @TableField("a_phone")
    private String paPhone;

    @Sensitive
    @TableField("a_location")
    private String paLocation;

    @TableField("party_b")
    private String partyB;

    @TableField("b_represe")
    private String pbReprese;

    @Sensitive
    @TableField("b_location")
    private String pbLocation;

    @Sensitive
    @TableField("b_phone")
    private String pbPhone;

    @Sensitive
    @TableField("b_id")
    private String pbId;

    private String signDate;

    private String appointTime;

    private String startTime;

    private String endTime;

    private String location;

    private Double area;

    private String upTime;

    @TableField("a_sign_time")
    private String paSignTime;


    @TableField("b_sign_time")
    private String pbSignTime;

    private String textObject;

    public String getAppointTime() {
        return appointTime;
    }

    public void setAppointTime(String appointTime) {
        this.appointTime = appointTime;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getPartyA() {
        return partyA;
    }

    public void setPartyA(String partyA) {
        this.partyA = partyA;
    }

    public String getPaId() {
        return paId;
    }

    public void setPaId(String paId) {
        this.paId = paId;
    }

    public String getPaPhone() {
        return paPhone;
    }

    public void setPaPhone(String paPhone) {
        this.paPhone = paPhone;
    }

    public String getPaLocation() {
        return paLocation;
    }

    public void setPaLocation(String paLocation) {
        this.paLocation = paLocation;
    }

    public String getPartyB() {
        return partyB;
    }

    public void setPartyB(String partyB) {
        this.partyB = partyB;
    }

    public String getPbReprese() {
        return pbReprese;
    }

    public void setPbReprese(String pbReprese) {
        this.pbReprese = pbReprese;
    }

    public String getPbLocation() {
        return pbLocation;
    }

    public void setPbLocation(String pbLocation) {
        this.pbLocation = pbLocation;
    }

    public String getPbPhone() {
        return pbPhone;
    }

    public void setPbPhone(String pbPhone) {
        this.pbPhone = pbPhone;
    }

    public String getPbId() {
        return pbId;
    }

    public void setPbId(String pbId) {
        this.pbId = pbId;
    }

    public String getSignDate() {
        return signDate;
    }

    public void setSignDate(String signDate) {
        this.signDate = signDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getUpTime() {
        return upTime;
    }

    public void setUpTime(String upTime) {
        this.upTime = upTime;
    }

    public String getPaSignTime() {
        return paSignTime;
    }

    public void setPaSignTime(String paSignTime) {
        this.paSignTime = paSignTime;
    }

    public String getPbSignTime() {
        return pbSignTime;
    }

    public void setPbSignTime(String pbSignTime) {
        this.pbSignTime = pbSignTime;
    }

    public String getTextObject() {
        return textObject;
    }

    public void setTextObject(String textObject) {
        this.textObject = textObject;
    }
}
package cn.fight.village.domain.household.repository;


import cn.fight.village.domain.household.entity.HouseholdMember;
import cn.fight.village.domain.household.vo.HouseMember;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 家庭成员信息dao
 *
 */
public interface HouseholdMemberMapper extends BaseMapper<HouseholdMember> {
    /**
     *根据户ID删除成员信息
     *
     * @param houseId
     * @return
     */
    int deleteByHouseId(@Param("houseId") String houseId, @Param("userId") String userId);

    /**
     * 获取家庭成员信息
     *
     * @param householdId
     * @param exHouseholder
     * @return
     */
    List<HouseMember> selectHomeMembers(@Param("householdId") String householdId, @Param("exHouseholder") Integer exHouseholder);

    /**
     * 根据身份证获取成员
     *
     * @param idCode
     * @return
     */
    HouseMember selectByIdCode(String idCode);
}

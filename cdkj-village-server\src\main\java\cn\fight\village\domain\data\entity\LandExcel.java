package cn.fight.village.domain.data.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import lombok.Data;

@Data
public class LandExcel {
    /****土地**********/
    @ExcelProperty("地块编码")
    private String landNo;

    @ExcelProperty("土地类型")
    private String landType;

    @ExcelProperty("种植情况")
    private String farming;

    @ExcelProperty("是否承包")
    private String contract;

    @ExcelProperty("是否流转")
    private String transfer;

    @ExcelProperty("面积")
    private Double area ;


    /******发包方*********/
    @ExcelProperty("发包方编码")
    private String upperNo;

    @ExcelProperty("组别")
    private String group;

    @ExcelProperty("发包方名称")
    private String upper;

    @ExcelProperty("发包方姓名")
    private String upperName;

    @ExcelProperty("发包方负责人联系电话")
    private String upperPhone;

    @ExcelProperty("发包方证件类型")
    private String upperIdType;

    @ExcelProperty("发包方证件号码")
    private String upperIdNo;


    /**承包方**/
    @ExcelProperty("承包方名称")
    private String under;

    private String  villager = "是";

    @ExcelProperty("承包方证件号")
    private String underIdNo;

    @ExcelProperty("证件类型")
    private String underIdType;

    @ExcelProperty("承包方编码")
    private String underNo;

    @ExcelProperty("确权（合同）总面积（亩）")
    private Double rightArea;

    @ExcelProperty("承包方式")
    private String contractType;

    @ExcelProperty("土地承包用途")
    private String usage;

    @ExcelProperty("承包经营权证书号")
    private String contractCretNo;

    public String toString() {
        return JSON.toJSONString(this);
    }
}


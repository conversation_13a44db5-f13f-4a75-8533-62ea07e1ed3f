<template>
  <div shadow="never" class="card pb-0">
    <funi-form
      ref="formRef"
      v-bind="$attrs"
      :col="colNumber"
      :schema="computedSchema"
      :inline="false"
      @keyup.enter.native="search"
    >
      <template v-for="(_, slot) in $slots" #[slot]="params">
        <slot :name="slot" v-bind="params"></slot>
      </template>
      <template #toolbar="{ item }">
        <div class="w-full flex" :class="toolbarAlignClass">
          <el-button type="primary" @click="search"> 查询</el-button>
          <el-button @click="reset"> 重置</el-button>
          <el-link v-if="item.expandable" type="primary" :underline="false" class="ml-19px" @click="setVisible">
            {{ expand ? '收起' : '高级查询' }}
          </el-link>
        </div>
      </template>
    </funi-form>
  </div>
</template>

<script setup>
import { computed, ref, unref } from 'vue';

defineOptions({
  name: 'FuniSearchForm',
  inheritAttrs: false
});

const props = defineProps({
  schema: { type: Array, default: () => [] },
  colNumber: { type: Number, default: 4 }
  // expandProp: String,
  // toolbarAlign: {
  //   type: String,
  //   default: 'auto',
  //   validator: value => ['left', 'right', 'center', 'auto'].includes(value)
  // }
});

const emit = defineEmits(['search', 'reset']);
const formRef = ref();
const expand = ref(true);
const colNumber = props.colNumber;

/**
 * 1.每行固定4列
 * 2.第一行最后一列固定为操作按钮
 * 3.默认展开高级查询
 */
const computedSchema = computed(() => {
  const schema = $utils.clone(props.schema, true).map((item, index) => ({
    ...item,
    hidden: index > 2 && !unref(expand)
  }));
  const toolbarSchema = {
    prop: 'toolbar',
    labelHidden: true,
    expandable: schema.length > colNumber - 1,
    slots: { default: 'toolbar' }
  };
  schema.splice(colNumber - 1, 0, toolbarSchema);
  return schema;
});

const toolbarAlignClass = computed(() => {
  return 'justify-start';
  // if (['auto', 'left'].includes(props.toolbarAlign)) return 'justify-start';
  // if (props.toolbarAlign === 'center') return 'justify-center';
  // if (props.toolbarAlign === 'right') return 'justify-end';
  // return '';
});

const search = async () => {
  try {
    await unref(formRef).validate();
    emit(
      'search',
      Object.fromEntries(
        Object.entries(unref(formRef).getValues()).map(([key, value]) => [
          key,
          $utils.isString(value) && !!value ? value.trim() : value
        ])
      )
    );
  } catch (error) {}
};

const reset = async () => {
  unref(formRef).resetFields();
  emit('reset');
};

const setVisible = () => {
  expand.value = !unref(expand);
};
</script>

<style lang="less" scoped>
.funi-search-form__card {
  :deep(.el-card__body) {
    padding-bottom: 2px;
  }
}
</style>

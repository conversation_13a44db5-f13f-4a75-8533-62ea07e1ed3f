<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-24 09:38:56
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-02 16:40:48
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCode/dialog/org/org.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div class="org-banner">
    <div class="orgSearch">
      <el-form validate-status="success">
        <el-input v-model="keyword" @input="iptChange" placeholder="请搜索管理主体"></el-input>
      </el-form>
    </div>
    <div class="orgList" data-key="orgBox" v-loading="loading">
      <el-tree-v2
        ref="tree"
        :data="orgList"
        :height="treeHeight"
        :props="defaultProps"
        show-checkbox
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
        :check-on-click-node="true"
        :default-checked-keys="checkedKeys"
        :filter-method="filterNode"
        check-strictly
        @check="nodeClick"
      >
      </el-tree-v2>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, nextTick, watch, onMounted, onUpdated } from 'vue';
import { getOrgTree, getAllNode } from './hooks/orgHooks.js';
const props = defineProps({
  allList: {
    type: Array,
    default: () => []
  },
  valueList: {
    type: Array,
    default: () => []
  },
  propsConfig: Object
});
const emit = defineEmits(['setallList', 'setValue']);
const defaultProps = {
  label: 'orgName'
};
const orgList = ref([]);
const loading = ref(false);
const keyword = ref('');
const searchName = ref('');
const checkedKeys = ref([]);
const tree = ref();
const orgBox = ref();
const treeHeight = ref(0);
let timer = null;
const mode = computed(() => {
  return props?.propsConfig?.mode;
});

const nodeClick = e => {
  if (e.disabled) return false;
  let ids = tree.value.getCheckedKeys();
  let vid = props.valueList.map(item => item.id);
  let list = [];
  if (mode.value !== 'multiple') {
    let id = tree.value.getCurrentKey();
    if (ids.indexOf(id) > -1) {
      tree.value.setCheckedKeys([id]);
      list = [id];
    } else {
      tree.value.setCheckedKeys([]);
    }
  } else {
    for (let i = 0; i < vid.length; i++) {
      let index = props.allList.findIndex(item => item.id === vid[i]);
      if (index > -1) {
        vid.splice(i, 1);
        i--;
      }
    }
    list = [...vid, ...ids];
  }
  emit('setValue', list);
};

const iptChange = e => {
  clearTimeout(timer);
  timer = setTimeout(() => {
    clearTimeout(timer);
    searchName.value = keyword.value;
  }, 600);
};

onMounted(async () => {
  orgList.value = await getOrgTree({ loadingOrg: loading, allowedOrg: props.propsConfig.allowedOrg });
  emit('setallList', getAllNode(orgList.value));
  await nextTick();
  treeHeight.value = document.querySelector('.operation[data-key="operation"]').offsetHeight - 95;
  setChecked();
});

const setChecked = async () => {
  let ids = props.valueList.map(item => item.id);
  checkedKeys.value = ids;
  tree.value && tree.value.setCheckedKeys(ids);
  await nextTick();
  //   if (tree.value) console.log(tree.value.getCurrentKey());
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.orgName.includes(value);
};

watch(searchName, val => {
  tree.value && tree.value.filter(val);
});

watch(
  tree,
  newVal => {
    setChecked();
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.valueList,
  newVal => {
    setChecked();
  },
  {
    deep: true,
    immediate: true
  }
);
</script>
<style scoped>
@import url('./../../style/banner.css');
.orgList {
  width: 100%;
  height: calc(100% - 35px);
  overflow: hidden;
  /* overflow-y: auto; */
}
.orgSearch {
  width: 100%;
  box-sizing: border-box;
  padding: 0 10px;
}

:deep(.el-checkbox) {
  order: 3;
  position: absolute;
  right: 0;
}

.custom-tree-node {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 3px;
}

:deep(.el-tree-node__content) {
  position: relative;
}
:deep(.el-checkbox__inner) {
  border-radius: var(--mode-border-radius);
}
:deep(.el-tree-node[aria-disabled='true'] > .el-tree-node__content) {
  cursor: not-allowed;
  color: var(--el-text-color-disabled);
}
</style>

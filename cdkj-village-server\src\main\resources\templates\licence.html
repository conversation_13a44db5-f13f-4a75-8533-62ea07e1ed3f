<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>激活许可证</title>
</head>
<body>
您的许可证请求码为:
<div th:text="${requestCode}"></div>
<div>请输入您获取到的许可证内容:</div>
<div><textarea id="licenceContent" name="licenceContent" style="width: 80%;height: 15%"></textarea></div>
<p>【操作】：<button type="button" onclick="activeService()">激活服务</button></p>
<p>请将您的许可证请求码告知系统开发者，以获取您的许可证</p>
</body>

<script type="text/javascript">
    var path = window.location.protocol+ "//" +window.location.host + '/village';

    //激活服务
    function activeService() {
        debugger;
        var licenceContent = document.getElementById("licenceContent").value;
        if (licenceContent) {
            var url = path + '/licence/activeLicence';

            //激活请求
            var param = {
                "licenceCode": licenceContent,
            };

            var paramJson = JSON.stringify(param);
            var response = commonPostRequest(url,paramJson);

            response.then((result)=>{
                if (result && result.success) {
                    alert(result.message);
                } else {
                    alert('请求失败');
                }
            });
        } else {
            alert('请输入您的许可证内容');
            return;
        }

        //发送同步POST请求
        async function commonPostRequest(url, argument) {
            const xhr = new XMLHttpRequest();
            xhr.open('post', url, false);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.send(argument);
            return JSON.parse(xhr.responseText);
        }


    }
</script>
</html>
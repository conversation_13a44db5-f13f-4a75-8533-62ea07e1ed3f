package cn.fight.village.domain.file.entity;


import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 业务文件信息
 */
@TableName("_file")
public class BusinessFile extends BaseEntity {
    /**关联业务件ID*/
    private String businessId;

    /**存储ID*/
    private String storeId;

    /**文件名称*/
    private String fileName;

    /**文件类型*/
    private String fileType;

    /**文件路径*/
    private String path;

    public String getBusinessId() {
        return businessId;
    }

    public void setBusiness_id(String business_id) {
        this.businessId = businessId;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}

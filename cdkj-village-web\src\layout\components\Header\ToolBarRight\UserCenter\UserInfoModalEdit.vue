<!--
 * @Author: 古加文 <EMAIL>
 * @Date: 2022-12-07 10:50:40
 * @LastEditors: 古加文 <EMAIL>
 * @LastEditTime: 2023-11-08 11:09:54
 * @FilePath: \funi-paas-cs-web-cli\src\layout\components\Header\ToolBarRight\UserCenter\UserInfoModalEdit.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-dialog v-model="dialogFormVisible" destroy-on-close align-center title="个人信息" width="450px">
      <el-form ref="formRef" :rules="formRules" :model="form" label-position="top" label-width="auto">
        <el-form-item label="用户类型">
          {{ form.userType || '--' }}
        </el-form-item>
        <el-form-item label="用户名">
          {{ form.userName || '--' }}
        </el-form-item>
        <el-form-item v-if="editInfo" prop="nickName" label="昵称">
          <el-input v-model="form.nickName" />
        </el-form-item>
        <el-form-item v-else label="昵称">
          {{ form.nickName || '--' }}
        </el-form-item>
        <el-form-item v-if="editInfo" prop="cellPhone" label="电话">
          <el-input v-model="form.cellPhone" />
        </el-form-item>
        <el-form-item v-else label="电话">
          {{ form.cellPhone || '--' }}
        </el-form-item>
        <el-form-item v-if="editInfo" prop="emailAddress" label="邮箱">
          <el-input v-model="form.emailAddress" />
        </el-form-item>
        <el-form-item v-else label="邮箱">
          {{ form.emailAddress || '--' }}
        </el-form-item>
      </el-form>
      <template #footer>
        <span v-if="editInfo" class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" :loading="loading" @click="editInfoConfirm"> 确定 </el-button>
        </span>
        <span v-else class="dialog-footer">
          <el-button type="primary" @click="editInfo = true">修改信息</el-button>
          <el-button type="primary" @click="changePwd"> 修改密码 </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="pwdVisible"
      destroy-on-close
      align-center
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="修改密码"
      width="350px"
    >
      <el-form ref="pwdFormRef" :model="pwdForm" :rules="pwdRules" label-width="auto">
        <el-form-item prop="oldPassword" label="旧密码">
          <el-input v-model="pwdForm.oldPassword" type="password" />
        </el-form-item>
        <el-form-item prop="newPassword" label="新密码">
          <el-input v-model="pwdForm.newPassword" type="password" />
          <template #error="{ error }">
            <el-tooltip class="box-item" effect="dark" :content="error" placement="bottom">
              <div class="el-form-item__error errorDiv">{{ error }}</div>
            </el-tooltip>
          </template>
        </el-form-item>
        <el-form-item prop="newPassword2" label="确认新密码">
          <el-input v-model="pwdForm.newPassword2" type="password" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            @click="
              pwdVisible = false;
              dialogFormVisible = true;
            "
            >取消</el-button
          >
          <el-button type="primary" :loading="loading" @click="pwdConfirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { logStore } from './store/logStore';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
const store = logStore();

//个人信息弹窗显示
const dialogFormVisible = ref(false);
//密码弹窗显示
const pwdVisible = ref(false);
//是否编辑个人信息
const editInfo = ref(false);
//个人信息表单
const formRef = ref();
const loading = ref(false);
const form = reactive({
  userType: '',
  userName: '',
  nickName: '',
  cellPhone: '',
  emailAddress: ''
});

const validateEmail = (rule, value, callback) => {
  if (!value) {
    callback();
  } else {
    return $utils.Validate.validateEMail(rule, value, callback);
  }
};
const formRules = reactive({
  nickName: [{ required: true, message: '请输入昵称', trigger: 'change' }],
  cellPhone: [{ required: true, validator: $utils.Validate.isiPhoneTwo, trigger: 'change' }],
  emailAddress: [{ validator: validateEmail, trigger: 'change' }]
});
//修改密码表单
const pwdFormRef = ref();
const pwdForm = ref({
  oldPassword: '',
  newPassword: '',
  newPassword2: ''
});
const validatePwd = (rule, value, callback) => {
  if (value && pwdForm.value.newPassword && value !== pwdForm.value.newPassword) {
    callback(new Error('确认新密码与新密码不一致'));
  } else {
    callback();
  }
};
var regExpData = reactive({
  passwordRegular: '',
  passwordRegularTips: ''
});
onMounted(async () => {
  regExpData = await store.getPasswordRegular();
});
//修改密码
const changePwd = () => {
  pwdForm.value = {};
  dialogFormVisible.value = false;
  pwdVisible.value = true;
};

//密码校验
const isPasswd = async (rule, value, callback) => {
  let patrn = new RegExp(regExpData.passwordRegular);
  if (value == '' || value == undefined || value == null || !patrn.test(value)) {
    return callback(new Error(regExpData.passwordRegularTips));
  } else {
    callback();
  }
};

const pwdRules = reactive({
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'change' }],
  newPassword: [{ required: true, validator: isPasswd, trigger: 'change' }],
  newPassword2: [
    { required: true, message: '请输入确认新密码', trigger: 'change' },
    {
      validator: validatePwd,
      trigger: 'blur'
    }
  ]
});

//打开个人信息弹窗
const userInfo = reactive({});
const show = async () => {
  dialogFormVisible.value = true;
  editInfo.value = false;
  const data = await store.getUserInfo();
  Object.assign(form, data);
  Object.assign(userInfo, data);
};

const cancelEdit = () => {
  editInfo.value = false;
  Object.assign(form, userInfo);
};

/**
 * 修改个人信息确认事件
 */
const editInfoConfirm = () => {
  formRef.value.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true;
      await store.updateUserInfo(form).finally(() => {
        loading.value = false;
      });
      store.updateNickName(form.nickName);
      ElMessage({
        message: '修改个人信息成功！',
        type: 'success'
      });
      dialogFormVisible.value = false;
    }
  });
};

/**
 * 修改密码确认事件
 */
const { push } = useRouter();
const pwdConfirm = () => {
  pwdFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true;
      await store.updatePassword(pwdForm.value).finally(() => {
        loading.value = false;
      });
      ElMessage({
        message: '修改密码成功，请重新登录！',
        type: 'success'
      });
      await store.logout();
      push({ path: '/' });
    }
  });
};

defineExpose({
  show
});
</script>

<style lang="less">
.errorDiv {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  width: 100%;
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

import NumberInput from '@/apps/lfpra/common/components/numberInput/numberInput.vue';
import LetterNumInput from '@/apps/lfpra/common/components/letterNumInput/index.vue';
//select 自定义组件
const getSelectComponent = e => {
  return (
    <el-select
      style="width:100%"
      onChange={i => {
        e.onChange ? e.onChange(i) : {};
      }}
    >
      {e.list.map(item => (
        <el-option key={item[e.value]} label={item[e.label]} value={item[e.value]} />
      ))}
    </el-select>
  );
};
// 表单配置
export const useSchema = params => {
  let arr = [
    {
      label: '宅基地编号',
      component: () => <LetterNumInput />,
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'siteCode'
    },
    {
      label: '宅基地面积（㎡）',
      component: () => <NumberInput />,
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'siteArea'
    },
    {
      label: '是否取证',
      component: () =>
        getSelectComponent({
          list: params.obtainType,
          value: 'value',
          label: 'label',
          onChange: params.contractChange
        }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'isForensics'
    },
    {
      label: '土地类型',
      component: () => getSelectComponent({ list: params.LandType, value: 'code', label: 'name' }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'dicLandCode'
    },
    {
      label: '证书编号',
      component: 'el-input',
      hidden: !params.isContract,
      props: {
        placeholder: '请输入',
        maxlength: 200,
        type: 'textarea'
      },
      colProps: {
        span: 24
      },
      prop: 'credentialCode'
    },
    {
      label: '未取证原因',
      component: 'el-input',
      hidden: !!params.isContract,
      props: {
        placeholder: '请输入',
        maxlength: 200,
        type: 'textarea'
      },
      colProps: {
        span: 24
      },
      prop: 'noForensicsCause'
    },
    {
      label: '土地四至-东至',
      component: 'el-input',
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'landEast'
    },
    {
      label: '土地四至-西至',
      component: 'el-input',
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'landWest'
    },
    {
      label: '土地四至-南至',
      component: 'el-input',
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'landSouth'
    },
    {
      label: '土地四至-北至',
      component: 'el-input',
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'landNorth'
    },
    {
      label: '房屋楼层',
      component: () => getSelectComponent({ list: params.floorType, value: 'code', label: 'name' }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'dicFloorCode'
    },
    {
      label: '房屋结构',
      component: () => getSelectComponent({ list: params.structureType, value: 'code', label: 'name' }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'dicHouseStructCode'
    },
    {
      label: '使用情况',
      component: () => getSelectComponent({ list: params.usageType, value: 'code', label: 'name' }),
      props: {
        placeholder: '请输入',
        maxlength: 50
      },
      prop: 'dicUseCondCode'
    },
    {
      label: '备注',
      component: 'el-input',
      hidden: false,
      props: {
        placeholder: '请输入',
        maxlength: 200,
        type: 'textarea'
      },
      colProps: {
        span: 24
      },
      prop: 'remark'
    }
  ];
  let detail = [];
  // 查看详情 文本显示
  if (!params.isEdit) {
    arr.map(item => {
      let obj = {
        label: item.label,
        prop: item.prop,
        colProps: item.colProps,
        hidden: item.hidden || false
      };
      //用于多选框 赋值
      if (item.prop == 'isForensics') {
        obj.prop = 'isForensicsName';
      }
      // if(item.prop == 'credentialCode'){
      //   obj.hidden =!params.isContract
      // }
      // if(item.prop == 'noForensicsCause'){
      //   obj.hidden = !!params.isContract
      // }
      if (['dicLandCode', 'dicFloorCode', 'dicHouseStructCode', 'dicUseCondCode'].includes(item.prop)) {
        obj.prop = item.prop.replace('Code', 'Name');
      }
      detail.push(obj);
    });
  }
  return params.isEdit ? arr : detail;
};
// 表单验证
export const useRules = params => {
  if (params.isEdit) {
    return {
      siteCode: [{ required: true, message: '必填', trigger: 'change' }], // 宅基地编号
      siteArea: [{ required: true, message: '必填', trigger: 'change' }], // 宅基地面积（㎡）
      isForensics: [{ required: true, message: '必填', trigger: 'change' }], //
      dicLandCode: [{ required: true, message: '必填', trigger: 'change' }],
      credentialCode: [{ required: true, message: '必填', trigger: 'change' }],
      noForensicsCause: [{ required: true, message: '必填', trigger: 'change' }],
      landEast: [{ required: true, message: '必填', trigger: 'change' }],
      landWest: [{ required: true, message: '必填', trigger: 'change' }],
      landSouth: [{ required: true, message: '必填', trigger: 'change' }],
      landNorth: [{ required: true, message: '必填', trigger: 'change' }],
      dicFloorCode: [{ required: true, message: '必填', trigger: 'change' }],
      dicHouseStructCode: [{ required: true, message: '必填', trigger: 'change' }],
      dicUseCondCode: [{ required: true, message: '必填', trigger: 'change' }]
    };
  } else {
    return {};
  }
};

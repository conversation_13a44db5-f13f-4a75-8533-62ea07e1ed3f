package cn.fight.village.domain.household.vo;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseValue;

/**
 * 家庭户信息列表对象
 *
 */
public class HouseholdListVo extends BaseValue {
    //户编号
    private String householdCode;

    //家庭住址
    private String location ;

    //是否贫困户
    private String poor;

    //是否低保户
    private String lower;

    //户主姓名
    private String  name;

    //性别
    private String  gender;

    //证件类型
    private String  idType;

    //证件号码
    @Sensitive
    private String  idCode;

    //联系电话
    @Sensitive
    private String  phone;

    public String getHouseholdCode() {
        return householdCode;
    }

    public void setHouseholdCode(String householdCode) {
        this.householdCode = householdCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPoor() {
        return poor;
    }

    public void setPoor(String poor) {
        this.poor = poor;
    }

    public String getLower() {
        return lower;
    }

    public void setLower(String lower) {
        this.lower = lower;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}

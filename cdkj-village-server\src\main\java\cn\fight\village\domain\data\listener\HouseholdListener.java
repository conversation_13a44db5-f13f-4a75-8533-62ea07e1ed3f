package cn.fight.village.domain.data.listener;

import cn.fight.village.domain.data.entity.HouseholdExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class HouseholdListener implements ReadListener<HouseholdExcel> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 3500;

    /**
     * 缓存的数据
     */
    private List<HouseholdExcel> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    private List<HouseholdExcel> resultList = null;

    public List<HouseholdExcel> getResultList() {
        return resultList;
    }

    public  HouseholdListener() {
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(HouseholdExcel data, AnalysisContext context) {
        cachedDataList.add(data);
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！");
        this.resultList = cachedDataList;
        //this.cachedDataList.clear();
    }
}

package cn.fight.village.domain.household.repository;

import cn.fight.village.domain.common.entity.BaseQuery;
import cn.fight.village.domain.household.entity.Household;
import cn.fight.village.domain.household.query.HouseholdListQuery;
import cn.fight.village.domain.household.vo.HouseMember;
import cn.fight.village.domain.household.vo.HouseholdListVo;
import cn.fight.village.domain.household.vo.HouseholderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * 家庭户信息添加
 *
 */
public interface HouseholdMapper extends BaseMapper<Household> {

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    List<HouseholdListVo> selectPageList(HouseholdListQuery query);

    /**
     * 家庭及成员列表查询
     *
     * @param query
     * @return
     */
    List<HouseMember> queryHoseMember(BaseQuery query);

    /**
     * 获取家庭成员信息
     *
     * @param houseId
     * @return
     */
    @Deprecated
    List<HouseMember> queryHoseMemberByHouseId(String houseId);

    /**
     * 根据户主身份证获取户信息
     *
     * @param
     * @return
     */
    List<Household> getHouseHoldByHouseholder(Map<String, Object> params);

    /**
     * 根据户主与户信息
     * @param name
     * @return
     */
    List<HouseholderVo> getHouseHoldByHouseholderName(String name);


    /**
     * 根据土地编号获取承包家庭信息
     * @param landNo
     */
    Map<String,Object> selectHouseHoldByLandNo(String landNo);
}

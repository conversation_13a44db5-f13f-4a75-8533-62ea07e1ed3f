/*
 * @Author: fengyi 8919340+<PERSON><EMAIL>
 * @Date: 2023-08-15 14:53:40
 * @LastEditors: fengyi <EMAIL>
 * @LastEditTime: 2023-08-24 17:12:49
 * @FilePath: \funi-bpaas-as-ui\src\hooks\useTheme.js
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import { reactive, computed } from 'vue'
import { useH5Store } from '@/stores/useH5TestStore'
export const useTheme = () => {
    const h5Store = useH5Store()
    const dark = {
        // 在styles中的h5配置的全局变量
        funiBg: '#272727',
        funiWhite: '#000',
        funiBlack: '#ccc',
        fuiBorderColor: '#e4e4e4',
        funiActiveColor: '#bbb',
        // cell盒子的样式
        cellTextColor: '#ccc',
        cellBackground: '#000',
        cellBorderColor: '#666',
        cellActiveColor: '#272727',
        // 输入框的样式
        fieldInputTextColor: '#ccc',
        fieldLabelColor: '#ccc',
        fieldPlaceholderTextColor: '#666',
        // 复选框的样式
        checkboxLabelColor: '#ccc',
        checkboxBorderColor: '#ccc',
        // 单选框的样式
        radioBorderColor: '#ccc',
        radioLabelColor: '#ccc',
        // 进度条的样式
        stepsBackground: '#000',
        stepCircleColor: '#ccc',
        stepLineColor: '#666',
        // 导航栏的样式
        navBarBackground: '#000',
        navBarTitleTextColor: '#ccc',
        navBarIconColor: '#fff',
        // 选择框样式
        pickerBackground: '#333',
        pickerMaskColor: '#000',
        pickerOptionTextColor: '#ccc',
        pickerConfirmActionColor: '#fff',
        // 选择器组样式
        pickerGroupBackground: '#333',
        tabsNavBackground: '#333',
        tabTextColor: '#ccc',
        tabActiveTextColor: '#fff',
        // 日历样式
        calendarBackground: '#333',
        calendarDayDisabledColor: '#ccccccae',
        //popup样式
        popupBackground: '#333',
        // 多级样式
        cascaderTabColor: '#ccc',
        // 按钮样式
        buttonPlainBackground: '#ddd',
        //switch样式
        switchBackground: '#ccc',
        switchNodeBackground: '#eee',
        // 滑块样式
        sliderButtonBackground: '#ccc',
        sliderInactiveBackground: '#aaa',
        // step样式
        stepperButtonDisabledColor: '#BBB',
        stepperButtonDisabledIconColor: '#000',
        stepperBackground: '#CCC'
    }
    const light = {}
    const theme = computed(() => {
        return h5Store.dark ? dark : light
    })
    return theme
}
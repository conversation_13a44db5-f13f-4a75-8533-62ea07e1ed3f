<template>
  <div class="funi-layout-logo">
    <funi-image class="funi-layout-logo__img" :src="logoPath" />
    <!-- <el-divider direction="vertical" /> -->
    <div class="funi-layout-logo__label">
      {{ systemName }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  systemName: String,
  logoPath: { type: String, default: '/logo.png' },
  redirection: String
});

function handleClick() {
  sessionStorage.clear();
  window.location.href = props.redirection;
}
</script>
<style lang="scss" scoped src="@/layout/styles/logo.scss"></style>
<style scoped>
.funi-layout-logo__label{
  font-size: 18px;
}
</style>

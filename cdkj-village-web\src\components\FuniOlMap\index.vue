<template>
  <div class="map-container-map-box" :style="{ height: mapHeight ? mapHeight + 'px' : '100%' }" v-loading="_loading">
    <div class="map-container-map" :id="mapId"></div>
    <!-- <div class="layer_box" v-if="showLayerTree" :style="{ width: layerBoxWidth + 'px' }">
      <el-menu>
        <layerTreeCom :layerTree="layerTree" @layerChange="layerChange"></layerTreeCom>
      </el-menu>
    </div> -->
    <div
      class="legend_box"
      v-show="getLegendList()"
      :style="{ width: legendBoxWidth ? legendBoxWidth + 'px' : 'auto' }"
    >
      <template v-for="item in getLegendList()" :key="item.layer">
        <template v-if="item._legendColorType == 'array'" v-for="legend in item.legendColor">
          <div class="legend-item" v-if="legendShowFunc ? legendShowFunc(legend, legendList) : true">
            <div
              class="color-bg"
              :style="{ background: legend.legendColor || legend.fill }"
              @click="showStyleModal(item, legend)"
              v-if="legend.legendColor || legend.fill"
            ></div>
            <div class="legend_name" :title="legendBoxWidth ? legend.name : ''">{{ legend.name }}</div>
          </div>
        </template>
        <div class="legend-item" v-else-if="legendShowFunc ? legendShowFunc(item, legendList) : true">
          <div class="color-bg" :style="{ background: item.legendColor }" @click="showStyleModal(item)"></div>
          <div class="legend_name" :title="legendBoxWidth ? item.name : ''">{{ item.name }}</div>
        </div>
      </template>
    </div>
    <div class="draw_box" @click.stop>
      <template v-if="_showDraw">
        <div v-show="!['add', 'split', 'merge'].includes(currentTool)">
          <div>
            <div
              class="draw-item"
              v-if="_drawTool.includes('add')"
              :class="{ disabledClick: ['choose', 'del'].includes(currentTool) }"
              @click="
                ['choose', 'del'].includes(currentTool)
                  ? ''
                  : drawToolClick({ type: 'add', message: '新增图斑，双击绘制完成' })
              "
            >
              <img src="./icon/add.png" />
              <div>新增</div>
            </div>
            <!-- <div
            class="draw-item"
            v-if="_drawTool.includes('add')"
            :class="{ disabledClick: currentTool == 'choose' }"
            @click="drawToolClick({ type: 'choose', message: '选中图斑' })"
          >
            <img src="./icon/add.png" />
            <div>选中</div>
          </div> -->
            <div
              class="draw-item"
              v-if="_drawTool.includes('merge')"
              :class="{ disabledClick: currentTool != 'choose' }"
              @click="currentTool != 'choose' ? '' : drawToolClick({ type: 'merge', message: '合并图斑' })"
            >
              <img src="./icon/merge.png" />
              <div>合并</div>
            </div>
            <div
              class="draw-item"
              v-if="_drawTool.includes('split')"
              :class="{ disabledClick: currentTool != 'choose' }"
              @click="currentTool != 'choose' ? '' : drawToolClick({ type: 'split', message: '拆分图斑' })"
            >
              <img src="./icon/split.png" />
              <div>拆分</div>
            </div>
            <div
              class="draw-item"
              v-if="_drawTool.includes('del')"
              :class="{ disabledClick: currentTool != 'choose' }"
              @click="currentTool != 'choose' ? '' : drawToolClick({ type: 'del', message: '已确定待删除图斑' })"
            >
              <img src="./icon/delete.png" />
              <div>删除</div>
            </div>
          </div>
        </div>
        <div v-if="currentTool == 'add'">
          <div class="draw-item" :class="{ disabledClick: optStatus == '' }" @click="drawDelete">
            <img src="./icon/delete.png" />
            <div>删除</div>
          </div>
          <div class="draw-item" :class="{ disabledClick: optStatus != 'start' }" @click="drawRevoke">
            <img src="./icon/revoke.png" />
            <div>撤销</div>
          </div>
          <!-- <div class="draw-item" :class="{ disabledClick: optStatus == '' }" @click="drawOutput">
          <img src="./icon/ok.png" />
          <div>完成</div>
        </div> -->
        </div>
        <div v-if="['merge'].includes(currentTool)">
          <div class="draw-item" :class="{ disabledClick: optStatus != 'end' }" @click="drawRevoke">
            <img src="./icon/revoke.png" />
            <div>撤销</div>
          </div>
          <!-- <div class="draw-item" :class="{ disabledClick: optStatus != 'merge' }" @click="drawOutput">
          <img src="./icon/ok.png" />
          <div>完成</div>
        </div> -->
        </div>
        <div v-if="['split'].includes(currentTool)">
          <div class="draw-item" :class="{ disabledClick: optStatus != 'start' }" @click="drawRevoke">
            <img src="./icon/revoke.png" />
            <div>撤销</div>
          </div>
          <!-- <div class="draw-item" :class="{ disabledClick: optStatus == '' }" @click="drawOutput">
          <img src="./icon/ok.png" />
          <div>完成</div>
        </div> -->
        </div>
      </template>
      <div
        class="draw-item"
        ref="baseMapChangeBoxRef"
        @click="isShowBaseMapTool = !isShowBaseMapTool"
        v-if="baseMapChangeTool"
      >
        <img src="./icon/layer.png" />
        <div>图层</div>
        <div class="map_layer_box" @click.stop v-show="isShowBaseMapTool" :style="getBaseMapChangeBoxStyle()">
          <div class="flex_box">
            <text>底图</text>
            <el-icon @click="isShowBaseMapTool = false"><Close /></el-icon>
          </div>
          <div class="line"></div>
          <div style="display: flex; gap: 10p; font-size: 12px">
            <div style="text-align: center; margin-right: 10px">
              <img
                class="base_map_icon"
                src="./icon/map_w.jpg"
                :class="{ active: _baseMapLayer == 'tdtImgLayer' }"
                @click="changeBaseLayer('tdtImgLayer')"
              />
              天地图影像
            </div>
            <div style="text-align: center">
              <img
                class="base_map_icon"
                src="./icon/map_vec.jpg"
                :class="{ active: _baseMapLayer == 'tdtVecLayer' }"
                @click="changeBaseLayer('tdtVecLayer')"
              />
              天地图矢量
            </div>
          </div>
          <!-- <div class="line"></div>
          <div class="flex_box">
            <div class="">地名</div>
            <el-checkbox v-model="isShowBaseMapLayerText" @change="changeBaseLayerText" />
          </div> -->
          <div class="line"></div>
          <div class="">图层</div>
          <div class="layer_box" v-if="showLayerTree">
            <el-menu>
              <layerTreeCom :layerTree="layerTree" @layerChange="layerChange"></layerTreeCom>
            </el-menu>
          </div>
        </div>
      </div>
    </div>
    <div ref="overlayRef" class="ol-popup-box">
      <slot name="overlay" v-bind="currentOverlayData">
        <div class="ol-popup">
          <a id="popup-closer" class="ol-popup-closer" @click.stop="closeModal"></a>
          <div id="aaaa">
            <component :is="contentRender()" v-if="isShowModal && modalContentRender" />
            <slot name="overlayContent" v-bind="currentOverlayData"></slot>
          </div>
        </div>
      </slot>
    </div>
    <div class="draw_message_box" v-show="drawMessage">
      <span style="margin-right: 3px">{{ drawMessage }}</span>
      <el-icon
        :size="16"
        @click="closeMessage"
        v-if="_showDraw && !(hightLightVectorLayerEdit && currentTool == 'choose')"
        ><CloseBold
      /></el-icon>
    </div>
    <legendStyleChangeModal
      ref="legendStyleChangeModalRef"
      v-model="isShowChangeStyleModal"
      :onConfirm="onChangeStyleConfirm"
    ></legendStyleChangeModal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick, isVNode, getCurrentInstance } from "vue";
import commonObj from "./common/common";
import getBaseMapFunc from "./common/baseMap";
import layerTreeCom from "./components/layerTree.vue";
import legendStyleChangeModal from "./components/legendStyleChangeModal.vue";
import { useAppStore } from "@/stores/useAppStore";
//地图依赖----------start----------
import { Map, View } from "ol";
import { WMTS, OSM, Vector as VectorSource, XYZ, ImageWMS } from "ol/source";
import TileWMTS from "ol/tilegrid/WMTS";
import { Tile as TileLayer, Vector as VectorLayer, Image as ImageLayer } from "ol/layer";
import Feature from "ol/Feature.js";
import { Style, Stroke, Fill, Circle, Icon, Text } from "ol/style";
import { LineString, Polygon, Point } from "ol/geom";
import Overlay from "ol/Overlay.js";
import { getWidth, getTopLeft } from "ol/extent";
import {
  Draw,
  Modify,
  Snap,
  Select,
  DragRotate,
  DoubleClickZoom,
  DragZoom,
  DragPan,
  PinchZoom,
  PinchRotate,
  KeyboardZoom,
  KeyboardPan,
  MouseWheelZoom,
} from "ol/interaction";
import { click } from "ol/events/condition";
import { GeoJSON, WFS } from "ol/format";
import { equalTo, and, or, like, intersects } from "ol/format/filter";
import { get, Projection } from "ol/proj";
import proj4 from "proj4";
import { register } from "ol/proj/proj4";
import * as turf from "@turf/turf";
import WKT from "terraformer-wkt-parser";
import { getArea, getLength } from "ol/sphere.js";
import { getVectorContext } from "ol/render.js";
import { unByKey } from "ol/Observable.js";
import { ElMessage } from "element-plus";
import * as x2js from "x2js";
import default_marker from "./icon/default_marker.png";
//地图依赖-----------end-----------
const _x2js = x2js.default || x2js;
const appStore = useAppStore();
//注册 EPSG:4490 坐标系
proj4.defs("EPSG:4490", "+proj=longlat +ellps=GRS80 +no_defs +type=crs");
register(proj4);
//emits
const emit = defineEmits(["mapLoadend", "layerClick"]);

/**
 * 参数
 */
const props = defineProps({
  /**
   * 绘制编辑值
   */
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  /**
   * 绘制输出type枚举
   */
  drawTypeEnum: {
    type: Object,
    default: () => ({
      add: "VECTOR_ADD", //新增
      split: "VECTOR_SPLIT", //分割
      merge: "VECTOR_MERGE", //混合
      del: "VECTOR_DELETE", //删除
    }),
  },
  /**
   *  绘制图斑数据id对应字段名，拆分合并删除时，需要此id
   */
  oldSns: {
    default: "contract_sn",
  },
  /**
   * 经纬度点集合数据类型，默认为jsonString2字符串,二维
   */
  geomDataType: {
    type: String,
    default: "jsonString2", //jsonString2(二维：农业生产用地需要),jsonString(原始为三维),json,wkt
  },
  /**
   * 绘制编辑key映射，不同系统可能不同字段，预留
   */
  modeValueMap: {
    default: () => ({
      oldSns: "oldSns",
      features: "farlandInfoRequests",
      geom: "geom",
      area: "farlandAcreage",
    }),
  },
  /**
   * 地图组件高度，不配置为100%
   */
  mapHeight: {
    type: Number,
  },
  /**
   * 地图交互
   */
  interaction: {
    type: Array,
    default: () => [
      "DragRotate",
      // 'DoubleClickZoom',
      "DragZoom",
      "DragPan",
      "PinchZoom",
      "PinchRotate",
      "KeyboardZoom",
      "KeyboardPan",
      "MouseWheelZoom",
    ],
  },
  /**
   * 地图id,需视图同步则必填
   */
  mapId: {
    type: String,
    default: () => {
      return window.$utils.guid();
    },
  },
  /**
   * 视图配置参数
   */
  viewOptions: {
    type: Object,
    default: () => ({
      maxZoom: 22,
    }),
  },
  /**
   * 视图同步id，需同步则必填
   */
  viewSyncMapId: {
    type: String,
  },
  /**
   * 坐标系
   */
  projection: {
    type: String,
    default: "EPSG:4490", //EPSG:4490 EPSG:4326  EPSG:3857
  },
  /* 基础地图,默认天地图影像 */
  baseMapLayer: {
    type: String,
    default: "tdtImgLayer", //目前3个值，默认天地图影像：tdtImgLayer:天地图影像，tdtVecLayer：天地图矢量，为空：无底图
  },
  /**
   * 是否显示天地图底图标注，默认不显示
   */
  showBaseMapLayerText: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否显示底图切换工具
   */
  baseMapChangeTool: {
    type: Boolean,
    default: true,
  },
  /**
   * 地图中心点，默认成都
   */
  mapCenter: {
    type: Array,
    default: () => [104.28146584, 30.61937578],
  },
  /**
   * 地图初始缩放级别，默认12
   */
  zoom: {
    type: Number,
    default: 15,
  },
  /**
   * 图层最大显示级别
   */
  layerMaxZoom: {
    type: Number,
  },
  /**
   * 图层服务
   */
  layers: {
    type: Array,
    default: () => [],
  },
  /**
   * 图层树宽度
   */
  layerBoxWidth: {
    type: Number,
    default: 200,
  },
  /**
   * 图例容器宽度
   */
  legendBoxWidth: {},
  /**
   * 是否显示图层树
   */
  showLayerTree: {
    type: Boolean,
    default: true,
  },
  /**
   * 图层服务api,通过接口方式获取
   */
  layersRequest: {
    type: Object,
    default: () => ({
      api: "",
      params: {},
      method: "post",
    }),
  },
  /**
   * 图层树请求回调方法
   */
  layersRequestCallback: {},
  /**
   * 是否显示弹框
   */
  showOverlay: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否显示图斑绘制功能
   */
  showDraw: {
    type: Boolean,
    default: false,
  },
  /**
   * 绘制工具
   */
  drawTool: {
    type: Array,
    default: () => ["add", "merge", "split", "del"],
  },
  /**
   * 绘制样式
   */
  drawStyle: {
    type: Object,
    default: () => ({
      fillColor: "#03a9f478",
      borderColor: "#80e3f6",
      width: 3,
    }),
  },
  /**
   * 选中样式
   */
  selectedStyle: {
    type: Object,
    default: () => ({
      fillColor: "#03a9f478",
      borderColor: "red",
      width: 3,
    }),
  },
  /**
   * 是否允许选中效果
   */
  enableSelect: {
    type: Boolean,
    default: true,
  },
  /**
   * 图斑最大拆分数
   */
  maxSplit: {
    type: Number,
    default: 2,
  },
  /**
   * 分割样式
   */
  drawSplitStyle: {
    type: Object,
    default: () => ({}),
  },
  /**
   * 边界轮廓数据，优先接口
   */
  boundAreaList: {
    type: Array,
    default: () => [],
  },
  /**
   * 绘制时限制边界
   */
  boundAreaLimit: {
    type: Boolean,
    default: false,
  },
  /**
   * 边界轮廓接口，
   */
  boundAreaRequest: {
    type: Object,
    default: () => ({
      api: "",
      params: {},
      method: "post",
    }),
  },
  /**
   * 边界轮廓样式
   */
  boundAreaStyle: {
    type: Object,
    default: () => ({
      fillColor: "transparent",
      borderColor: "red",
      width: 3,
    }),
  },
  /**
   * 区域弹框选择数据
   */
  regionModalList: {
    type: Array,
    default: () => [],
  },
  /**
   * 图层权限请求头
   */
  layerAuthHeader: {
    type: Object,
  },
  /**
   * 高亮样式
   */
  highLightStyle: {
    type: Object,
    default: () => ({
      fillColor: "rgba(255, 0, 0, 0.2)",
      borderColor: "red",
      width: 3,
    }),
  },
  /**
   * wfs查询服务地址
   */
  wfsServer: {
    type: String,
    default: "/geoserver/village/wfs",
  },
  /**
   * 自适应参数
   */
  fitParams: {
    type: Object,
    default: () => ({
      duration: 500,
      padding: [80, 80, 80, 80],
    }),
  },
  /**
   * 弹框内容自定义
   */
  modalContentRender: {},
  /**
   * 点击是否自动弹框
   */
  isAutoShowModal: {
    type: Boolean,
    default: false,
  },
  /**
   * 查询图层样式接口
   */
  queryStyleRequest: {
    type: Object,
    default: () => ({
      api: "/aplcm/layerInfo/queryStyle",
      params: {},
      method: "fetch",
    }),
  },
  /**
   * 修改图层样式接口
   */
  updateStyleRequest: {
    type: Object,
    default: () => ({
      api: "/aplcm/layerInfo/updateStyle",
      params: {},
      method: "post",
    }),
  },
  /**
   * 限制底图边界
   */
  boundArylimitBaseMap: {
    type: Boolean,
  },
  /**
   * 限制图层边界
   */
  boundArylimitLayer: {
    type: Boolean,
  },
  /**
   * 绘制完成之前回调函数
   */
  drawCallback: {
    type: Function,
  },
  /**
   * 图例自定义显示
   */
  legendShowFunc: {
    type: Function,
  },
  /**
   * 图例数组自定义过滤
   */
  legendListFunc: {
    type: Function,
  },
});

//loading
const _loading = ref(false);
//底图
const baseMap = getBaseMapFunc();
//是否显示底图
let isShowBaseMapTool = ref(false);
// 底图切换dom
let baseMapChangeBoxRef = ref();
//当前选择底图
let _baseMapLayer = ref();
//是否显示底图标注
let isShowBaseMapLayerText = ref();
//图例样式修改弹框
let isShowChangeStyleModal = ref(false);
//图例弹框
const legendStyleChangeModalRef = ref();
//当前选中图例
let currentChooseLegend;
//地图实例
let map;
//是否需要重新渲染地图
let isNeedInitMap = true;
//图层树
let layerTree = ref([]);
//投影
let projection;
//分辨率
let resolutions;
//已添加图层实例集合---移除图层需要原始引用对象，所以单独收集,未和图例一起
let layersInstance = [];
//已添加图例实例集合
let legendList = ref([]);
//弹框
let overlay;
// 是否显示弹框
let isShowModal = ref(false);
//弹框dom
const overlayRef = ref();
//当前弹框数据
const currentOverlayData = ref({});
//绘制实例
let _draw;
//绘制输出对象
let drawOutputParam;
//绘制矢量源
let drawVectorSource;
//绘制矢量图层
let drawVectorLayer;
//查询矢量源
let searchVectorSource;
//查询矢量图层
let searchVectorLayer;
//当前操作状态
let optStatus = ref("");
//当前绘制工具
let currentTool = ref();
//当前操作提示语
let drawMessage = ref("");
//删除提示语
let beforeDeleteMessage = ref("");
//当前绘制工具
let _drawTool = ref([]);
//选中交互
let select = null;
//选中旧交互--合并用
let oldSelect = null;
//当前选中要素
let currentSelectFeature;
//边界数据
let _boundAreaList;
//边界图层轮廓矢量源
let boundAreaVectorSource;
//边界图层轮廓矢量图层
let boundAreaVectorLayer;
//外部搜索高亮图层矢量源
let hightLightVectorSource;
//外部搜索高亮图层
let hightLightVectorLayer;
//是否高亮编辑
let hightLightVectorLayerEdit = ref(false);
//记录上一个change layer
let changePreLayer = {};
//是否显示绘制
let _showDraw = ref();
//默认交互
const defaultInteractions = {
  DragRotate,
  DoubleClickZoom,
  DragZoom,
  DragPan,
  PinchZoom,
  PinchRotate,
  KeyboardZoom,
  KeyboardPan,
  MouseWheelZoom,
};
//记录点击数
let drawPointCount = ref(0);
//是否显示底图
let _isShowBaseLayer = ref(true);
const instance = getCurrentInstance();
//wms 请求头
let _layerAuthHeader = ref();
//vNode渲染
function contentRender(content = props.modalContentRender) {
  return content(currentOverlayData.value, currentClickLayer?.getProperties());
}

/**
 * 转换tree
 * @param layers
 * @param parent
 */
function transFormateLayers(layers, parent) {
  layers.forEach((x) => {
    x.parent = parent;
    if (x.children) {
      transFormateLayers(x.children, x);
      let checkedLength = x.children.filter((item) => item.checked && item.showInTree !== false).length;
      x.indeterminate = checkedLength && checkedLength < x.children.length;
      x.checked = checkedLength && checkedLength == x.children.length;
    }
  });
}

//记录当前点击图层layer
let currentClickLayer;
//点击显示弹框方法
let modalShowFunc;
/**
 * 地图初始化
 */
const mapInit = async () => {
  if (!props.layerAuthHeader) {
    // let res = await $http.fetch('/cov/common/findCoverage');
    _layerAuthHeader.value = JSON.parse(
      appStore.platformConfig?.geoserverAuthHeader || '{"Authorization":"Basic YWRtaW46Z2Vvc2VydmVy"}'
    );
  } else {
    _layerAuthHeader.value = props.layerAuthHeader;
  }
  _showDraw.value = props.showDraw;
  layerTree.value = [...props.layers];
  transFormateLayers(layerTree.value);
  _boundAreaList = props.boundAreaList;
  _drawTool.value = props.drawTool;
  resolutions = [];
  legendList.value = [];
  layersInstance = [];

  if (props.projection == "EPSG:4490") {
    projection = new Projection({
      code: "EPSG:4490",
      units: "degrees",
      axisOrientation: "neu",
    });
    projection.setExtent([73.62, 16.7, 147.34, 53.56]);
  } else {
    projection = get(props.projection);
  }
  const projectionExtent = projection.getExtent();
  const size = getWidth(projectionExtent) / 256;

  for (let z = 0; z <= 20; ++z) {
    resolutions[z] = size / Math.pow(2, z + 1);
  }
  //底图
  if (props.baseMapLayer && _isShowBaseLayer.value) {
    baseMap.find((x) => x.get("id") == "_baseMap_" + props.baseMapLayer)?.setVisible(true);
  }
  //添加标注
  if (props.showBaseMapLayerText && _isShowBaseLayer.value) {
    baseMap.find((x) => x.get("id") == "_baseMap_" + props.baseMapLayer + "Text")?.setVisible(true);
  }
  _baseMapLayer.value = props.baseMapLayer;
  isShowBaseMapLayerText.value = props.showBaseMapLayerText;
  //加载轮廓数据
  if (props.boundAreaRequest?.api) {
    let res = await $http[props.boundAreaRequest.method || "post"](
      props.boundAreaRequest.api,
      props.boundAreaRequest.params || {}
    );
    _boundAreaList = res;
  }
  addBoundAreaLayer(_boundAreaList);
  //限制底图边界
  if (props.boundArylimitBaseMap) {
    baseMap.forEach((item) => {
      item.on("postrender", function (e) {
        const vectorContext = getVectorContext(e);
        e.context.globalCompositeOperation = "destination-in";
        if (boundAreaVectorLayer.getSource().getFeatures()) {
          vectorContext.drawFeature(
            boundAreaVectorLayer.getSource().getFeatures()[0],
            new Style({
              fill: new Fill({
                color: "black",
              }),
            })
          );
        }
        e.context.globalCompositeOperation = "source-over";
      });
    });
  }

  //渲染地图
  map = new Map({
    target: props.mapId,
    layers: baseMap,
    controls: [],
    interactions: [],
    view:
      props.viewSyncMapId && commonObj[props.viewSyncMapId]
        ? commonObj[props.viewSyncMapId].getView()
        : new View({
            projection: props.projection, // 坐标系，有EPSG:4326和EPSG:3857
            center: props.mapCenter,
            zoom: props.zoom, // 地图缩放级别（打开页面时默认级别）
            ...(props.viewOptions || {}),
          }),
  });
  //添加交互
  props.interaction.map((x) => {
    if (defaultInteractions[x]) {
      map.addInteraction(new defaultInteractions[x]());
    }
  });
  commonObj[props.mapId] = map;
  //添加选中交互
  addSelect();
  //添加边界
  map.addLayer(boundAreaVectorLayer);
  //添加图层
  if (props.layersRequest?.api) {
    await $http[props.layersRequest.method || "post"](props.layersRequest.api, props.layersRequest.params || {}).then(
      (res) => {
        if (props.layersRequestCallback == "function") {
          res.list = props.layersRequestCallback(res.list);
        }
        layerTree.value = res.list;
        transFormateLayers(layerTree.value);
      }
    );
  }
  addLayer(layerTree.value);

  //添加查询矢量图层
  searchVectorSource = new VectorSource();
  searchVectorLayer = new VectorLayer({
    source: searchVectorSource,
    id: "_searchLayer",
    zIndex: 99,
    style: new Style({
      stroke: new Stroke({
        color: props.drawStyle.borderColor,
        width: props.drawStyle.width,
        strokeOpacity: props.drawStyle.strokeOpacity || 1,
      }),
      fill: props.drawStyle.fillColor
        ? new Fill({
            color: props.drawStyle.fillColor,
            fillOpacity: props.drawStyle.fillOpacity || 1,
          })
        : undefined,
    }),
  });
  map.addLayer(searchVectorLayer);
  //添加高亮图层
  hightLightVectorSource = new VectorSource();
  hightLightVectorLayer = new VectorLayer({
    source: hightLightVectorSource,
    id: "_highLightLayer",
    zIndex: 99,
    style: new Style({
      stroke: new Stroke({
        color: props.highLightStyle.borderColor,
        width: props.highLightStyle.width,
        strokeOpacity: props.highLightStyle.strokeOpacity || 1,
      }),
      fill: props.highLightStyle.fillColor
        ? new Fill({
            color: props.highLightStyle.fillColor,
            fillOpacity: props.highLightStyle.fillOpacity || 1,
          })
        : undefined,
    }),
  });
  map.addLayer(hightLightVectorLayer);

  //map 点击事件
  map.on("singleclick", (e) => {
    currentClickLayer = undefined;
    //新增，绘制状态
    if (optStatus.value == "start" && currentTool.value == "add") {
      drawPointCount.value++;
      return;
    }
    const coordinate = e.coordinate;
    const view = map.getView();
    const viewResolution = view.getResolution();
    const viewProjection = view.getProjection();
    // e.target.forEachFeatureAtPixel(e.pixel, feature => {
    //   console.log(feature);
    // });

    //请求 promise
    let getInfoPromise = [];
    //当前渲染图层组：图层-显示-倒序-开始
    let laryerArr = map
      .getLayers()
      .getArray()
      .filter(
        (x) =>
          x.getVisible() &&
          x.get("canGetInfo") !== false &&
          x.get("isPreview") !== true &&
          !x.get("id")?.startsWith("_")
      )
      .reverse()
      .sort((a, b) => b.getZIndex() - a.getZIndex());

    laryerArr.forEach((layer) => {
      //wms图层
      if (layer.get("type") == "wms") {
        const source = layer.getSource();
        let url = source.getFeatureInfoUrl(e.coordinate, viewResolution, viewProjection, {
          INFO_FORMAT: "application/json",
        });
        getInfoPromise.push(
          fetch(url, { headers: _layerAuthHeader.value }).then((res) => {
            if (res.status === 200) {
              return res.json();
            }
          })
        );
      }
      //矢量图层
      else if (layer instanceof VectorLayer) {
        getInfoPromise.push(layer.getFeatures(e.pixel));
      }
    });
    Promise.all(getInfoPromise).then((resArr) => {
      resArr.forEach((item) => {
        //矢量图层---转化为wms一样的数据格式
        if (item instanceof Array) {
          item.features = item;
        }
      });
      let index = resArr.findIndex((x) => x?.features?.length);
      //找到数据
      if (index >= 0) {
        currentClickLayer = laryerArr[index];
        let data = resArr[index].features[0].properties || resArr[index].features[0].getProperties(); //properties;
        currentOverlayData.value = data;
        let feature =
          resArr[index].features[0] instanceof Feature
            ? resArr[index].features[0]
            : new GeoJSON().readFeature(resArr[index].features[0]);
        //触发弹框--非绘制状态
        if (currentClickLayer.showOverlay !== false && props.showOverlay && optStatus.value != "start") {
          //临时处理低代码多次渲染问题
          isShowModal.value = false;
          modalShowFunc = () => {
            nextTick(() => {
              isShowModal.value = true;
              nextTick(() => {
                overlay.setPosition(coordinate);
              });
            });
          };
          if (props.isAutoShowModal) {
            modalShowFunc();
          }
        }
        if (_showDraw.value) {
          //绘制图层
          if (currentClickLayer.get("isDrawLayer")) {
            if (!currentTool.value || currentTool.value == "choose") {
              //清除搜索图层
              searchVectorSource.clear();
              drawToolClick({ type: "choose", message: "已选中图斑" });
              currentSelectFeature = feature;
              searchVectorSource.addFeature(feature);
            } else if (currentSelectFeature && currentTool.value == "merge" && optStatus.value == "start") {
              mergeFeature(currentSelectFeature, feature)
                .then((res) => {
                  optStatus.value = "end";
                  drawVectorSource.addFeature(res);
                  transformDrawOutputData(
                    [
                      {
                        geometry: res.getGeometry(),
                        properties: currentSelectFeature.getProperties(),
                      },
                    ],
                    [currentSelectFeature, feature]
                  );
                })
                .catch((res) => {
                  select.getFeatures().clear();
                });
            }
          }
        }
        //传递图斑点击事件，外部自定义弹框处理
        emit("layerClick", data, currentClickLayer.getProperties(), feature);
      }
    });
    //调用后端获取数据
    // $http.post(url,data).then(res=>{
    //   emit("outputClick",res)
    // })
    // map.getLayers().getArray().reverse().forEach(layer=>{
    //   if(layer.getSource()){
    //     const source = layer.getSource()
    //     console.log(source)
    //     if(source.getUrlForParams){
    //       const url = source.getUrlForParams(e.coordinate,viewResolution,viewProjection)
    //       if(url){
    //         fetch(url).then(res=>res.text()).then(res=>{
    //           const features = source.getFormat().readFeatures(res)
    //         })
    //       }
    //     }
    //   }
    // })
  });
  //map 移动事件
  map.on("moveend", (e) => {
    layersInstance.forEach((item) => {
      let maxZoom = item.layer.layerMaxZoom || props.layerMaxZoom;
      if (maxZoom) {
        if (map.getView().getZoom() < maxZoom) {
          item.layerInstance.setVisible(false);
        } else {
          item.layerInstance.setVisible(true);
        }
      }
    });
  });
  //地图渲染完成回调
  map.once("loadend", (e) => {
    emit("mapLoadend");
  });
  //弹框
  overlay = new Overlay({
    element: overlayRef.value,
    autoPan: {
      animation: {
        duration: 250,
      },
    },
    positioning: "bottom-center",
  });
  map.addOverlay(overlay);
  //绘制图层
  if (_showDraw.value) {
    drawVectorSource = new VectorSource();
    drawVectorLayer = new VectorLayer({
      source: drawVectorSource,
      id: "_drawLayer",
      zIndex: 99,
      style: new Style({
        stroke: new Stroke({
          color: props.drawStyle.borderColor,
          width: props.drawStyle.width,
          strokeOpacity: props.drawStyle.strokeOpacity || 1,
        }),
        fill: props.drawStyle.fillColor
          ? new Fill({
              color: props.drawStyle.fillColor,
              fillOpacity: props.drawStyle.fillOpacity || 1,
            })
          : undefined,
      }),
    });
    map.addLayer(drawVectorLayer);
  }
  createMeasureTooltip();
};

/**
 * 添加图层
 */
function addLayer(layers) {
  if (!(layers instanceof Array)) {
    layers = [layers];
  }
  layers.forEach((layer) => {
    //存在 children
    if (layer.children) {
      addLayer(layer.children);
    }
    //  未添加过 且 存在选中type
    else if (!layersInstance.find((x) => x.layer == layer) && layer.type && layer.checked) {
      let matrixIds = [];
      for (let z = 0; z < 20; ++z) {
        matrixIds[z] = layer.matrixSet + ":" + z;
      }
      let layerObj;
      if (layer.type == "tile") {
        let _resolutions=[];
          if (layer.matrixSet == "EPSG:4490") {
            projection = new Projection({
              code: "EPSG:4490",
              units: "degrees",
              axisOrientation: "neu",
            });
            projection.setExtent([73.62, 16.7, 147.34, 53.56]);
          } else {
            projection = get(layer.matrixSet);
          }
          const projectionExtent = projection.getExtent();
          const size = getWidth(projectionExtent) / 256;

          for (let z = 0; z <= 20; ++z) {
            _resolutions[z] = size / Math.pow(2, z + 1);
          }
        //瓦片图层
        layerObj = new TileLayer({
          id: layer.layer,
          type: layer.type,
          canGetInfo: layer.canGetInfo,
          isDrawLayer: layer.isDrawLayer,
          layerProperties: { ...layer },
          zIndex: 1,
          ...(layer.layerParams || {}),
          source: new WMTS({
            ...layer,
            format: "image/png",
            tileGrid: new TileWMTS({
              origin: getTopLeft(projection.getExtent()),
              resolutions: _resolutions,
              matrixIds: matrixIds,
            }),
            ...(layer.sourceParams || {}),
          }),
        });
      } else if (layer.type == "wms") {
        // WMS图层
        layerObj = new ImageLayer({
          id: layer.layer,
          type: layer.type,
          canGetInfo: layer.canGetInfo,
          isDrawLayer: layer.isDrawLayer,
          zIndex: 1,
          layerProperties: { ...layer },
          ...(layer.layerParams || {}),
          source: new ImageWMS({
            url: layer.url,
            imageLoadFunction: (iamge, src) => {
              //如果传入授权请求头
              if (Object.keys(_layerAuthHeader.value).length) {
                fetch(src, { headers: _layerAuthHeader.value })
                  .then((res) => res.blob())
                  .then((blob) => {
                    // 创建一个新的URL指向Blob对象
                    const imageUrl = URL.createObjectURL(blob);
                    iamge.getImage().src = imageUrl;
                  });
              } else {
                iamge.getImage().src = src;
              }
            },
            params: {
              VERSION: "1.1.0",
              LAYERS: layer.layer,
              CQL_FILTER: layer.filter,
              ...(layer.params || {}),
            },
            ...(layer.sourceParams || {}),
          }),
        });
      } else if (layer.type == "geojson") {
        let vectorSource;
        const format = new GeoJSON();
        if (typeof layer.source == "string") {
          vectorSource = new VectorSource({
            url: layer.source,
            format,
            ...(layer.sourceParams || {}),
          });
        } else if (typeof layer.source == "object") {
          vectorSource = new VectorSource();
          const features = format.readFeatures(layer.source);
          vectorSource.addFeatures(features);
        }
        layerObj = new VectorLayer({
          source: vectorSource,
          id: layer.id || layer.name,
          type: layer.type,
          isDrawLayer: layer.isDrawLayer,
          canGetInfo: layer.canGetInfo,
          layerProperties: { ...layer },
          zIndex: 1,
          ...(layer.layerParams || {}),
          style: (feature) => {
            // const size = feature.get('features').length;
            return new Style({
              stroke: new Stroke({
                color: layer.style?.borderColor || "rgba(255,0,0,0.6)", // 描边
                width: layer.style?.width || 1, // 设置描边宽度为 1 像素
                strokeOpacity: layer.style?.strokeOpacity || 1,
              }),
              fill: new Fill({
                color: layer.style?.fillColor, // 填充
                fillOpacity: layer.style?.fillOpacity || 1,
              }),
            });
          },
        });
      } else if (layer.type == "marker") {
        let markerSource = new VectorSource();
        layerObj = new VectorLayer({
          source: markerSource,
          id: layer.id || layer.name,
          type: layer.type,
          isDrawLayer: layer.isDrawLayer,
          canGetInfo: layer.canGetInfo,
          layerProperties: { ...layer },
          zIndex: 1,
          ...(layer.layerParams || {}),
        });
        //设置样式
        layerObj.setStyle((feature) => {
          // const size = feature.get('features').length;
          let styleOption = {};
          if (layer.style?.Circle) {
            styleOption.image = new Circle({
              ...(layer.style?.circle || {}),
              stroke: new Stroke({
                color: layer.style?.circle?.borderColor, // 描边
                width: layer.style?.circle?.width || 1, // 设置描边宽度为 1 像素
                strokeOpacity: layer.style?.circle?.strokeOpacity || 1,
              }),
              fill: new Fill({
                color: layer.style?.circle?.fillColor, // 填充
                fillOpacity: layer.style?.circle?.fillOpacity || 1,
              }),
              radius: layer.style?.circle?.radius,
            });
          }
          //圆和图片二选一，默认图片
          else {
            styleOption.image = new Icon({
              anchorOrigin: "bottom-left",
              anchorXUnits: "fraction",
              anchorYUnits: "pixels",
              src: layer.style?.img?.src || default_marker,
              scale: 1.0,
              ...(layer.style?.img || {}),
            });
          }
          if (layer.style?.text) {
            styleOption.text = new Text({
              font: "12px sans-serif",
              ...(layer.style?.text || {}),
              text:
                typeof layer.style?.text.text == "function"
                  ? layer.style?.text.text(feature.getProperties())
                  : layer.style?.text.text,
              fill: new Fill({
                color: layer.style?.text?.fillColor, // 填充
                fillOpacity: layer.style?.text?.fillOpacity || 1,
              }),
              stroke: new Stroke({
                color: layer.style?.text?.borderColor, // 描边
                width: layer.style?.text?.width || 1, // 设置描边宽度为 1 像素
                strokeOpacity: layer.style?.text?.strokeOpacity || 1,
              }),
            });
          }

          return new Style(styleOption);
        });
        let arr = [];
        layer.source?.forEach((item) => {
          let iconFeature;
          if (item instanceof Array) {
            iconFeature = new Feature({
              geometry: new Point([Number(item[0]), Number(item[1])]),
            });
          } else {
            let lng, lat;
            if (layer.sourceParams?.lng && layer.sourceParams?.lat) {
              //同字段,
              if (layer.sourceParams.lng == layer.sourceParams.lat) {
                //数组
                if (item[layer.sourceParams.lng] instanceof Array) {
                  [lng, lat] = item[layer.sourceParams.lng];
                }
                //字符串
                else {
                  [lng, lat] = item[layer.sourceParams.lng].split(",");
                }
              }
              //2个字段
              else {
                lng = item[layer.sourceParams.lng];
                lat = item[layer.sourceParams.lat];
              }
            } else {
              lng = item.lng;
              lat = item.lat;
            }
            iconFeature = new Feature({
              geometry: new Point([Number(lng), Number(lat)]),
            });
            //对象数据才设置属性
            iconFeature.setProperties(item);
          }
          arr.push(iconFeature);
        });
        markerSource.addFeatures(arr);
      }
      //储存layer实例
      layersInstance.push({ layerInstance: layerObj, layer });
      //可编辑，则从geoserve取
      if (layer.canEditColor || layer.queryStyle) {
        //记录顺序
        let index = legendList.value.length;
        //同一个图层去重
        if (!legendList.value.find((x) => x.layer == layer.layer)) {
          legendList.value.push(layer);
          queryStyle(layer).then((res) => {
            legendList.value.splice(index, 1, res);
          });
        }
      } else if (layer.legendColor !== false && layer.legendColor !== null) {
        layer._legendColorType = layer.legendColor instanceof Array ? "array" : "string";
        //同一个图层去重
        if (!legendList.value.find((x) => x.layer == layer.layer)) {
          legendList.value.push(layer);
        }
      }
      //限制图层边界
      if (props.boundArylimitLayer) {
        layerObj.on("postrender", function (e) {
          const vectorContext = getVectorContext(e);
          e.context.globalCompositeOperation = "destination-in";
          if (boundAreaVectorLayer.getSource().getFeatures()) {
            vectorContext.drawFeature(
              boundAreaVectorLayer.getSource().getFeatures()[0],
              new Style({
                fill: new Fill({
                  color: "black",
                }),
              })
            );
          }
          e.context.globalCompositeOperation = "source-over";
        });
      }
      map.addLayer(layerObj);
      //仅矢量图层可自适应
      if (layer.isFit && layerObj instanceof VectorLayer) {
        mapFit(layerObj.getSource().getExtent());
      }
    }
  });
}

/**
 * 图例回调
 */
function getLegendList() {
  if (props.legendListFunc) {
    return props.legendListFunc(legendList.value, layersInstance);
  }
  return legendList.value;
}
/**
 * 查询可编辑颜色图层样式
 * @param layer
 */
function queryStyle(layer) {
  return $http[props.queryStyleRequest.method](props.queryStyleRequest.api, {
    layerName: layer.layer.split(":")[1],
    ...props.updateStyleRequest.params,
  }).then((res) => {
    let xj = new _x2js();
    let styleXml = xj.xml2js(res);
    let rule = styleXml.StyledLayerDescriptor.NamedLayer.UserStyle.FeatureTypeStyle.Rule;
    layer._legendColorType = rule instanceof Array ? "array" : "string";
    layer.legendColor = rule.map((x) => {
      let obj = {
        name: x.Name?.__text || "--",
      };
      if (x.PolygonSymbolizer?.Fill?.CssParameter) {
        if (x.PolygonSymbolizer.Fill.CssParameter instanceof Array) {
          x.PolygonSymbolizer.Fill.CssParameter.forEach((item) => {
            obj[item._name] = item.__text;
          });
        } else {
          obj[x.PolygonSymbolizer.Fill.CssParameter._name] = x.PolygonSymbolizer.Fill.CssParameter.__text;
        }
      }
      if (x.PolygonSymbolizer?.Stroke?.CssParameter) {
        if (x.PolygonSymbolizer.Stroke.CssParameter instanceof Array) {
          x.PolygonSymbolizer.Stroke.CssParameter.forEach((item) => {
            obj[item._name] = item.__text;
          });
        } else {
          obj[x.PolygonSymbolizer.Stroke.CssParameter._name] = x.PolygonSymbolizer.Stroke.CssParameter.__text;
        }
      }
      //处理geoserver 透明度为1时不返回
      if (obj["fill-opacity"] === undefined) {
        obj["fill-opacity"] = 1;
      }
      if (obj["stroke-opacity"] === undefined) {
        obj["stroke-opacity"] = 1;
      }
      if (obj["stroke-width"] === undefined) {
        obj["stroke-width"] = 1;
      }
      return obj;
    });
    layer.styleXml = styleXml;
    return layer;
  });
}
/**
 * 移除指定图层
 * @param layers 图层名
 */
function removeLayer(layers) {
  if (!(layers instanceof Array)) {
    layers = [layers];
  }
  layers.forEach((layer) => {
    if (layer.showInTree !== false) {
      let index = layersInstance.findIndex((x) => x.layer == layer || x.layer.layer == layer.layer);
      if (index >= 0) {
        map.removeLayer(layersInstance[index].layerInstance);
        layersInstance.splice(index, 1);
        let sameLayerArr = layersInstance.filter((x) => x.layer == layer || x.layer.layer == layer.layer);
        if (!sameLayerArr.length) {
          legendList.value.splice(index, 1);
        }
      }
      if (layer.children?.length) {
        removeLayer(layer.children);
      }
    }
  });
}
/**
 * 移除所有已添加图层
 */
function removeAllLayer() {
  layersInstance.forEach((item) => {
    map.removeLayer(item.layerInstance);
  });
  layersInstance = [];
  legendList.value = [];
}

function loopChildrenLayer(arr, isChecked) {
  arr.forEach((item) => {
    if (item.showInTree !== false) {
      item.checked = isChecked;
      if (item.children?.length) {
        loopChildrenLayer(item.children, isChecked);
      }
    }
  });
}

/**
 * 图层切换
 * @param item 图层
 */
function layerChange(item) {
  loopChildrenLayer([item], item.checked);
  if (item.parent) {
    item.parent.checked = item.checked;
  }
  let parent = item.parent;
  while (parent) {
    let checkedLength = parent.children.filter((x) => x.checked && x.showInTree !== false).length;
    parent.indeterminate = checkedLength && checkedLength < parent.children.length;
    parent.checked = checkedLength == parent.children.length;
    parent = parent.parent;
  }
  if (item.checked) {
    addLayer([item]);
  } else {
    item.indeterminate = false;
    removeLayer([item]);
  }
}
/**
 * 绘制工具选择
 */
function drawToolClick(item) {
  currentTool.value = item.type;
  drawMessage.value = item.message;
  beforeDeleteMessage.value = item.message;
  select?.setActive(false);
  switch (item.type) {
    case "choose":
      break;
    case "add":
      draw();
      break;
    case "merge":
      optStatus.value = "start";
      addSelect();
      break;
    case "split":
      drawSplit();
      break;
    case "del":
      optStatus.value = "end";
      transformDrawOutputData([], [currentSelectFeature]);
      break;
  }
}
/**
 * 关闭绘制提示
 */
function closeMessage(isOutput = true) {
  let feature = currentSelectFeature;
  searchVectorSource.clear();
  drawClear();
  initOpt();
  addSelect();
  isNeedInitMap = false;
  if (isOutput) {
    emit("drawend", drawOutputParam);
    emit("update:modelValue", drawOutputParam);
  }
  if (hightLightVectorLayerEdit.value) {
    currentSelectFeature = feature;
    drawToolClick({ type: "choose", message: "已选中图斑" });
    searchVectorSource.addFeature(currentSelectFeature);
  }
}

/**
 * 动态渲染底图切换box top
 */
function getBaseMapChangeBoxStyle() {
  let top_distance =
    (baseMapChangeBoxRef.value?.getBoundingClientRect().top || 0) -
    (document.getElementById(props.mapId)?.getBoundingClientRect() || 0);
  return {
    top: top_distance > 70 ? undefined : 0,
  };
}

/**
 * 底图切换
 */
function changeBaseLayer(name) {
  _baseMapLayer.value = name;
  baseMap
    .filter((x) => x.get("_type") == "img")
    .forEach((x) => {
      x.setVisible(false);
    });
  baseMap.find((x) => x.get("id") == "_baseMap_" + name)?.setVisible(true);
}
/**
 * 底图标注切换
 */
function changeBaseLayerText() {
  let currentBaseMap = baseMap.find((x) => x.get("_type") == "img" && x.getVisible());
  if (!currentBaseMap) {
    throw Error("底图图层存在问题");
  }
  baseMap
    .filter((x) => x.get("_type") == "text")
    .forEach((x) => {
      x.setVisible(false);
    });
  if (isShowBaseMapLayerText.value) {
    baseMap.find((x) => x.get("id") == currentBaseMap.get("id") + "Text").setVisible(true);
  }
}
/**
 * 样式修改弹框确认
 */
function onChangeStyleConfirm() {
  legendStyleChangeModalRef.value.validate().then((res) => {
    if (res.isValid) {
      let rule = currentChooseLegend.styleXml.StyledLayerDescriptor.NamedLayer.UserStyle.FeatureTypeStyle.Rule;
      let item = rule.find((x) => x.Name.__text == res.values.name);
      if (item.PolygonSymbolizer?.Fill?.CssParameter) {
        if (item.PolygonSymbolizer.Fill.CssParameter instanceof Array) {
          let fill = item.PolygonSymbolizer.Fill.CssParameter.find((x) => x._name == "fill");
          let fillOpacity = item.PolygonSymbolizer.Fill.CssParameter.find((x) => x._name == "fill-opacity");
          if (fill) {
            fill.__text = res.values.fill;
          } else {
            item.PolygonSymbolizer.Fill.CssParameter.push({ _name: "fill", __prefix: "sld", __text: res.values.fill });
          }
          if (fillOpacity) {
            fillOpacity.__text = res.values["fill-opacity"];
          } else {
            item.PolygonSymbolizer.Fill.CssParameter.push({
              _name: "fill-opacity",
              __prefix: "sld",
              __text: res.values["fill-opacity"],
            });
          }
        } else {
          item.PolygonSymbolizer.Fill.CssParameter = [
            { _name: "fill", __prefix: "sld", __text: res.values.fill },
            { _name: "fill-opacity", __prefix: "sld", __text: res.values["fill-opacity"] },
          ];
        }
      }
      if (item.PolygonSymbolizer?.Stroke?.CssParameter) {
        if (item.PolygonSymbolizer.Stroke.CssParameter instanceof Array) {
          let stroke = item.PolygonSymbolizer.Stroke.CssParameter.find((x) => x._name == "stroke");
          let strokeOpacity = item.PolygonSymbolizer.Stroke.CssParameter.find((x) => x._name == "stroke-opacity");
          let strokeWidth = item.PolygonSymbolizer.Stroke.CssParameter.find((x) => x._name == "stroke-width");
          if (stroke) {
            stroke.__text = res.values.stroke;
          } else {
            item.PolygonSymbolizer.Stroke.CssParameter.push({
              _name: "stroke",
              __prefix: "sld",
              __text: res.values.stroke,
            });
          }
          if (strokeOpacity) {
            strokeOpacity.__text = res.values["stroke-opacity"];
          } else {
            item.PolygonSymbolizer.Stroke.CssParameter.push({
              _name: "stroke-opacity",
              __prefix: "sld",
              __text: res.values["stroke-opacity"],
            });
          }
          if (strokeWidth) {
            strokeWidth.__text = res.values["stroke-width"];
          } else {
            item.PolygonSymbolizer.Stroke.CssParameter.push({
              _name: "stroke-width",
              __prefix: "sld",
              __text: res.values["stroke-width"],
            });
          }
        } else {
          item.PolygonSymbolizer.Stroke.CssParameter = [
            { _name: "stroke", __prefix: "sld", __text: res.values.stroke },
            { _name: "stroke-opacity", __prefix: "sld", __text: res.values["stroke-opacity"] },
            ,
            { _name: "stroke-width", __prefix: "sld", __text: res.values["stroke-width"] },
          ];
        }
      }
      let xj = new _x2js();
      $http[props.updateStyleRequest.method](props.updateStyleRequest.api, {
        sldBody: xj.js2xml(currentChooseLegend.styleXml),
        layerName: currentChooseLegend.layer.split(":")[1],
        ...props.updateStyleRequest.params,
      }).then((res) => {
        let { layerInstance } = layersInstance.find((x) => x.layer.layer == currentChooseLegend.layer);
        layerInstance.getSource().refresh();
        isShowChangeStyleModal.value = false;
        let index = legendList.value.findIndex((x) => x == currentChooseLegend);
        queryStyle(currentChooseLegend).then((res) => {
          legendList.value.splice(index, 1, res);
        });
      });
    }
  });
}
/**
 * 初始化操作
 */
function initOpt() {
  currentTool.value = "";
  drawMessage.value = "";
  drawOutputParam = undefined;
  _draw && map.removeInteraction(_draw);
  oldSelect?.getFeatures().clear();
  select && map.removeInteraction(select);
  _draw = undefined;
  oldSelect = undefined;
  select = undefined;
  currentSelectFeature = undefined;
}
/**
 * 删除
 */
function drawDelete() {
  //还原删除之前的message
  drawMessage.value = beforeDeleteMessage.value;
  optStatus.value = "";
  _draw.abortDrawing();
  map.addInteraction(_draw);
  drawClear();
}
/**
 * 图例样式弹框
 */
function showStyleModal(item, legend) {
  if (item.canEditColor) {
    currentChooseLegend = item;
    isShowChangeStyleModal.value = true;
    nextTick(() => {
      legendStyleChangeModalRef.value.setValues(legend);
    });
  }
}

/**
 * 点击完成
 */
function drawOutput() {
  initOpt();
  emit("drawend", drawOutputParam);
  emit("update:modelValue", drawOutputParam);
  isNeedInitMap = false;
  addSelect();
}

const _mapInit = $utils.debounce(() => {
  map.dispose();
  mapInit();
}, 100);

watch(props, (newVal, oldVal) => {
  if (isNeedInitMap) {
    _mapInit();
  }
  isNeedInitMap = true;
});

//挂载
onMounted(() => {
  mapInit();
});

//------------------外部方法------------------------
/**
 * 获取地图实例
 */
function getMap() {
  return map;
}
/**
 * 获取地图view
 */
function getView() {
  return map.getView();
}
/**
 * 自适应范围
 * @param extent 待显示范围
 */
function mapFit(extent, duration = props.fitParams.duration, padding = props.fitParams.padding) {
  getView().fit(extent, {
    duration,
    padding,
  });
}
/**
 * 设置地图中心点
 * @param center
 */
function setCenter(center) {
  map.getView().setCenter(center);
}
/**
 * 设置地图zoom
 * @param {number} zoom 缩放级别
 */
function setZoom(zoom) {
  map.getView().setZoom(zoom);
}

/**
 * 添加选中交互
 */
function addSelect() {
  if (props.enableSelect) {
    return new Promise((resolve, reject) => {
      if (select) {
        oldSelect = select;
      }
      select = new Select({
        condition: click,
        filter: (feature, layer, c, d) => {
          //内部图层不能选中
          return (!layer.get("id") || !layer.get("id").startsWith("_")) && layer.get("type") != "marker";
        },
        style: new Style({
          fill: new Fill({
            color: props.selectedStyle.fillColor,
            fillOpacity: props.selectedStyle.fillOpacity || 1,
          }),
          stroke: new Stroke({
            color: props.selectedStyle.borderColor,
            width: props.selectedStyle.width,
            strokeOpacity: props.selectedStyle.fillColor || 1,
          }),
        }),
      });
      map.addInteraction(select);
      select.on("select", function (e) {
        if (_showDraw.value && ["add", "split", "merge", "del"].includes(currentTool)) {
          //合并
          if (currentSelectFeature && currentTool.value == "merge" && e.selected[0] && optStatus.value == "start") {
            mergeFeature(currentSelectFeature, e.selected[0])
              .then((res) => {
                optStatus.value = "end";
                drawVectorSource.addFeature(res);
                transformDrawOutputData(
                  [{ geometry: res.getGeometry(), properties: {} }],
                  [currentSelectFeature, e.selected[0]]
                );
              })
              .catch((res) => {
                select.getFeatures().clear();
              });
            return;
          } else if (!e.selected[0]) {
            drawToolClick({ type: "", message: "" });
          }
          if (!oldSelect) {
            currentSelectFeature = e.selected[0];
            mapFit(currentSelectFeature.getGeometry().getExtent());
            drawToolClick({ type: "choose", message: "已选中图斑" });
          }
          resolve({ feature: currentSelectFeature });
        }
      });
    });
  }
}

let measureTooltipElement;
let measureTooltip;
function createMeasureTooltip() {
  if (measureTooltipElement) {
    measureTooltipElement.remove();
  }
  if (measureTooltip) {
    map.removeOverlay(measureTooltip);
  }
  measureTooltipElement = document.createElement("div");
  measureTooltipElement.className = "ol-tooltip ol-tooltip-measure";
  measureTooltip = new Overlay({
    element: measureTooltipElement,
    positioning: "bottom-center",
    stopEvent: false,
    insertFirst: false,
  });
  map.addOverlay(measureTooltip);
}

function getAreString(polygon) {
  const area = getArea(polygon, { projection: props.projection });
  let output;
  if (area > 10000) {
    output = Math.round((area / 1000000) * 100) / 100 + " " + "km<sup>2</sup>";
  } else {
    output = Math.round(area * 100) / 100 + " " + "m<sup>2</sup>";
  }
  return output;
}
/**
 * 图斑绘制
 * @param {string} type 默认为多边形
 * @param {boolean}isClear 是否清除之前绘制，默认为true
 */
function draw(type = "Polygon", isClear = true) {
  drawOutputParam = undefined;
  _draw = new Draw({
    source: drawVectorSource,
    type,
    style: new Style({
      stroke: new Stroke({
        color: props.drawStyle.borderColor,
        width: props.drawStyle.width,
        strokeOpacity: props.drawStyle.strokeOpacity || 1,
      }),
      fill: new Fill({
        color: props.drawStyle.fillColor,
        fillOpacity: props.drawStyle.fillOpacity || 1,
      }),
    }),
  });
  let snap = new Snap({
    source: drawVectorSource,
  });
  map.addInteraction(_draw);
  map.addInteraction(snap);
  let sketch, listener;
  _draw.on("drawstart", (e) => {
    optStatus.value = "start";
    drawPointCount.value = 0;
    if (isClear) {
      drawClear();
    }
    sketch = e.feature;
    listener = sketch.getGeometry().on("change", (evt) => {
      const geom = evt.target;
      let output;
      let tooltipCoord;
      if (geom instanceof Polygon) {
        output = getAreString(geom);
        tooltipCoord = geom.getInteriorPoint().getCoordinates();
      }
      measureTooltipElement.innerHTML = output;
      measureTooltip.setPosition(tooltipCoord);
    });
  });
  _draw.on("drawend", async (e) => {
    optStatus.value = "end";
    let geometry = e.feature.getGeometry();
    let differenceFeature;
    map.removeInteraction(_draw);
    //绘制图层
    let drawLayerArr = layersInstance.filter((x) => x.layer.isDrawLayer == true);
    if (drawLayerArr.length) {
      _loading.value = true;
      let res = await wfsPolygon(drawLayerArr, geometry).catch(() => {
        measureTooltip.setPosition();
        _loading.value = false;
      });
      _loading.value = false;
      if (res?.features?.length) {
        let geomArr = res.features.map((x) =>
          turf.polygon(x.geometry.type == "MultiPolygon" ? x.geometry.coordinates[0] : x.geometry.coordinates)
        );
        var difference = turf.difference(turf.featureCollection([turf.polygon(geometry.getCoordinates()), ...geomArr]));
        if (!difference) {
          sketch = null;
          unByKey(listener);
          nextTick(() => {
            ElMessage({
              message: `禁止在已有图斑上绘制`,
              type: "warning",
            });
            drawDelete();
          });
          return;
        }
        if (difference.geometry.type == "MultiPolygon") {
          sketch = null;
          unByKey(listener);
          nextTick(() => {
            ElMessage({
              message: `去重后会产生多个图斑`,
              type: "warning",
            });
            drawDelete();
          });
          return;
        }
        geometry.setCoordinates(difference.geometry.coordinates);
        differenceFeature = new Feature(geometry);
        nextTick(() => {
          drawVectorSource.removeFeature(e.feature);
          drawVectorSource.addFeature(differenceFeature);
        });
      }
    }
    sketch = null;
    unByKey(listener);
    //不限制边界绘制
    if (!props.boundAreaLimit) {
      transformDrawOutputData([{ geometry: geometry, properties: {} }]);
    } else {
      //包含数组
      let containsArr = [];
      //重合数组
      let overlapArr = [];
      //第一个点所在图形
      let firstPointPolygonArr = [];
      boundAreaVectorSource.getFeatures().forEach((feature) => {
        let boundPloygon = turf.polygon(
          feature.getGeometry().getType("MultiPolygon")
            ? feature.getGeometry().getCoordinates()[0]
            : feature.getGeometry().getCoordinates()
        );
        let drawPloygon = turf.polygon(geometry.getCoordinates());
        if (turf.booleanContains(boundPloygon, drawPloygon)) {
          containsArr.push({ feature, boundPloygon, drawPloygon });
        }
        if (turf.booleanOverlap(boundPloygon, drawPloygon)) {
          overlapArr.push({ feature, boundPloygon, drawPloygon });
        }
        //第一个点在哪个边界内部
        if (turf.booleanPointInPolygon(turf.point(e.feature.getGeometry().getFirstCoordinate()), boundPloygon)) {
          firstPointPolygonArr.push({ feature, boundPloygon, drawPloygon });
        }
      });
      //重合
      if (overlapArr.length) {
        if (overlapArr.length > 1) {
          if (firstPointPolygonArr.length == 1) {
            let intersection = turf.intersect(
              turf.featureCollection([firstPointPolygonArr[0].boundPloygon, firstPointPolygonArr[0].drawPloygon])
            );
            let intersectFeature = new Feature(new Polygon(intersection.geometry.coordinates));
            nextTick(() => {
              drawVectorSource.removeFeature(e.feature);
              drawVectorSource.removeFeature(differenceFeature);
              drawVectorSource.addFeature(intersectFeature);
              transformDrawOutputData([{ geometry: intersectFeature.getGeometry(), properties: {} }]);
            });
            return;
          }
          //第一个点存在多个内部
          nextTick(() => {
            drawDelete();
            ElMessage({
              message: `第一个点不能绘制在边界外`,
              type: "warning",
            });
          });
        } else {
          let intersection = turf.intersect(
            turf.featureCollection([overlapArr[0].boundPloygon, overlapArr[0].drawPloygon])
          );
          let intersectFeature = new Feature(new Polygon(intersection.geometry.coordinates));
          nextTick(() => {
            drawVectorSource.removeFeature(e.feature);
            drawVectorSource.removeFeature(differenceFeature);
            drawVectorSource.addFeature(intersectFeature);
            transformDrawOutputData([{ geometry: intersectFeature.getGeometry(), properties: {} }]);
          });
        }
      }
      //未包含
      else if (!containsArr.length) {
        nextTick(() => {
          drawDelete();
          ElMessage({
            message: `只能绘制在边界范围内`,
            type: "warning",
          });
        });
        return;
      } else {
        transformDrawOutputData([{ geometry: geometry, properties: {} }]);
      }
    }
  });
}
/**
 * 转换输出数据
 * @param features 新的图形
 * @param oldFeatures 旧的图形
 */
function transformDrawOutputData(features = [], oldFeatures = []) {
  drawMessage.value = "绘制完成";
  let arr = features.map((item) => {
    let geom;
    if (props.geomDataType == "json") {
      geom = item.geometry.getCoordinates();
    } else if (props.geomDataType == "wkt") {
      geom = WKT.convert({
        type: item.geometry.getType(),
        coordinates: item.geometry.getCoordinates(),
      });
    } else if (props.geomDataType == "jsonString") {
      //3维数组{Array<Array<Coordinate>>}
      geom = JSON.stringify(item.geometry.getCoordinates());
    } else {
      //默认取第一项[0]为二维数组  {Array<Array<Coordinate>>}
      geom = JSON.stringify(item.geometry.getCoordinates()[0]);
    }
    return {
      ...item.properties,
      [props.modeValueMap.geom]: geom,
      [props.modeValueMap.area]: getArea(item.geometry, { projection: props.projection }), //formatArea(item.geometry.getCoordinates())
    };
  });
  drawOutputParam = {
    type: props.drawTypeEnum[currentTool.value],
    [props.modeValueMap.oldSns]: oldFeatures.map((x) => x.getProperties()?.[props.oldSns]).filter((x) => !!x),
    [props.modeValueMap.features]: arr,
    oldFeatures: oldFeatures.map((x) => x.getProperties()),
  };
  if (typeof props.drawCallback == "function") {
    if (!props.drawCallback(oldFeatures)) {
      nextTick(() => {
        drawDelete();
        emit("drawend", undefined);
        emit("update:modelValue", undefined);
        drawVectorSource.clear();
        drawPointCount.value = 0;
        isNeedInitMap = false;
      });
      return;
    }
  }
  emit("drawend", drawOutputParam);
  emit("update:modelValue", drawOutputParam);
  drawPointCount.value = 0;
  //传递数据，不需要初始化地图
  isNeedInitMap = false;
}
/**
 * 分割
 */
function drawSplit(
  callback = (arr) => {
    if (arr.length > props.maxSplit) {
      ElMessage({
        message: `一次只能拆分为${props.maxSplit}个`,
        type: "warning",
      });
      return false;
    }
    return true;
  },
  isClear = true
) {
  drawOutputParam = undefined;
  _draw = new Draw({
    source: drawVectorSource,
    type: "LineString",
    style: new Style({
      image: new Circle({
        radius: 5,
        fill: new Fill({
          color: "#03a9f4",
        }),
      }),
      stroke: new Stroke({
        color: "#03a9f4",
        width: 2,
      }),
      fill: new Fill({
        color: "rgba(255, 255, 255, 0.7)",
      }),
    }),
  });

  map.addInteraction(_draw);
  _draw.on("drawstart", (e) => {
    optStatus.value = "start";
    if (isClear) {
      drawClear();
    }
  });
  _draw.on("drawend", (e) => {
    optStatus.value = "end";
    const coordinates = e.feature.getGeometry().getCoordinates();
    let ploygon = turf.polygon(
      currentSelectFeature.getGeometry().getType() == "MultiPolygon"
        ? currentSelectFeature.getGeometry().getCoordinates()[0]
        : currentSelectFeature.getGeometry().getCoordinates()
    );
    ploygon.properties = currentSelectFeature.values_;
    let polyline = turf.lineString(coordinates);
    let result = polygonCut(ploygon, polyline);
    if (result.features.length == 1) {
      nextTick(() => {
        drawClear();
        ElMessage({
          message: `请在选中图斑上分割`,
          type: "warning",
        });
      });
      return;
    }
    nextTick(() => {
      drawClear();
      if (callback(result.features)) {
        map.removeInteraction(_draw);
        //绘出结果 绘制出多个个多边形
        result.features.forEach((feature, index) => {
          if (feature.geometry && feature.geometry.type && feature.geometry.type === "Polygon") {
            console.log(feature.geometry.coordinates);
            let feature1 = new Feature(new Polygon(feature.geometry.coordinates));
            feature1.setProperties(feature.properties);
            feature1.setStyle(
              new Style({
                //填充色
                fill: new Fill({
                  color: props.drawSplitStyle.fillColor || "rgba(244, 3, 49, 0.5)",
                  fillOpacity: props.drawSplitStyle.fillOpacity || 1,
                }),
                //边线颜色
                stroke: new Stroke({
                  color: props.drawSplitStyle.strokeColor || "#fff",
                  width: props.drawSplitStyle.width || 2,
                  strokeOpacity: props.drawSplitStyle.strokeOpacity || 1,
                }),
              })
            );
            // let Area = formatArea(feature.geometry.coordinates);
            // feature1.set('Area', Area);
            drawVectorSource.addFeature(feature1);
            // let measureTooltipElement = document.createElement("div");
            //   measureTooltipElement.innerText = String(index + 1);
            //   measureTooltipElement.style.color = "#fff";
            //   let measureTooltip = new Overlay({
            //     element: measureTooltipElement,
            //     // offset: [0, -15],
            //     stopEvent: false,
            //     insertFirst: false,
            //   });
            //   let openlayer = new Feature({
            //     geometry: new Polygon(item.geometry.coordinates),
            //   });
            //   measureTooltip.setPosition(
            //     openlayer.getGeometry().getInteriorPoint().getCoordinates()
            //   );
            //   this.measureTooltip.push(measureTooltip);
            //   this.map.addOverlay(measureTooltip);
          }
        });
        transformDrawOutputData(
          drawVectorSource.getFeatures().map((x) => ({ geometry: x.getGeometry(), properties: x.getProperties() })),
          [currentSelectFeature]
        );
      }
    });

    //点击完成再输出
    //drawOutput()
  });
}
// 计算图形面积
function formatArea(c) {
  var polygon = turf.polygon(c);

  var area = turf.area(polygon);
  return area;
}
// 线拆分逻辑处理
function polygonCut(poly, line, tolerance = 0.000001, toleranceType = "kilometers") {
  if (line.geometry === void 0 || line.geometry.type.toLowerCase().indexOf("linestring") === -1) {
    // throw "传入的必须为linestring";
    return;
  }

  if (line.geometry.type === "LineString") {
    if (
      turf.booleanPointInPolygon(turf.point(line.geometry.coordinates[0]), poly) ||
      turf.booleanPointInPolygon(turf.point(line.geometry.coordinates[line.geometry.coordinates.length - 1]), poly)
    ) {
      // throw "起点和终点必须在多边形之外";

      return;
    }
  }
  // 2. 计算交点，并把线的点合并
  //计算相交点
  let lineIntersect = turf.lineIntersect(line, poly);

  //获取一个或一组Feature，并将所有位置作为点返回。拆分为点
  const lineExp = turf.explode(line);

  for (let i = 0; i < lineExp.features.length - 1; i++) {
    lineIntersect.features.push(turf.point(lineExp.features[i].geometry.coordinates));
  }
  // 3. 计算线的缓冲区
  const lineBuffer = turf.buffer(line, tolerance, {
    units: toleranceType,
  });

  // 4. 计算线缓冲和多边形的difference，返回"MultiPolygon"，所以将其拆开
  //通过从第一个多边形剪裁第二个多边形来查找两个多边形之间的差异。
  const _body = turf.difference(turf.featureCollection([poly, lineBuffer]));
  let pieces = [];
  if (_body.geometry.type === "Polygon") {
    pieces.push(turf.polygon(_body.geometry.coordinates));
  } else {
    _body.geometry.coordinates.forEach(function (a) {
      pieces.push(turf.polygon(a));
    });
  }
  // 5. 处理点数据
  for (let p = 0; p < pieces.length; p++) {
    const piece = pieces[p];
    for (let c in piece.geometry.coordinates[0]) {
      const coord = piece.geometry.coordinates[0][c];
      const p = turf.point(coord);
      for (let lp in lineIntersect.features) {
        const lpoint = lineIntersect.features[lp];
        //判断两点距离
        if (turf.distance(lpoint, p, toleranceType) <= tolerance * 2) {
          piece.geometry.coordinates[0][c] = lpoint.geometry.coordinates;
        }
      }
    }
  }
  // 6. 过滤掉重复点
  // for (let p=0;p<pieces.length; p++) {
  //   const coords = pieces[p].geometry.coordinates[0];
  //   pieces[p].geometry.coordinates[0] = filterDuplicatePoints(coords);
  // }
  // 7. 将属性赋予每一个polygon，并处理id
  pieces.forEach((a, index) => {
    a.properties = Object.assign({}, poly.properties);
    delete a.properties.geometry;
    a.properties.id += `-${index + 1}`;
  });

  //获取一个或多个Feature并创建一个FeatureCollection。
  return turf.featureCollection(pieces);
}
/**
 * 清除之前绘制
 */
function drawClear() {
  if (drawVectorSource) {
    drawVectorSource.clear();
    drawPointCount.value = 0;
  }
  if (measureTooltip) {
    measureTooltip.setPosition();
  }
}
/**
 * 撤销
 */
function drawRevoke() {
  //合并
  if (currentTool.value == "merge") {
    optStatus.value = "start";
    drawVectorSource.clear();
    select?.getFeatures().clear();
    drawOutputParam = undefined;
    emit("drawend", drawOutputParam);
    emit("update:modelValue", drawOutputParam);
    isNeedInitMap = false;
  } else {
    _draw?.removeLastPoint();
    if (drawPointCount.value > 0) {
      drawPointCount.value--;
    }
    // drawClear();
  }
}
/**
 * 合并图斑
 * @param feataure1 要素1
 * @param feataure2 要素2
 */
function mergeFeature(feataure1, feataure2, fillColor, strokeColor) {
  if (!feataure1 || !feataure2) return;
  return new Promise((resolve, reject) => {
    // 创建两个多边形
    let coor1, coor2;
    if (feataure1.type == "Feature" && feataure2.type == "Feature") {
      coor1 = feataure1.geometry.coordinates;
      coor2 = feataure2.geometry.coordinates;
    } else if (feataure1 instanceof Feature && feataure2 instanceof Feature) {
      coor1 = feataure1.getGeometry().getCoordinates();
      coor2 = feataure2.getGeometry().getCoordinates();
    }
    if (coor1.length == 1) {
      coor1 = coor1[0];
    }
    if (coor2.length == 1) {
      coor2 = coor2[0];
    }
    const polygon1 = turf.polygon(coor1);
    const polygon2 = turf.polygon(coor2);
    // 判断两个图斑是否相交
    const isIntersect = turf.booleanOverlap(polygon1, polygon2);
    if (isIntersect) {
      // 合并两个相交的图斑
      const mergedFeature = turf.union(turf.featureCollection([polygon1, polygon2]));
      // 创建一个新的图斑要素
      if (mergedFeature.geometry.type == "MultiPolygon") {
        reject("图斑经纬度存在问题，无法合并为一个图斑");
        ElMessage({
          message: "图斑经纬度存在问题，无法合并为一个图斑",
          type: "warning",
        });
        return;
      }
      const newMargeFeature = new Feature({
        geometry: new Polygon(mergedFeature.geometry.coordinates),
      });
      newMargeFeature.setStyle(
        new Style({
          //填充色
          fill: new Fill({
            color: fillColor || props.drawStyle.fillColor,
            fillOpacity: props.drawStyle.fillOpacity || 1,
          }),
          //边线颜色
          stroke: new Stroke({
            color: strokeColor || props.drawStyle.borderColor,
            width: props.drawStyle.width,
            strokeOpacity: props.drawStyle.strokeOpacity || 1,
          }),
        })
      );
      resolve(newMargeFeature);
    } else {
      reject("请选择相邻的图斑");

      ElMessage({
        message: "请选择相邻的图斑",
        type: "warning",
      });
    }
  });
}

function yc(a, c, d, e, f) {
  return f ? ((f[0] = a), (f[1] = c), (f[2] = d), (f[3] = e), f) : [a, c, d, e];
}

function Oc(a, c, d, e) {
  var f = (c * e[0]) / 2;
  e = (c * e[1]) / 2;
  c = Math.cos(d);
  var g = Math.sin(d);
  d = f * c;
  f *= g;
  c *= e;
  var h = e * g,
    k = a[0],
    m = a[1];
  a = k - d + h;
  e = k - d - h;
  g = k + d - h;
  d = k + d + h;
  h = m - f - c;
  k = m - f + c;
  var n = m + f + c;
  f = m + f - c;
  return yc(Math.min(a, e, g, d), Math.min(h, k, n, f), Math.max(a, e, g, d), Math.max(h, k, n, f), void 0);
}
/**
 *
 * @param coordinate 获取点击bbox
 * @param bbox bbox
 */
function getBbox(coordinate, bbox = [1, 1]) {
  return Oc(coordinate, getView().getResolution(), 0, bbox);
}
function wfs({ layerName, filter, condition = "and", bbox }) {
  //filter
  let _filters = [];
  filter?.forEach((item) => {
    if (item.type == "=" || !item.type) {
      _filters.push(equalTo(item.key, item.value));
    }
  });
  if (_filters.length > 1) {
    _filters = condition == "and" ? and(..._filters) : or(..._filters);
  } else {
    _filters = _filters[0];
  }
  const featureRequest = new WFS().writeGetFeature({
    srsName: "EPSG:4490",
    featureNS: "http://geoserver.org",
    featurePrefix: "osm",
    featureTypes: layerName,
    outputFormat: "application/json",
    filter: _filters,
    bbox: bbox,
    geometryName: "geom",
  });
  return new Promise((resolve, reject) => {
    fetch(props.wfsServer, {
      method: "POST",
      body: new XMLSerializer().serializeToString(featureRequest),
      headers: _layerAuthHeader.value,
    })
      .then(function (response) {
        return response.json();
      })
      .then((json) => {
        resolve(json);
      });
  });
}

function wfsPolygon(drawLayerArr, geom) {
  let layerName = drawLayerArr.map((x) => x.layer.layer.split(":")[1]);
  let wfsFilter = [];
  if (drawLayerArr[0].layer.wfs) {
    //wfsfilter
    drawLayerArr[0].layer.wfs.filter?.forEach((item) => {
      if (item.type == "=" || !item.type) {
        wfsFilter.push(equalTo(item.key, item.value));
      }
    });
    if (wfsFilter.length > 1) {
      wfsFilter = drawLayerArr[0].layer.wfs.condition == "and" ? and(...wfsFilter) : or(...wfsFilter);
    } else {
      wfsFilter = wfsFilter[0];
    }
  } else {
    let arr = drawLayerArr[0].layer.filter?.split(/(?:and|or)\b/gi).filter(Boolean);
    arr?.forEach((item) => {
      let [key, val] = item.split("=");
      wfsFilter.push(equalTo(key.trim(), val.trim()));
    });
  }
  const featureRequest = new WFS().writeGetFeature({
    srsName: "EPSG:4490",
    featureNS: "http://geoserver.org",
    featurePrefix: "osm",
    featureTypes: layerName,
    outputFormat: "application/json",
    filter: wfsFilter.length ? and(intersects("geom", geom), ...wfsFilter) : intersects("geom", geom),
    // geometryName: 'geom'
  });
  return new Promise((resolve, reject) => {
    fetch(props.wfsServer, {
      method: "POST",
      body: new XMLSerializer().serializeToString(featureRequest),
      headers: _layerAuthHeader.value,
    })
      .then(function (response) {
        return response.json();
      })
      .then((json) => {
        resolve(json);
      });
  });
}
/**
 * 添加边界图层
 */
function addBoundAreaLayer(list, styleObj = props.boundAreaStyle) {
  if (boundAreaVectorLayer) {
    map && map.removeLayer(boundAreaVectorLayer);
  }
  boundAreaVectorSource = new VectorSource();
  boundAreaVectorLayer = new VectorLayer({
    source: boundAreaVectorSource,
    id: "_boundAreaVectorSource",
    zIndex: 99,
    style: (feature) => {
      return new Style({
        stroke: new Stroke({
          color: styleObj.borderColor, // 描边
          width: styleObj.width, // 设置描边宽度为 1 像素
          strokeOpacity: props.drawStyle.strokeOpacity || 1,
        }),
        fill: new Fill({
          color: styleObj.fillColor, // 填充
          fillOpacity: props.drawStyle.fillOpacity || 1,
        }),
      });
    },
  });
  list.forEach((item) => {
    const features = new GeoJSON().readFeatures(item);
    boundAreaVectorSource.addFeatures(features);
  });
  if (map) {
    map.addLayer(boundAreaVectorLayer);
  }
}
/**
 * 高亮要素
 */
function highLightFeatures({ features, isEdit, highLightStyle, isFit = true }) {
  return new Promise((resolve, reject) => {
    if (!features || !features.length) {
      resolve();
      return;
    }
    highLightLayerClear();
    if (highLightStyle) {
      hightLightVectorLayer.setStyle(
        new Style({
          stroke: new Stroke({
            color: highLightStyle.borderColor,
            width: highLightStyle.width,
            strokeOpacity: highLightStyle.strokeOpacity || 1,
          }),
          fill: new Fill({
            color: highLightStyle.fillColor,
            fillOpacity: highLightStyle.fillOpacity || 1,
          }),
        })
      );
    }
    let arr = [];
    features.forEach((item, index) => {
      arr.push( item instanceof Feature?item:new GeoJSON().readFeature(item));
    });
    //选中--只能第一个
    if (isEdit && arr[0]) {
      hightLightVectorLayerEdit.value = true;
      currentSelectFeature = arr[0];
      //移除add
      _drawTool.value = _drawTool.value.filter((x) => x != "add");
      isFit && mapFit(currentSelectFeature.getGeometry().getExtent());
      searchVectorSource.clear();
      drawToolClick({ type: "choose", message: "已选中图斑" });
      searchVectorSource.addFeature(currentSelectFeature);
    } else {
      hightLightVectorSource.addFeatures(arr);
      isFit && mapFit(hightLightVectorSource.getExtent());
    }
    resolve(hightLightVectorLayer);
  });
}

/**
 * 通过sql查询高亮
 * @param wfsFilter [{type:"=",key:"",value:"}]
 * @param layerName 图层名 --如果只添加了一个图层，默认为添加的图层
 */
function highLightByFilter({ wfsFilter, layerName, loading, isEdit, highLightStyle, condition = "and", isFit = true }) {
  return new Promise((resolve, reject) => {
    let _layerName = layerName;
    if (!_layerName && layersInstance.length == 1) {
      _layerName = layersInstance[0].layer.layer?.split(":")?.[1];
    }
    if (!_layerName) {
      reject("未找到图层名");
      return;
    }
    if (loading) {
      _loading.value = true;
    }
    wfs({ layerName: _layerName instanceof Array ? _layerName : [_layerName], filter: wfsFilter, condition })
      .then((res) => {
        highLightFeatures({ features: res.features, isEdit, highLightStyle, isFit }).then((res) => {
          resolve(res);
        });
      })
      .finally(() => {
        _loading.value = false;
      });
  });
}
/**
 * 获取当前视窗经纬度
 */
function getViewBox() {
  return map.getView().calculateExtent(map.getSize());
}
/**
 * 高亮图层清空
 */
function highLightLayerClear() {
  hightLightVectorSource.clear();
}

/**
 * 切换图层--用于外部调用
 * @param group 分组
 */
function changeLayer(item, group = "group") {
  if (changePreLayer[group]) {
    removeLayer(changePreLayer[group]);
  }
  changePreLayer[group] = item;
  if (item) {
    addLayer(item);
  }
}
/**
 * 关闭弹框
 */
function closeModal() {
  highLightLayerClear();
  overlay.setPosition(undefined);
  isShowModal.value = false;
}
/**
 * 展示弹框
 */
function showModal() {
  modalShowFunc();
}
/**
 * wms图层过滤
 * @param sqpString
 * @param layer
 */
function filterWms(sqpString, layerName) {
  let layer;
  if (layerName) {
    layer = layersInstance.find((item) => item.layer.layer == layerName)?.layerInstance;
  }
  if (!layer) {
    layer = layersInstance[0]?.layerInstance;
  }
  if (!layer) {
    throw Error("找不到图层");
  }
  layer.getSource().updateParams({
    CQL_FILTER: sqpString || undefined,
  });
}
/**
 * 同步地图
 * @param mapId 地图id
 */
function setView(mapId) {
  if (!commonObj[mapId]) {
    throw Error("不存在此id的地图");
  }
  getMap().setView().commonObj[mapId].getView();
}

/**
 *
 * @param isShow 是否显示底图
 */
function isShowBaseLayer(isShow) {
  _isShowBaseLayer.value = isShow;
  if (isShow) {
    //底图
    if (_baseMapLayer.value) {
      baseMap.find((x) => x.get("id") == "_baseMap_" + _baseMapLayer.value)?.setVisible(true);
    }
    //添加标注
    if (_baseMapLayer.value && props.showBaseMapLayerText) {
      baseMap.find((x) => x.get("id") == "_baseMap_" + _baseMapLayer.value + "Text")?.setVisible(true);
    }
  } else {
    baseMap.forEach((item) => {
      item.setVisible(isShow);
    });
  }
}

/**
 * 绘制完成，将会禁用页面绘制功能
 */
function drawEnd(obj = { isShowDraw: false, drawMessage: "绘制完成" }) {
  _showDraw.value = obj.isShowDraw;
  drawMessage.value = obj.drawMessage;
}

function getLayersInstance() {
  return layersInstance;
}

/**
 *
 * @param feature 添加选中图斑
 */
function addChooseFeature(feature) {
  searchVectorSource.addFeature(feature);
}
/**
 *
 * @param feature 移除选中图斑
 */
function removeChooseFeature(feature) {
  searchVectorSource.removeFeature(feature);
}
defineExpose({
  getMap,
  getView,
  setCenter,
  setZoom,
  mapFit,
  drawToolClick,
  draw,
  drawSplit,
  drawClear,
  drawRevoke,
  getViewBox,
  addBoundAreaLayer,
  highLightFeatures,
  changeLayer,
  highLightByFilter,
  highLightLayerClear,
  closeModal,
  addLayer,
  removeLayer,
  removeAllLayer,
  filterWms,
  setView,
  isShowBaseLayer,
  drawEnd,
  showModal,
  getLayersInstance,
  closeMessage,
  addChooseFeature,
  removeChooseFeature,
});
</script>

<style lang="scss" scoped>
.map-container-map-box {
  position: relative;
  height: 100%;
  width: 100%;
  line-height: normal;
  .map-container-map {
    width: 100%;
    height: 100%;
  }
  .layer_box {
    // position: absolute;
    // z-index: 99;
    // top: 10px;
    // right: 10px;
    // max-height: calc(100% - 20px);
    // overflow-x: hidden;
    // box-shadow: 0 0 4px #b2aeae;
    // overflow: hidden;
    ::v-deep(){
      .el-menu {
        border-right: none;
      }
    }
  }
  .legend_box {
    position: absolute;
    z-index: 99;
    bottom: 10px;
    left: 10px;
    max-height: 100px;
    overflow: auto;
    padding: 0 10px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    &:not(:empty) {
      padding: 5px 10px;
    }
    .legend-item {
      display: flex;
      align-items: center;
      white-space: nowrap;
      padding: 2px 0;
      display: flex;
      overflow: hidden;
      .legend_name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .color-bg {
        width: 20px;
        height: 20px;
        margin: 2px 4px;
      }
    }
  }
  .draw_box {
    position: absolute;
    z-index: 99;
    top: 10px;
    left: 10px;
    .draw-item {
      background: #fff;
      font-size: 13px;
      width: 45px;
      height: 45px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      border-radius: 4px;
      cursor: pointer;
      position: relative;
      user-select: none;
      img {
        width: 20px;
        height: 20px;
      }
      .map_layer_box {
        font-size: 14px;
        position: absolute;
        background-color: #fff;
        color: #000;
        padding: 7px;
        left: 100%;
        margin-left: 12px;
        box-shadow: 0 0 4px #b2aeae;
        .flex_box {
          display: flex;
          justify-content: space-between;
        }

        .line {
          border-top: 1px solid #f0f0f0;
          margin: 10px 0;
        }

        .active {
          box-shadow: -1px -1px 2px #0079fe, 1px 1px 2px #0079fe;
        }

        .base_map_icon {
          width: 74px;
          height: 52px;
        }
      }
    }
    .disabledClick {
      filter: grayscale(1);
      color: #9a9a9a;
      cursor: not-allowed;
    }
  }
  .ol-popup-box {
    .ol-popup {
      position: absolute;
      transform: translate(-50%);
      bottom: 12px;
      background-color: white;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
      padding: 15px;
      border-radius: 10px;
      border: 1px solid #cccccc;
      min-width: 280px;
    }
    .ol-popup:after,
    .ol-popup:before {
      top: 100%;
      border: solid transparent;
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
    }
    .ol-popup:after {
      border-top-color: white;
      border-width: 10px;
      left: 50%;
      margin-left: -10px;
    }
    .ol-popup:before {
      border-top-color: #cccccc;
      border-width: 11px;
      left: 50%;
      margin-left: -11px;
    }
    .ol-popup-closer {
      text-decoration: none;
      position: absolute;
      top: 2px;
      right: 8px;
      color: #00aaff;
      cursor: pointer;
    }
    .ol-popup-closer:after {
      content: "✖";
    }
  }
  .draw_message_box {
    position: absolute;
    z-index: 99;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    border-radius: 5px;
    background: #00aaff;
    color: #fff;
    display: flex;
    align-items: center;
    i {
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss">
.legend-picker-popper {
  .el-color-dropdown__link-btn {
    display: none;
  }
}
.map-container-map-box {
  .ol-tooltip {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: white;
    padding: 4px 8px;
    opacity: 0.7;
    white-space: nowrap;
    font-size: 12px;
    cursor: default;
    user-select: none;
  }
  .ol-tooltip-measure {
    opacity: 1;
    font-weight: bold;
  }
  .ol-tooltip-static {
    background-color: #ffcc33;
    color: black;
  }
  .ol-tooltip-measure:before,
  .ol-tooltip-static:before {
    border-top: 6px solid rgba(0, 0, 0, 0.5);
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
    content: "";
    position: absolute;
    bottom: -6px;
    margin-left: -7px;
    left: 50%;
  }
  .ol-tooltip-static:before {
    border-top-color: #ffcc33;
  }
}
</style>

<template>
  <div>
    <div :id="`map_container${props.idNum}`" :style="{ height: props.height + 'px' }"></div>
  </div>
</template>
<script setup lang="jsx">
import XYZ from 'ol/source/XYZ';
import openlayerSdk from '../openLayerMap/openlayerSdk.js';
import { GeoJSON, WFS } from 'ol/format.js';
import { setValue } from '@/components/FuniBpmn/api/api';
import { and as andFilter, equalTo as equalToFilter, like as likeFilter,or } from 'ol/format/filter.js';
import { TileWMS, OSM, Vector as VectorSource } from 'ol/source';
import { Tile as TileLayer, Vector as VectorLayer, Image as ImageLayer } from 'ol/layer.js';
import { Style, Stroke, Fill } from 'ol/style';
import { ref, computed, nextTick, unref, onMounted, inject, watch } from 'vue';
import { ElNotification } from 'element-plus';
const props = defineProps({
  layerType: {},
  isEdit: {
    type: Boolean,
    default: true
  },
  height: {
    type: Number,
    default: 600
  },
  id: {
    type: Number || String,
    default: ''
  },
  idNum: {
    type: Number || String,
    default: ''
  }
});

const otherPatternIds = inject('otherPatternIds');
const emit = defineEmits(['exportMap']);
// 表单查询
const map = ref(null);
//图斑
let polygnLayer;
//矢量图层
let vectorLayer;
//初始化地图
let funiMapSdk;
let tunderLayer;
const initMap = e => {
  // map.value && map.value.destroy()
  // map.value = null
  funiMapSdk = new openlayerSdk();
  funiMapSdk.init(`map_container${props.idNum}`);
  //天地图图层
  // funiMapSdk.addTileLayer({
  //   source: new XYZ({
  //     url: 'http://t0.tianditu.com/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=16efebb5258f64b86de89851ebcca46c'
  //   }),
  //   name: '天地图影像图层'
  // });
  tunderLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/fragment/wms',
    url: '/tdgeo/fragment/wms',
    params: { LAYERS: 'fragment:img_ljc' },
    ratio: 1,
    serverType: 'geoserver'
  });
  polygnLayer = funiMapSdk.addImageWMS({
    // url: '/geoserver/cyheal/wms',
    url: '/ggo/cyheal/wms',
    params: { LAYERS: props.layerType },
    ratio: 1,
    serverType: 'geoserver',
  });
  vectorLayer = funiMapSdk.addVectorLayer();
  map.value = funiMapSdk.getMap();
  //回显选中的图斑 当前的
  if (props.id) {
    getBusinessEntity();
  }
  //回显选中的图斑 其他的
  if (otherPatternIds.value && otherPatternIds.value.length > 0) {
    getBusinessEntityOther();
  }
  //回显选中的图斑 其他被选中的
  map.value.on('singleclick', function (evt) {
    if (!props.isEdit) return; //查看 不可编辑图斑

    var viewResolution = map.value.getView().getResolution();
    var url = polygnLayer
      .getSource()
      .getFeatureInfoUrl(evt.coordinate, viewResolution, 'EPSG:4326', { INFO_FORMAT: 'application/json' });
    if (url) {
      fetch(url)
        .then(response => {
          return response.text();
        })
        .then(data => {
          let jsonData = JSON.parse(data);
          //不能点击重复的
          if (otherPatternIds.value && otherPatternIds.value.length > 0) {
            const { properties } = jsonData.features[0];
            if (otherPatternIds.value.includes(properties.id)) {
              ElNotification({
                title: '提示',
                message: '图斑不能重复关联！',
                type: 'warning'
              });
              return;
            }
          }
          funiMapSdk.renderRenderVectorLayer(vectorLayer, jsonData);
          emit('exportMap', jsonData.features);
        });
    }
  });
};

const getBusinessEntity = e => {
  let featureTypes = [];
  featureTypes.push(props.layerType.split(':')[1]);
  const featureRequest = new WFS().writeGetFeature({
    srsName: 'EPSG:4490',
    featureNS: 'http://geoserver.org',
    featurePrefix: 'osm',
    featureTypes,
    outputFormat: 'application/json',
    filter: equalToFilter('id', e || props.id, true)
  });
  fetch('/geoserver/cyheal/wfs', {
    method: 'POST',
    body: new XMLSerializer().serializeToString(featureRequest)
  })
    .then(function (response) {
      return response.json();
    })
    .then(function (json) {
      const features = new GeoJSON().readFeatures(json);
      funiMapSdk.renderRenderVectorLayer(vectorLayer, json);
      map.value.getView().fit(vectorLayer.getSource().getExtent(), { maxZoom: 18 });
    })
    .catch(error => {
      console.log('error----------');
    });
};

const getBusinessEntityOther = () => {
  let vectorLayerOTher = funiMapSdk.addVectorLayer(
    new Style({
      stroke: new Stroke({
        color: 'blue',
        width: 3
      })
    })
  );
  let featureTypes = [];
  featureTypes.push(props.layerType.split(':')[1]);
  const filters = otherPatternIds.value.map(value => equalToFilter('id', value));
  const featureRequest = new WFS().writeGetFeature({
    srsName: 'EPSG:4490',
    featureNS: 'http://geoserver.org',
    featurePrefix: 'osm',
    featureTypes,
    outputFormat: 'application/json',
    filter:otherPatternIds.value.length > 1 ? or(...filters) : equalToFilter('id', otherPatternIds.value[0])
  });
  fetch('/ggo/cyheal/wfs', {
    method: 'POST',
    body: new XMLSerializer().serializeToString(featureRequest)
  })
    .then(function (response) {
      return response.json();
    })
    .then(function (json) {
      const features = new GeoJSON().readFeatures(json);
      funiMapSdk.renderRenderVectorLayer(vectorLayerOTher, json, false);
    })
    .catch(error => {
      console.log('error----------');
    });
};

onMounted(() => {
  initMap();
});
defineExpose({
  getBusinessEntity
});
</script>

<style scoped></style>

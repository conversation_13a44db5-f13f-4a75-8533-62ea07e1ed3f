package cn.fight.village.domain.data.api;

import cn.fight.village.domain.data.service.DataManageService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 数据处理接口层
 */
//@RestController
//@RequestMapping("dataManage")
public class DataManageApi {

    @Resource
    private DataManageService dataManageService;

    /**
     * 户信息导入
     *
     * @param multipartFile 户信息Excel
     * @return
     */
    @RequestMapping("household")
    public String householdParse(@RequestParam MultipartFile multipartFile) throws Exception {
        if (multipartFile == null) {
            return "文件解析为空";
        }

        return dataManageService.householdParse(multipartFile);
    }

    /**
     * 承包地信息导入
     *
     * @param multipartFile
     * @return
     * @throws Exception
     */
    @RequestMapping("land")
    public String LandParse(@RequestParam MultipartFile multipartFile) throws Exception {
        if (multipartFile == null) {
            return "文件解析为空";
        }

        return dataManageService.landParse(multipartFile);
    }

    /**
     * 承包地档案文件处理
     *
     * @return
     */
    @RequestMapping("landDoc")
    public String landDocMag(){
        return dataManageService.landDocMag();
    }

    /**
     * 敏感字段加密
     * @return
     */
    //@GetMapping("encode")
    public String encode(){
        //return dataManageService.encode();
        return null;
    }

    /**
     * 真实土地关联
     * @return
     */
    @GetMapping("realLandRela")
    public String realLandRela(){
        return dataManageService.realLandRelation();
    }
}

<script lang="jsx">
import { unref, ref, watch } from 'vue';
import { useRenderMenuItem } from './useRenderMenuItem';
import { useRouter } from 'vue-router';
import { useLayoutStore } from '@/layout/useLayoutStore';
import menus from '@/router/route.js';

export default {
  setup(props) {
    const layoutStore = useLayoutStore();
  
    const { push } = useRouter();

    const defaultActive = ref('');

    const menuSelect = (index, indexPath, item) => {
      if(index=="mir"){
        window.open(location.origin+location.pathname+"#/mir")
      }
      else{
        push({name:index})
      }
    };

    watch(
      () => layoutStore.activeMenu,
      newMenu => !!newMenu && (defaultActive.value = newMenu.id),
      { immediate: true }
    );

    return () => (
      <div class="funi-layout-nav-menu">
        <el-menu
          defaultActive={unref(defaultActive)}
          uniqueOpened={false}
          collapseTransition={false}
          onSelect={menuSelect}
          mode="horizontal"
        >
          {{
            default: () => {
              const { renderMenuItem } = useRenderMenuItem();
              return renderMenuItem(unref(menus));
            }
          }}
        </el-menu>
      </div>
    );
  }
};
</script>

<style lang="scss" scoped src="@/layout/styles/nav_menu.scss"></style>

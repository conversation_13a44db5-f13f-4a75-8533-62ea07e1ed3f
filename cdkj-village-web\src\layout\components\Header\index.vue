<!--
 * @Author: tao.yang <EMAIL>
 * @Date: 2023-10-12 13:54:57
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-10-13 17:48:49
 * @FilePath: /funi-cloud-web-gsbms/src/layout/components/Header/index.vue
 * @Description:
 * Copyright (c) 2023 by tao.yang <EMAIL>, All Rights Reserved.
-->

<template>
  <header class="funi-layout-header">
    <ToolBarLeft :systemName="systemName" :redirection="redirection" :logoPath="logoPath" />
    <NavMenu class="flex-grow-1" v-if="isHorizontal" />
    <ToolBarRight class="flex-shrink-0" :userMenuOption="userMenuOption" :pubUser="pubUser" />
  </header>
</template>

<script setup>
import { computed } from 'vue';
import NavMenu from './NavMenu/index.vue';
import ToolBarLeft from './ToolBarLeft/index.vue';
import ToolBarRight from './ToolBarRight/index.vue';
import { useLayoutStore } from '@/layout/useLayoutStore';

defineOptions({ name: 'LayoutHeader' });

const props = defineProps({
  systemName: String,
  logoPath: { type: String, default: './logo.png' },
  redirection: String,
  userMenuOption: { type: Object, default: () => ({}) },
  pubUser: Boolean
});

const store = useLayoutStore();
const isHorizontal = computed(() => store.layoutMode === 'horizontal');
</script>

<style lang="scss" scoped src="@/layout/styles/header.scss"></style>

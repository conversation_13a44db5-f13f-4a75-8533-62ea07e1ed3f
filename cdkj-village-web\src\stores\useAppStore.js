import { defineStore } from 'pinia';
import { reactive, ref } from "vue"

export const useAppStore = defineStore('main_app', () => {
    const platformConfig = reactive({
        platformLogo: "./staticapp/logo.png"
    })
    const systemName = ref("乡村资源智慧管理系统")
    const loading = ref(false)

    function setLoading(value) {
        loading.value = value
    }
    return {
        platformConfig,
        systemName,
        loading,
        setLoading
    }
})
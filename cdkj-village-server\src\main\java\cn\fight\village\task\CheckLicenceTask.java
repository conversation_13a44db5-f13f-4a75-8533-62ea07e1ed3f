package cn.fight.village.task;

import cn.fight.village.domain.common.util.SecureUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 检查许可证有效状态任务
 */
@Slf4j
@Component
@EnableScheduling
public class CheckLicenceTask {

    private static Boolean licenceAvailable;

    public static Boolean getLicenceAvailable() {
        return licenceAvailable;
    }

    public static void setLicenceAvailable(Boolean isAvailable) {
         licenceAvailable = isAvailable;
    }

    @Scheduled(cron = "0 0 1/5 * * ?")
    public void testScheduleTask() {
       log.info("=================开始检查应用许可证==================");
        if (!SecureUtils.checkLicence()) {
            licenceAvailable = Boolean.FALSE;
        }
        log.info("=================检查应用许可证完成==================");
    }
}

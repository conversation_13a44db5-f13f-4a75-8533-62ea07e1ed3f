@use 'element-plus/theme-chalk/src/mixins/mixins' as el;

@use '@/styles/mixins/functions.scss' as *;
@use '@/styles/mixins/utils' as *;
@use './var.scss';

@include b(layout-aside) {
  @include el.set-css-var-value('menu-bg-color', var(--aside-menu-bg-color));
  @include el.set-css-var-value('menu-hover-bg-color', var(--aside-menu-hover-bg-color));

  @include el.set-css-var-value('menu-active-color', var(--aside-menu-active-color));
  @include el.set-css-var-value('menu-sub-item-height', var(--aside-menu-sub-item-height));
  @include el.set-css-var-value('menu-item-height', var(--aside-menu-item-height));
  @include el.set-css-var-value('menu-base-level-padding', 12px);

  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: getCssVar('aside-menu', 'max-width');
  height: 100%;
  border: 1px solid var(--aside-menu-bg-color);
  background-color: getCssVar('aside-menu', 'bg-color');
  transition: width var(--transition-time-02);

  &--collapse {
    width: getCssVar('aside-menu', 'min-width');
  }
}

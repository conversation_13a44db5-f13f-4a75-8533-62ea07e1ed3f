<template>
    <funi-curd-v2  :columns="columns" :data="dataList" :pagination="false"></funi-curd-v2>
</template>

<script setup lang="jsx">
import { ref } from 'vue';
import { contractQueryByHousehold } from '@/apps/api/household.js'
import { useRoute,useRouter } from 'vue-router';

const props = defineProps({
    type:{
        default:"家庭承包"
    }
})

const { id } = useRoute().query;
const router = useRouter()
if(id){
    contractQueryByHousehold({householdId:id,type:props.type}).then(res=>{
        dataList.value = res
    })
}
const columns = ref([
{
      label: '地块编号',
      prop: 'landNo',
      render: ({ row, index }) => {
        return (
            <el-button type="primary" link onClick={()=>toPage(row,index)}>
              {row.landNo}
            </el-button>
        )
      }
    },
    {
      label: '地块实测面积（㎡）',
      prop: 'area'
    },
    { label: '是否承包地', prop: 'contract' },
    { label: '土地类型', prop: 'landType' },
    { label: '种植情况', prop: 'farming' },
    { label: '是否流转', prop: 'transfer' },
])
const dataList =ref([])

function toPage(row){
    if(props.type == "家庭承包"){
        router.push({
            name:"ContractDetail",
            query:{
                id:row.uuid,
                title: '家庭承包信息详情',
                bizName: '详情',
                tab: `家庭承包信息-${row.contractCretNo}-详情`,
            }
        })
    }
    else{
        router.push({
            name:"WanderContractDetail",
            query:{
                id:row.uuid,
                title: '土地流转管理详情',
                bizName: '详情',
                tab: `土地流转管理-${row.contractCretNo}-详情`,
            }
        })
    }
}
</script>

<style lang="scss" scoped>

</style>
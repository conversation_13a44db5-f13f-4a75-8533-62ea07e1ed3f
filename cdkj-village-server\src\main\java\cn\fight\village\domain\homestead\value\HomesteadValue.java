package cn.fight.village.domain.homestead.value;

import cn.fight.village.domain.household.vo.HouseMember;
import com.fasterxml.jackson.databind.ser.Serializers;

import java.util.List;

/**
 * 宅基地信息视图对象
 *
 */
public class HomesteadValue extends Serializers.Base {
    //关联家庭主键
    private String household;

    //宅基地编号
    private String code;

    //面积
    private Double area;

    //土地类型
    private String landType;

    //是否取证
    private String hasCert;

    //证书编号
    private String certNo;

    //未取证原因
    private String noCertReason;

    //楼层
    private String floor;

    //结构
    private String structure;

    //使用情况
    private String usage;

    //东
    private String east;

    //西
    private String west;

    //南
    private String south;

    //北
    private String north;

    //备注
    private String remark;

    private List<HouseMember> houseMembers;

    public String getHousehold() {
        return household;
    }

    public void setHousehold(String household) {
        this.household = household;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public String getLandType() {
        return landType;
    }

    public void setLandType(String landType) {
        this.landType = landType;
    }

    public String getHasCert() {
        return hasCert;
    }

    public void setHasCert(String hasCert) {
        this.hasCert = hasCert;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getNoCertReason() {
        return noCertReason;
    }

    public void setNoCertReason(String noCertReason) {
        this.noCertReason = noCertReason;
    }

    public String getFloor() {
        return floor;
    }

    public void setFloor(String floor) {
        this.floor = floor;
    }

    public String getStructure() {
        return structure;
    }

    public void setStructure(String structure) {
        this.structure = structure;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getEast() {
        return east;
    }

    public void setEast(String east) {
        this.east = east;
    }

    public String getWest() {
        return west;
    }

    public void setWest(String west) {
        this.west = west;
    }

    public String getSouth() {
        return south;
    }

    public void setSouth(String south) {
        this.south = south;
    }

    public String getNorth() {
        return north;
    }

    public void setNorth(String north) {
        this.north = north;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<HouseMember> getHouseMembers() {
        return houseMembers;
    }

    public void setHouseMembers(List<HouseMember> houseMembers) {
        this.houseMembers = houseMembers;
    }
}

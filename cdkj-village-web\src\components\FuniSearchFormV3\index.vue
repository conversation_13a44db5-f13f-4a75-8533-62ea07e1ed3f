<template>
  <div shadow="never" class="card">
    <search-form
      ref="formRef"
      v-bind="$attrs"
      :col="colNumber"
      :schema="computedSchema"
      :inline="false"
      :expand="expand"
      @keyup.enter.native="search"
      @change="search"
    >
      <template v-for="(_, slot) in $slots" #[slot]="params">
        <slot :name="slot" v-bind="params"></slot>
      </template>
      <template #toolbar="{ item }">
        <div class="w-full flex" :class="toolbarAlignClass">
          <el-button type="primary" @click="search"> 查询</el-button>
          <el-button @click="reset"> 重置</el-button>
          <el-link v-if="item.expandable" type="primary" :underline="false" class="ml-19px" @click="setVisible">
            {{ expand ? '收起' : '高级查询' }}
          </el-link>
        </div>
      </template>
    </search-form>
  </div>
</template>

<script setup lang="jsx">
import { ref, computed, unref, provide } from 'vue';
import { cellRender } from './cell-render.jsx';
import SearchForm from './form.vue';

defineOptions({
  name: 'FuniSearchFormV3',
  inheritAttrs: false
});

const emit = defineEmits(['search', 'reset']);
const props = defineProps({
  colNumber: { type: Number, default: 4 },
  queryFields: { type: Array, default: () => [] }
});

provide($utils.symbols.searchForm, { getFilters });

const formRef = ref();
const expand = ref(false);
const colNumber = props.colNumber;

const operateMap = computed(() => {
  const map = new Map();
  props.queryFields.forEach(item => {
    const operateOptions = item.operateOptions || [];
    const operate = (operateOptions || [])[0] || { value: '' };
    !!operate.value && map.set(item.column, operate.value);
  });
  return map;
});

/**
 * 1.每行固定4列
 * 2.第一行最后一列固定为操作按钮
 * 3.默认展开高级查询
 */
const computedSchema = computed(() => {
  const schema = props.queryFields.map((item, index) => {
    let type = item.type.toUpperCase();
    const operate = operateMap.value.get(item.column) || '';
    const attribute = item.attribute || {};

    if (type === 'NUMBER' && operate === 'BETWEEN') {
      type = 'NUMBER_RANGE';
    }

    const renderName = type === 'SLOT' ? attribute.key || item.column : type;
    const render = cellRender.get(renderName);

    return {
      label: item.name,
      prop: item.column,
      component: ({ item, formModel, value }) => {
        // console.log('formModel', formModel);
        return !!render ? render(item, formModel, value) : '';
      },
      hidden: index > colNumber - 2 && !unref(expand),
      props: {
        column: item.column,
        operate: operate,
        attribute: attribute
      }
    };
  });
  const toolbarSchema = {
    prop: 'toolbar',
    labelHidden: false,
    expandable: schema.length > colNumber - 1,
    slots: { default: 'toolbar' }
  };
  schema.splice(schema.length, 0, toolbarSchema);
  return schema;
});

const toolbarAlignClass = computed(() => {
  return !expand.value && props.colNumber < 4 ? 'justify-start' : 'justify-end';
});

const search = async () => {
  try {
    await unref(formRef).validate();
    const filters = getFilters();
    emit('search', !!filters.length ? { filters } : {});
  } catch (error) {}
};

function getFilters() {
  const values = unref(formRef).getValues();
  return Object.entries(values)
    .filter(([key, value]) => {
      if (!(!!key && !!value && !!operateMap.value.get(key))) return false;
      if ($utils.isArray(value)) return !!value.filter(i => !!i).length;
      return true;
    })
    .map(([key, value]) => {
      const newItem = {
        column: key,
        operate: operateMap.value.get(key) || '',
        value: value
      };
      if ($utils.isArray(value)) {
        const field = props.queryFields.find(item => item.column === key);
        const type = field.type.toUpperCase();
        if (type === 'NUMBER_RANGE') {
          Object.assign(newItem, { value: [value[0] || '0', value[1] || '9999999999'].join(',') });
        } else {
          Object.assign(newItem, { value: value.filter(i => !!i).join(',') });
        }
      }
      return newItem;
    });
}

const reset = async () => {
  unref(formRef).resetFields();
  emit('reset');
};

const setVisible = () => {
  expand.value = !unref(expand);
};
</script>

<style lang="less" scoped>
.card {
  padding: 2px 16px;
}
.funi-search-form__card {
  :deep(.el-card__body) {
    padding-bottom: 2px;
  }
}
</style>

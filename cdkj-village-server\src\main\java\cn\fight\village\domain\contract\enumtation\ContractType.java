package cn.fight.village.domain.contract.enumtation;

/**
 * 合同类型
 *
 */
public enum ContractType {
    FAMILY_CONTRACT ("家庭承包","家庭承包"),
    LAND_TRANS("土地流转","土地流转");

    private String name;
    private String code;

    ContractType(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}

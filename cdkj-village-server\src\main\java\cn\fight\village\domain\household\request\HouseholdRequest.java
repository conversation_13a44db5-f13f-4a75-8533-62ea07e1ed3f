package cn.fight.village.domain.household.request;

import cn.fight.village.domain.common.entity.BaseRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * 家庭与户信息添加
 *
 */
public class HouseholdRequest extends BaseRequest {
    //户编号
    private String householdCode;

    //位置
    private String location ;

    //是否贫困户
    private String poor;

    //是否低保户
    private String lower ;

    //备注
    private String backup;

    //家庭成员
    private List<HouseholdMemberRequest> members = new ArrayList<>();

    public String getHouseholdCode() {
        return householdCode;
    }

    public void setHouseholdCode(String householdCode) {
        this.householdCode = householdCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPoor() {
        return poor;
    }

    public void setPoor(String poor) {
        this.poor = poor;
    }

    public String getLower() {
        return lower;
    }

    public void setLower(String lower) {
        this.lower = lower;
    }

    public String getBackup() {
        return backup;
    }

    public void setBackup(String backup) {
        this.backup = backup;
    }

    public List<HouseholdMemberRequest> getMembers() {
        return members;
    }

    public void setMembers(List<HouseholdMemberRequest> members) {
        this.members = members;
    }
}

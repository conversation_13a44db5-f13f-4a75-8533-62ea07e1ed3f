import { useAppStore } from '@/stores/useAppStore';
import router from '@/router/index.js';

import env from '@/utils/env';
const filePath = `./${env.platform || 'paas'}.js`;
const modules = import.meta.glob(['./*.js', '!./index.js'], { eager: true });
const module = modules[filePath].default;

router.beforeResolve(to => {
  const appStore = useAppStore();
  document.title = [to.meta.title || '', appStore.systemName].filter(i => !!i).join(' - ');
});

router.beforeEach(module.beforeEachHandler);

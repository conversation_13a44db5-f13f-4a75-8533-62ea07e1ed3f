<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-23 10:34:26
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-02 16:43:32
 * @FilePath: /funi-paas-cs-web-cli/src/components/FuniRUOCLowCode/index.vue
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
-->
<template>
  <div
    :style="{
      width: '100%',
      '--funi-ruoc-lc-border-color': 'rgba(242, 246, 252, 1)',
      '--mode-border-radius': config.mode === 'multiple' ? 'var(--el-checkbox-border-radius)' : '50%'
    }"
  >
    <div ref="funiRuocLc" class="el-select funi-ruoc-lc" @click="ruocClick">
      <div class="ruoc-value" v-if="config.mode === 'multiple'">
        <div v-for="(item, index) in valueList" :key="item.id" class="itemBlock">
          <span class="item-icon"> <funi-icon :icon="iconClass[item.type]" /> </span>
          <span style="line-height: 14px">{{ item.name }}</span>
          <span v-if="!readonly && !disabled" class="delItem" @click.stop="delItem(item, index)">
            <funi-icon icon="iconamoon:close-thin" />
          </span>
        </div>
      </div>
      <div v-else style="padding-left:10px;">
        {{ valueList.length ? valueList[0].name : '' }}
      </div>
      <span class="el-input__suffix" v-if="!readonly && !disabled">
        <span class="el-input__suffix-inner">
          <i class="el-icon el-select__icon icon_clear clear_ruoc" style="margin-left: 0" @click.stop="clearValue">
            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-ea893728="">
              <path
                fill="currentColor"
                d="m466.752 512-90.496-90.496a32 32 0 0 1 45.248-45.248L512 466.752l90.496-90.496a32 32 0 1 1 45.248 45.248L557.248 512l90.496 90.496a32 32 0 1 1-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 0 1-45.248-45.248L466.752 512z"
              ></path>
              <path
                fill="currentColor"
                d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768zm0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896z"
              ></path>
            </svg>
          </i>
          <i class="el-icon el-select__caret el-select__icon icon_default">
            <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
              <path
                fill="currentColor"
                d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"
              ></path>
            </svg>
          </i>
        </span>
      </span>
    </div>
    <!-- <ChooseDialog ref="dialog" :value="valueList" :propsConfig="config" @updateValue="updateValue"></ChooseDialog> -->
    <ChooseDialog
      v-if="showDialog && !readonly && !disabled"
      ref="dialog"
      :value="valueList"
      :propsConfig="config"
      @updateValue="updateValue"
    ></ChooseDialog>
    <Select
      v-else-if="!showDialog && !readonly && !disabled"
      :selectRef="funiRuocLc"
      :value="valueList"
      :propsConfig="config"
      @updateValue="updateValue"
    ></Select>
  </div>
</template>
<script setup>
import { ref, watch, computed } from 'vue';
import { iconClass } from './hooks/config.js';
import ChooseDialog from './dialog/index.vue';
import Select from './select/index.vue';
import { useFormItem } from 'element-plus';
const formTiem = useFormItem();
const emit = defineEmits(['update:modelValue']);
const dialog = ref();
const valueList = ref([]);
const funiRuocLc = ref();
const props = defineProps({
  modelValue: {
    type: Array,
    default: []
  },
  propsConfig: {
    type: Object,
    default: {
      type: 'org',
      mode: 'multiple',
      // mode: 'radio',
      allowedOrg: [
        // '123', '255a5568-4fec-4120-9700-cf55b555819b', '1552915274506743809', '1552915285533569026'
      ],
      allowedRole: [
        // '905fd6c8-cf23-4a98-9a79-cd4313f7f6f9',
        // 'a407499f-2bc5-4564-83b6-d6349d5f9999',
        // '73100eb8-30fd-454b-8e50-0cfa8dbfdc2d'
      ],
      allowedSelf: true
    }
  },
  readonly: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const config = computed(() => {
  return Object.assign(
    {
      type: 'user',
      mode: '',
      allowedOrg: [],
      allowedRole: [],
      allowedSelf: true
    },
    props.propsConfig
  );
});

const showDialog = computed(() => {
  let { allowedOrg, allowedRole } = config.value;
  if ((allowedOrg && allowedOrg.length) || (allowedRole && allowedRole.length)) {
    return true;
  }
  return false;
});

const ruocClick = () => {
  if (props.readonly || props.disabled) return;
  let title = config.value.type === 'user' ? '选择人员' : config.value.type === 'org' ? '选择部门' : '';
  dialog.value &&
    dialog.value.show({
      title
    });
};

const delItem = (item, index) => {
  valueList.value.splice(index, 1);
  !valueList.value.length && (valueList.value = void 0);
  emit('update:modelValue', valueList.value);
  _validate();
};
const updateValue = list => {
  valueList.value = list;
  emit('update:modelValue', list);
  _validate();
};

const setModeValue = () => {
  if (config.value.mode !== 'multiple' && valueList.value && valueList.value.length) {
    valueList.value = valueList.value.slice(-1);
  }
};

const clearValue = () => {
  updateValue(void 0);
};
const _validate = () => {
  formTiem?.formItem?.validate();
};

watch(
  () => props.modelValue,
  newVal => {
    valueList.value = newVal;
    setModeValue();
  },
  {
    immediate: true,
    deep: true
  }
);
watch(
  () => config.value,
  () => {
    setModeValue();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<style scoped>
@import url('./style/itemBlock.css');
.funi-ruoc-lc {
  width: max(240px, 100%);
  min-height: 32px;
  padding: 0 3px;
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.funi-ruoc-lc > div {
  width: calc(100% - 30px);
}
.clear_ruoc {
  display: none;
}
.funi-ruoc-lc:hover .clear_ruoc {
  display: inline-block;
}
.ruoc-value {
  min-height: 32px;
  /* overflow-y: auto; */
  width: 100%;
  display: flex;
  box-sizing: border-box;
  padding: 3px;
  justify-content: flex-start;
  align-content: flex-start;
  flex-wrap: wrap;
  gap: 5px;
}

.el-form-item.is-error .funi-ruoc-lc {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}
.el-input__suffix {
  padding-right: 12px;
}
</style>

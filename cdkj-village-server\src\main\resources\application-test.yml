#日志级别
logging:
  level:
    root: error

spring:
  #数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ******************************************
    username: postgres
    password: fight@postgres@2025
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000

#系统业务配置参数
business:
  file-store-path: /soft/file_store/
  env: test
  #licence: lOo58U9xn5S6XdHsSJCxIOoIylDrCqQYaWimKqqUjdBRABim9ARUj91KMSkWVTKdb60wr8OyQoLVmxS7S8E3gFq+X8fSCr2JqcvPzc3hJIzaluDP7JJXbTSxFfdiuUfAe/fkc5ork6mkTjWPS3//9XMFO4RK6h7U0ST1jEeNqxk=
  licence: YMxZQnfSEB9BInDDfuu555CiHhMhBQ7UCitUbV560kWdjVG9CzUEaJy2QzGJapnLFY757ABWmqntzLfB7BDazwtJ4hBnAaAbLVoG+9w7n1a5+9he6NrvgJFSbZqRCN1fJ1TtcBpqy7sPofyFoah/v5Ftw/C8mQaQzbbCEwlvlbw=
  documents_path: /usr/local/tomcat9/webapps/documents
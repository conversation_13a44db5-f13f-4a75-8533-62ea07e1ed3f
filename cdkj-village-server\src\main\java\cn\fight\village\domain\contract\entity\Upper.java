package cn.fight.village.domain.contract.entity;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 发包方信息
 *
 */
@TableName("public.rlams_upper")
public class Upper extends BaseEntity {
    //合同ID
    private String contractId;

    //发包方编号
    private String upperNo;

    //发包方名称
    private String upper;

    //发包方负责人名
    private String upperName;

    //发包方负责人证件号
    @Sensitive
    private String upperIdNo;

    //发包方负责人证件类型
    private String upperIdType;

    //发包方负责人联系电话
    @Sensitive
    private String upperPhone;

    //联系地址
    @Sensitive
    @TableField(exist = false)
    private String upperLocation;

    //备注
    private String remark;

    public String getUpperLocation() {
        return upperLocation;
    }

    public void setUpperLocation(String upperLocation) {
        this.upperLocation = upperLocation;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    public String getUpperNo() {
        return upperNo;
    }

    public void setUpperNo(String upperNo) {
        this.upperNo = upperNo;
    }

    public String getUpper() {
        return upper;
    }

    public void setUpper(String upper) {
        this.upper = upper;
    }

    public String getUpperName() {
        return upperName;
    }

    public void setUpperName(String upperName) {
        this.upperName = upperName;
    }

    public String getUpperIdNo() {
        return upperIdNo;
    }

    public void setUpperIdNo(String upperIdNo) {
        this.upperIdNo = upperIdNo;
    }

    public String getUpperIdType() {
        return upperIdType;
    }

    public void setUpperIdType(String upperIdType) {
        this.upperIdType = upperIdType;
    }

    public String getUpperPhone() {
        return upperPhone;
    }

    public void setUpperPhone(String upperPhone) {
        this.upperPhone = upperPhone;
    }
}

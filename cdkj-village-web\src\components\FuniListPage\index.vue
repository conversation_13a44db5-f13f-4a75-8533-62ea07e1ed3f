<template>
  <funi-teleport to=".layout-content__wrap" :disabled="!teleported">
    <el-tabs v-model="activeTab" :class="['funi_list_page', 'bg-white', { hide_tab: hideTab, teleported }]">
      <el-tab-pane :label="item.label" :name="item.key" v-for="(item, index) in cardTab" :key="item.key">
        <!-- <div class="search_box" v-if="item.showSearch !== false">
        <funi-search @output="searchOutput" />
      </div> -->
        <funi-curd
          v-on="item.curdOption.on || {}"
          :loading="loading[index]"
          :lodaData="
            item.api || item.curdOption.api ? (page, searchParams = {}) => getData(item, page, searchParams) : undefined
          "
          ref="curd"
          :isShowSearch="isShowSearch"
          :searchConfig="getSearchConfig()"
          :actionsProps="{ disabled: activeTab !== item.key }"
          v-bind="item.curdOption"
          :reloadOnActive="false"
          @beforeRequest="beforeRequest"
          @afterRequest="afterRequest"
          @requestError="requestError"
          v-if="!item.slot"
        >
          <!-- 表格标题 -->
          <template #header v-if="item.curdOption.header">
            <component :is="contentRender(item.curdOption.header)" />
          </template>
          <!-- 表格按钮 -->
          <template #buttonGroup="scope">
            <template v-for="btn in item.curdOption.btns">
              <el-button
                v-if="!btn.component"
                type="primary"
                v-bind="btn"
                v-auth="btn.auth"
                :key="btn.key"
                @click="headBtnClick(btn)"
              >
                {{ btn.label }}
              </el-button>
              <component v-else :is="btn.component()"></component>
            </template>
          </template>
          <!-- 表格列头插槽 -->
          <template
            #[column.slots.header]="scope"
            v-for="column in item.curdOption.columns.filter(x => x.slots?.header)"
          >
            <slot :name="column.slots.header" v-bind="scope"></slot>
          </template>
          <!-- 表格列插槽 -->
          <template
            #[column.slots.default]="scope"
            v-for="column in item.curdOption.columns.filter(x => x.slots?.default)"
          >
            <slot :name="column.slots.default" v-bind="scope"></slot>
          </template>
          <!-- 空数据插槽 -->
          <template #empty>
            <slot name="empty"></slot>
          </template>
          <!-- append插槽 -->
          <template #append>
            <slot name="append"></slot>
          </template>
          <template #pagination_extra="params">
            <component
              v-if="!!item.curdOption.paginationExtra"
              :is="contentRender(item.curdOption.paginationExtra(params))"
            ></component>
          </template>
          <template v-for="(_, slot) in $slots" #[slot]="params">
            <slot :name="slot" v-bind="params || {}" />
          </template>
        </funi-curd>
        <slot v-else :name="item.slot"></slot>
      </el-tab-pane>
    </el-tabs>
  </funi-teleport>
</template>
<script setup lang="jsx">
import { computed, reactive, ref, isVNode, watch, nextTick, onActivated } from 'vue';

const emit = defineEmits(['headBtnClick', 'beforeRequest', 'afterRequest', 'requestError']);
const props = defineProps({
  cardTab: {
    type: Array,
    default() {
      return [];
    }
  },
  isShowSearch: { type: Boolean, default: true },
  active: { type: String },
  showTab: {
    type: Boolean
  },
  teleported: { type: Boolean, default: true },
  reloadOnActive: Boolean
});
//data
const activeTab = ref(props.active || props.cardTab[0]?.key);
//curd实例
let curd = ref();
let loading = reactive([]);
let skipReloadCount = 1;

//是否隐藏tab
const hideTab = computed(() => {
  if (props.cardTab.length > 1) {
    return props.showTab !== false;
  }
  return props.showTab !== true;
});

const activeCurdRef = computed(() => {
  return curd.value?.[props.cardTab.findIndex(x => x.key == activeTab.value)];
});

watch(activeTab, autoReloadActiveCurd);

onActivated(() => {
  skipReloadCount === 0 && autoReloadActiveCurd();
  skipReloadCount = Math.max(0, skipReloadCount - 1);
});

/**
 * 配置curd自动刷新，curdOption.reloadOnActive优先级高于props.reloadOnActive
 * 1. curdOption.reloadOnActive为true时，自动刷新
 * 2. curdOption.reloadOnActive为false时，不自动刷新
 * 3. curdOption.reloadOnActive未配置时，根据props.reloadOnActive判断是否自动刷新
 */
function autoReloadActiveCurd() {
  const activeTabIndex = props.cardTab.findIndex(x => x.key == activeTab.value);
  const activeCurd = curd.value?.[activeTabIndex];
  const curdOption = props.cardTab[activeTabIndex]?.curdOption;
  if (!activeCurd) return;

  const reloadOnActive =
    curdOption.reloadOnActive === true || (curdOption.reloadOnActive !== false && props.reloadOnActive);
  reloadOnActive && nextTick(() => activeCurd.reload({ resetPage: false }));
}

//请求函数
function getData(item, page, searchParams = {}) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  //api
  let api = item.api || item.curdOption.api;
  //requestParams
  let requestParams = item.requestParams || item.curdOption.requestParams;
  if (api) {
    loading[index] = true;
    let _searchParams = searchParams;
    //外部整理搜索参数
    if (typeof item.fixSearchParams == 'function') {
      _searchParams = item.fixSearchParams(searchParams);
    }
    return $http
      .post(api, {
        ...page,
        ...(requestParams || {}),
        ..._searchParams
      })
      .then(res => {
        return res;
      })
      .finally(() => {
        loading[index] = false;
      });
  }
}

//搜索配置
function getSearchConfig() {
  return (
    props.cardTab.find(x => x.key == activeTab.value).searchConfig || {
      schema: [
        {
          prop: 'keyword',
          label: '关键字',
          component: 'el-input'
        }
      ]
    }
  );
}

//头部按钮点击事件
function headBtnClick(item) {
  emit('headBtnClick', item.key);
}

//搜索
function searchOutput(arr) {
  reload();
}

//vNode渲染
function contentRender(header) {
  return isVNode(header) ? header : <span>{header || ''}</span>;
}

//刷新
function reload({ resetPage = true } = {}) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  if (curd.value) {
    curd.value[index].reload({ resetPage });
  }
}

//请求前事件
function beforeRequest() {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  loading[index] = true;
  emit('beforeRequest');
}

//请求后事件
function afterRequest(list) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  loading[index] = false;
  emit('afterRequest', list);
}

//请求抛错事件
function requestError(error) {
  let index = props.cardTab.findIndex(x => x.key == activeTab.value);
  loading[index] = false;
  emit('requestError', error);
}

defineExpose({
  reload,
  activeCurdRef
});
</script>
<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/mixins/mixins' as el;

.hide_tab {
  :deep(.el-tabs__header) {
    display: none;
  }
}

.funi_list_page {
  :deep(.el-tabs__item) {
    padding: 0 20px !important;
  }

  .search_box {
    padding: 8px 16px;
    background: white;
    margin-bottom: 16px;
  }

  .title_row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: white;

    .table_title {
      font-size: 16px;
      color: #515151;
    }

    .btn_box {
      display: flex;

      .btn {
        min-width: 80px;
      }
    }
  }

  :deep() {
    .#{el.$namespace}-tabs__header {
      margin-bottom: 0px;
    }
  }
}
</style>

<style lang="scss" scoped>
// 强制FuniListPage高度100%，curd分页置底，内容自动拉满
.funi_list_page.teleported {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(> .el-tabs__header) {
    flex-shrink: 0;
  }

  :deep(> .el-tabs__content) {
    flex-grow: 1;
    overflow: auto;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
      > .funi-curd__wrap {
        // height: 100%;
        flex-grow: 1;
      }
    }
  }
}
</style>

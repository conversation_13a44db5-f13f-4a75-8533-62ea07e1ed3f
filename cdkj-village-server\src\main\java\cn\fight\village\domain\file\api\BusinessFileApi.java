package cn.fight.village.domain.file.api;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.file.service.BusinessFileService;
import cn.fight.village.domain.user.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 业务文件接口层
 */
@Slf4j
@RestController
@RequestMapping("file")
public class BusinessFileApi {

    @Resource
    private BusinessFileService businessFileService;

    /**
     * 文件上传
     * @return
     */
    @PostMapping("upload")
    public JsonResult fileUpload(@UserInfo User user, MultipartFile file) throws IOException {
        return businessFileService.uploadFile(user,file);
    }

    /**
     * 文件下载
     */
    @GetMapping("download")
    public void fileDownload(@RequestParam String fileStoreId, HttpServletResponse response) {
        businessFileService.downloadFile(fileStoreId,response);
    }
}

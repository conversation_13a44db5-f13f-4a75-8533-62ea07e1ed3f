<template>
  <div class="contract-template">
    <iframe src="./contract.html" ref="contractRef" @load="onLoad"></iframe>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";

const emit = defineEmits(["output"]);

const props = defineProps({
  transProtocol: {},
  isDetail: false,
});
const contractRef = ref();

watch(
  () => props.transProtocol,
  () => {
    onLoad();
  }
);

function onLoad() {
  if (contractRef.value?.contentWindow) {
    contractRef.value.contentWindow.setValues(props.transProtocol);
    if (props.isDetail) {
      contractRef.value.contentWindow.setDetail();
    }
  }
}
function print(){
  contractRef.value.contentWindow.print()
}
/**
 * 下一步
 */
async function nextStep() {
  let values = await contractRef.value.contentWindow.getValues();
  if (!values) {
    return Promise.reject();
  }
  emit("output", values);
  return Promise.resolve();
}

defineExpose({
  nextStep,
  print
});
</script>

<style lang="scss" scoped>
.contract-template {
  height: calc(100vh - 276px);
  iframe {
    width: 100%;
    height: 100%;
    border: 0;
  }
}
</style>

package cn.fight.village.domain.common.entity;

import cn.hutool.core.date.DateUtil;

import java.io.Serializable;

/**
 * 业务数据统一返回对象
 */
public class JsonResult implements Serializable {
    private Boolean success;

    private Integer code;

    private String message;

    private Object data;

    private Long timeStamp;

    private static final int SUCCESS_CODE = 200;
    private static final int ERROR_CODE = 400;
    private static final String SUCCESS_MSG = "请求成功";
    private static final String ERROR_MSG = "服务器内部错误,请联系系统管理员";

    /**
     * 成功消息
     * @param message
     * @return
     */
    public static JsonResult successMessage(String message) {
        JsonResult result = new JsonResult();
        result.setSuccess(Boolean.TRUE);
        result.setCode(SUCCESS_CODE);
        result.setMessage(message);
        return result;
    }

    /**
     * 失败消息
     * @param message
     * @return
     */
    public static JsonResult failMessage(String message) {
        JsonResult result = new JsonResult();
        result.setSuccess(Boolean.FALSE);
        result.setCode(ERROR_CODE);
        result.setMessage(message);
        return result;
    }

    /**
     * 失败消息
     * @param message
     * @return
     */
    public static JsonResult failMessage(Integer code,String message) {
        JsonResult result = new JsonResult();
        result.setSuccess(Boolean.FALSE);
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    /**
     * 返回数据
     * @param data
     * @return
     */
    public static JsonResult valueOfObject(Object data) {
        JsonResult result = new JsonResult();
        result.setSuccess(Boolean.TRUE);
        result.setCode(SUCCESS_CODE);
        result.setMessage(SUCCESS_MSG);
        result.setData(data);
        return result;
    }

    /**
     * 默认错误数据
     * @return
     */
    public static JsonResult defaultError() {
        JsonResult result = new JsonResult();
        result.setSuccess(Boolean.FALSE);
        result.setMessage(ERROR_MSG);
        result.setCode(ERROR_CODE);
        return result;
    }

    private JsonResult() {
        this.timeStamp = DateUtil.date().getTime();
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    private void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Boolean getSuccess() {
        return success;
    }

    private void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getCode() {
        return code;
    }

    private void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    private void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    private void setData(Object data) {
        this.data = data;
    }

}

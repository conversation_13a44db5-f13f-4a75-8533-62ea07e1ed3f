@use 'element-plus/theme-chalk/src/mixins/mixins' as el;
@use '@/styles/mixins/functions.scss' as *;
@use '@/styles/mixins/utils.scss' as *;
@use './var.scss';
@use 'sass:map';

$tabHieght: getCssVar('multi-tab', 'height');
$itemHieght: getCssVar('multi-tab', 'item-height');

@include b(layout-multi-tab) {
  display: flex;
  align-items: stretch;
  height: $tabHieght;
  border-bottom: getCssVar('multi-tab', 'border');
  box-sizing: border-box;
  padding: 0px 12px;

  @include el.b(tabs) {
    @include el.set-css-var-value('tabs-header-height', $tabHieght);

    flex: 1 1 0%;
    min-width: 0;

    :deep() {
      $a: '';

      @include el.e(header) {
        margin: 0;
        box-sizing: border-box;
        border: none;
      }

      @include el.e(nav-wrap) {
        padding: 0;
        box-sizing: border-box;
        &.is-scrollable {
          padding: 0 40px;
        }
      }

      @include el.e((nav-prev, nav-next)) {
        width: 36px;
        line-height: $itemHieght;
        border-right: 1px solid #eee;
        border-left: 1px solid #eee;
        border-top: 1px solid #eee;
        margin-top: calc(#{$tabHieght} - #{$itemHieght});
      }

      @include el.e(nav) {
        border: none !important;
      }

      @include el.e(item) {
        background: #ffffff;
        color: getCssVar('multi-tab', 'text-color');
        border: getCssVar('multi-tab', 'border') !important;
        border-right: none !important;
        height: $itemHieght;
        margin-top: calc(#{$tabHieght} - #{$itemHieght});
        font-size: var(--el-font-size-extra-small);
        font-weight: 400;
        padding: 0 14px !important;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        &.is-active {
          background: #f5f7fa;
          border-bottom: none !important;
          color: getCssVar('multi-tab', 'text-color-active');
          .el-dropdown {
            color: getCssVar('multi-tab', 'text-color-active');
          }
        }

        &:hover {
          color: getCssVar('multi-tab', 'text-color-hover');
          .el-dropdown {
            color: getCssVar('multi-tab', 'text-color-active');
          }
        }

        &:last-child {
          border-right: getCssVar('multi-tab', 'border') !important;
          border-radius: 0px 4px 0px 0px;
        }

        &:first-child {
          border-radius: 4px 0px 0px 0px;
        }

        .is-icon-close {
          margin-left: 2px;
        }
      }

      @include el.e(content) {
        display: none;
      }
    }
  }

  &__label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size:  var(--el-font-size-extra-small);
    line-height: 28px;
    max-width: 200px;
  }
}

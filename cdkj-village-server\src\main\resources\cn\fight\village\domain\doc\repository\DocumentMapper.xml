<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.fight.village.domain.doc.repository.DocumentMapper">

    <sql id="BaseQuery">
        select
            doc.uuid,
			doc.contract_id contractId,
            c.contract_no contractNo,
			doc.doc_no docNo,
			doc.path,
            STRING_AGG(DISTINCT up.upper, ',') AS upper,
            STRING_AGG(DISTINCT up.upper_name, ',') AS upperName,
            STRING_AGG(DISTINCT up.upper_id_no, ',') AS upperIdNo,
		    STRING_AGG(DISTINCT ud.upper,',') as under,
		    STRING_AGG(DISTINCT ud.under_name,',') as underName,
		    STRING_AGG(DISTINCT ud.under_id_no,',') as underIdNo,
		    STRING_AGG(DISTINCT ud.under_location,',') as location
        from public.rlams_contract c
		inner join public.rlams_document doc on c.uuid = doc.contract_id and doc.deleted = 0
        left join public.rlams_land l on c.uuid = l.contract_id and l.deleted = 0
        left join public.rlams_upper up on c.uuid = up.contract_id and up.deleted = 0
        left join public.rlams_under ud on c.uuid = ud.contract_id and ud.deleted = 0
        where c.deleted = 0
    </sql>

    <select id="queryList" resultType="cn.fight.village.domain.doc.value.DocumentListVo">
        <include refid="BaseQuery"/>
        <if test="contractNo != null and contractNo != ''">
            and c.contract_no = #{contractNo}
        </if>
        <if test="docNo != null and docNo != ''">
            and doc.doc_no = #{docNo}
        </if>
        <if test="contractType != null and contractType != ''">
            and c.type = #{contractType}
        </if>
        <if test="upper != null and upper != ''">
            and (up.upper like '%' ||  #{upper} || '%' or up.upper_name like '%' ||  #{upper} || '%')
        </if>
        <if test="under != null and under != ''">
            and (ud.upper like '%' || #{under} || '%' or ud.under_name like '%' || #{under} || '%')
        </if>
        <if test="location != null and location != ''">
            and ud.under_location like '%' || #{location} || '%'
        </if>
        <if test="idCode != null and idCode != ''">
            and (up.upper_id_no =  #{idCode}  or ud.under_id_no = #{idCode})
        </if>
		group by  doc.uuid,doc.contract_id,c.uuid,c.contract_no,doc.doc_no,doc.path
    </select>

    <select id="selectByContractId" resultType="cn.fight.village.domain.doc.value.DocumentListVo">
        <include refid="BaseQuery"/>
        and c.uuid = #{contractId}
        group by  doc.uuid,doc.contract_id,c.uuid,c.contract_no,doc.doc_no,doc.path
    </select>

    <select id="queryList4Trans" resultType="cn.fight.village.domain.doc.value.DocumentListVo">
        select
            doc.uuid,
            doc.contract_id contractId,
            c.contract_no contractNo,
            doc.doc_no docNo,
            doc.path,
            p.party_a  upper,
            p.party_b under,
            p.a_id upperIdNo,
            p.a_location as location,
            p.b_represe underName
        from public.rlams_contract c
		inner join public.rlams_document doc on c.uuid = doc.contract_id and doc.deleted = 0
        inner join public.rlams_land_protocol p on c.uuid = p.contract_id
        where c.deleted = 0
        <if test="upper != null and upper != ''">
            and p.party_a like '%' || #{upper} || '%'
        </if>
        <if test="under != null and under != ''">
            and p.party_b like '%' || #{under} || '%'
        </if>
        <if test="location != null and location != ''">
            and p.a_location like '%' || #{location} || '%'
        </if>
        <if test="idCode != null and idCode != ''">
            and p.a_id = #{idCode}
        </if>
        <if test="contractNo != null and contractNo != ''">
            and c.contract_no = #{contractNo}
        </if>
    </select>
</mapper>
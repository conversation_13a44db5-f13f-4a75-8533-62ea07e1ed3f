/*
 * @Author: co<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-01 09:50:05
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-06-12 17:47:22
 * @FilePath: /funi-cloud-web-gsbms/src/utils/directive/draggable.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

import Sortable from 'sortablejs';

export default {
  mounted(el, binding, vnode, prevVnode) {
    const options = binding.value;
    options.forEach(o => {
      new Sortable(el.querySelector(o.el), o.option);
    });
  }
};

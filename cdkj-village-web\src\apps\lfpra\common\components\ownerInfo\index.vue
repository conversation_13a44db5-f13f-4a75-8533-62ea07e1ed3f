<!-- 权属信息 -->
<template>
  <div class="owner_info">
    <div class="owner_info_add" @click="handleShow" v-if="tableData.length < 1" >
      <span>关联集体组织成员家庭（使用人）</span>
    </div>
      <funi-curd-v2
        v-else
        :data="tableData"
        :columns="conColumns"
        :pagination="false"
        :loading="loading"
        :stripe="false"
        border
      >
      </funi-curd-v2>
  </div>
  <ChooseCollection ref="chooseModal" @exportObject="setCollection"></ChooseCollection>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref,onMounted, watchEffect } from 'vue';
import { ElNotification } from 'element-plus';
import ChooseCollection from '@/apps/lfpra/common/components/ownerInfo/chooseCollection.vue';
import Hyperlink from '@/apps/lfpra/common/components/hyperlinkTable/index.vue';
import { deleteInfoHttp } from '@/apps/lfpra/common/hooks/api.js';
const props = defineProps({
  tableData:{type:Array,default:()=>[]},
  isEdit:{type:Boolean,default:true},//控制新增按钮 查看不显示
})
const emit = defineEmits(['exData'])
const chooseModal = ref(null)
 const tableData = ref([]); // 表格数据
const loading = ref(false)
const conColumns = computed(() => {
  return [

    {
      label: '成员编号',
      prop: 'memberCode'
    },
    {
      label: '户主姓名',
      prop: 'memberName'
    },
    {
      label: '户主证件号码',
      prop: 'cerCertificateNo'
    },
    {
      label: '家庭住址',
      prop: 'detailedAddress'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      hidden:!props.isEdit,
      render: ({ row, index }) => {
        let operationBtn = {
          DEL: (
            <el-popconfirm
              title="确定移除当前项？"
              width="220"
              onConfirm={() => {
                removeFunc(row,index);
              }}
            >
              {{
                reference: () => <Hyperlink  text={'移除'}></Hyperlink>
              }}
            </el-popconfirm>
          )
        };
        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {operationBtn.DEL}
          </div>
        );
      }
    }
  ];
});
watchEffect(()=>{
  // 编辑/查看进入 回显权属信息列表
  if(props.tableData.length > 0){
    tableData.value = props.tableData
  }
})

// 打开表格
const handleShow =()=>{
  let list = tableData.value.length>0 ?tableData.value.map(item=>item.familyId):[]

  chooseModal.value.show(list)
}
// 选择后的表格数据
const setCollection = e=>{
  tableData.value = e
  emit('exData',tableData.value)
}
// table表格 操作移除
const removeFunc = async (row,index)=>{
  //编辑时 删除权属信息
  // if(row.ownerShipInfoId){
  //   await deleteInfoHttp({ ownerShipInfoId: row.ownerShipInfoId });
  // }
  tableData.value.splice(index, 1)
  emit('exData',tableData.value)
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
}
</script>
<style scoped lang="less">
.owner_info{
  width: 100%;
  .owner_info_add{
    width: 100%;
    padding: 30px 0;
    display: flex;
    justify-content: center;
    border: 1px dashed var(--el-color-primary);
    border-radius: 8px;
    cursor: pointer;
    span{
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }
}
</style>

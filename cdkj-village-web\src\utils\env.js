const metaEnv = import.meta.env;

export default {
  /** 部署应用时的基本 URL。他由base 配置项决定。 */
  baseURL: metaEnv.BASE_URL,
  /** 是否运行在开发环境 */
  dev: metaEnv.DEV,
  /** 运行模式 */
  mode: metaEnv.MODE,
  /** 是否运行在生产环境 */
  prod: metaEnv.PROD,
  /** 是否运行在 server 上 */
  ssr: metaEnv.SSR,
  /** 当前平台类型paas / bpaas */
  platform: metaEnv.VITE_PLATFORM,
  isPaas: metaEnv.VITE_PLATFORM === 'paas',
  isBpaas: metaEnv.VITE_PLATFORM === 'bpaas',
  useSearchV2: metaEnv.VITE_USE_SEARCH_V2 === 'true'
};

package cn.fight.village.domain.household.vo;

import cn.fight.village.domain.common.anno.Sensitive;
import cn.fight.village.domain.common.entity.BaseValue;

/**
 * 家庭成员信息
 *
 */
public class HouseMember extends BaseValue {
    private String householdId;

    //户编号
    private String householdCode;

    //姓名
    private String name;

    //证件号码
    @Sensitive
    private String idCode;

    //性别
    private String  gender;

    //家庭关系
    private String  relation;

    //证件类型
    private String  idType;

    //家庭地址
    @Sensitive
    private String location;

    //是否户主
    private Integer householder;

    //联系电话
    @Sensitive
    private String phone;

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getHouseholdId() {
        return householdId;
    }

    public void setHouseholdId(String householdId) {
        this.householdId = householdId;
    }

    public Integer getHouseholder() {
        return householder;
    }

    public void setHouseholder(Integer householder) {
        this.householder = householder;
    }

    public String getHouseholdCode() {
        return householdCode;
    }

    public void setHouseholdCode(String householdCode) {
        this.householdCode = householdCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}

<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" title="关联集体组织成员家庭（使用人）">
      <div>
        <FuniCurd
          ref="funiCurd"
          @get-curd="
            e => {
              funi_curd = e;
            }
          "
          height="calc(50vh - 40px)"
          :columns="columnsProject"
          :useSearchV2="false"
          :isShowSearch="true"
          :searchConfig="searchConfig"
          :lodaData="lodaData"
          :loading="loading"
          @current-change="getData"
          :rowKey="keyType"
          size="small"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :disabled="selection.length == 0" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref } from 'vue';
import { queryOwnerShipInfoListtHttp } from '@/apps/lfpra/common/hooks/api.js';
// 表单查询

const funi_curd = ref(null);
const dialogVisible = ref(false); // 控制模态框显示隐藏
const funiCurd = ref(void 0);

const selection = ref([]);
const snList = ref([]);
let year = void 0;
const keyType = ref('familyId');
const loading = ref(false);
// 模态框表格配置
const columnsProject = computed(() => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left'
    },
    {
      label: '成员编号',
      prop: 'memberCode'
    },
    {
      label: '户主姓名',
      prop: 'memberName'
    },
    {
      label: '户主证件号码',
      prop: 'cerCertificateNo'
    },
    {
      label: '家庭住址',
      prop: 'detailedAddress'
    }
  ];
});
const searchConfig = computed(() => {
  let obj = {
    schema: [
      {
        label: '关键字',
        component: 'el-input',
        prop: 'queryValue',
        props: {
          placeholder: '请输入成员编号、户主姓名、户主证件号码搜索',

        },
        colProps: { span: 12 },
        style:'width:100%'

      }
    ]
  };
  return obj;
});
const emit = defineEmits(['exportObject']);
// 选择表格项
const getData = e => {
  selection.value = [];
  selection.value.push(e);
};
//显示dailog框
const show = async (list, key) => {
  selection.value = [];
  snList.value = list || [];
  keyType.value = key || 'familyId';
  await nextTick();
  dialogVisible.value = true;
};
//获取列表数据
const lodaData = async (page, params) => {
  loading.value = true;
  const resData = await queryOwnerShipInfoListtHttp({
    ...page,
    ...params
  }).finally(() => {
    loading.value = false;
  });
  if (keyType.value == 'familySn') {
    let activeRow = resData.list.filter(item => item[keyType.value] == snList.value[0][keyType.value])[0];
    funiCurd.value.setCurrentRow(activeRow);
  }

  return resData;
};
//添加选中效果
const tableRowClassName = ({ row }) => {
  if (snList.value.some(item => item[keyType.value] == row[keyType.value])) {
    return 'disabled-row';
  }
  return '';
};
//  确认按钮
const confirmFunc = () => {
  emit('exportObject', unref(selection));
  cancelClick();
};
//  取消按钮
const cancelClick = () => {
  dialogVisible.value = false;
  selection.value = [];
};

defineExpose({
  show
});
</script>
<style scoped>
@import url('@/apps/lfpra/common/style/table_disabled.css');
</style>

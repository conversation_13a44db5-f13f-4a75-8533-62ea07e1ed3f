<!-- 宅基地管理 -->
<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" />
  </div>
</template>

<script setup lang="jsx">
import { computed, onMounted, ref } from 'vue';
import { userTableColumns, useBtnsConfig } from './hooks/index.jsx';

import { useRouter } from 'vue-router';
import { ElNotification } from 'element-plus';
const router = useRouter();
const listPage = ref();
const query = ref();
// 获取列表数据
const lodaData = async (pages, parmas) => {
  query.value = parmas;
  listPage.value.activeCurd.resetCurrentRow();
  let data = await queryListHttp({
    ...pages,
    ...parmas
  });
  return data;
};
onMounted(() => {});
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({addFunc}),
        lodaData: lodaData,
        reloadOnActive: true,
        fixedButtons: true,
        checkOnRowClick: true,
        columns: userTableColumns({ seeDateils, editFunc, delFunc })
      }
    }
  ];
});
// 查看详情
const seeDateils = row => {
  router.push({
    name: 'lfpra_homestead_details',
    query: {
      title: '宅基地信息详情',
      bizName: '详情',
      type: 'info',
      id: row.id,
      principalSn: row.siteSn,
      no:row.siteCode,
      tab: ['宅基地信息', row.siteCode, '详情'].join('-')
    }
  });
};
// 编辑
const editFunc = row => {
  router.push({
    name: 'HousesteadAdd',
    query: {
      title: '宅基地信息编辑',
      bizName: 'edit',
      type: 'edit',
      id: row.id,
      no:row.siteCode,
      tab: ['宅基地信息', row.siteCode, '编辑'].join('-')
    }
  });
};
// 删除
const delFunc = async row => {
  await deleteHttp({ siteInfoId: row.id });
  ElNotification({
    title: '删除成功',
    type: 'success'
  });
  listPage.value.reload({ resetPage: false });
};
//新增
const addFunc = () => {
  router.push({
    name: 'HousesteadAdd',
    query: {
      title: '宅基地信息新增',
      bizName: '新建',
      type: 'add',
      tab: '宅基地信息-新增'
    }
  });
};
</script>

//用户管理

import { ElNotification } from 'element-plus'
/**
 * 获取用户列表
 * @param {*} data 
 */
export const userList = (data) => {
    return $http.post("/user/list", data)
}
/**
 * 添加用户
 * @param {*} data 
 */
export const userAdd = (data) => {
    return $http.post("/user/add", data)
}
/**
 * 修改用户
 * @param {*} data 
 */
export const userUpdate = (data) => {
    return $http.post("/user/update", data)
}
/**
 * 删除用户
 * @param {*} data 
 */
export const userRemove = (data) => {
    return $http.post("/user/remove", data).then(res => {
        ElNotification({
            title: '删除成功',
            type: 'success',
        });
        return res
    })
}
/**
 * 获取用户信息
 * @param {*} data 
 */
export const userInfo = (data) => {
    return $http.fetch("/user/info", data)
}
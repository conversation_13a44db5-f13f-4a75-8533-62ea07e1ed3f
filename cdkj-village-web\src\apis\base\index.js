// src/utils/request.js
import router from '@/router'
import axios from 'axios'
import { ElMessage } from 'element-plus' // 根据实际使用的 UI 库修改
import { useAppStore } from '@/stores/useAppStore';

// 创建 axios 实例
const service = axios.create({
    baseURL: process.env.NODE_ENV === 'development' ? './villagetest' : location.origin + "/villagetest",
    timeout: 60000, // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        // 在这里可以统一处理请求配置，例如添加 token
        if(config.config.isLoading){
            const appStore = useAppStore();
            appStore.isLoading = true
        }
        const token = sessionStorage.getItem('token')
        if (token) {
            config.headers.token = token
        }
        return config
    },
    error => {
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        if(response.config.isLoading){
            const appStore = useAppStore();
            appStore.isLoading = false
        }
        // 处理响应数据（根据后端约定格式）
        const res = response.data

        // 这里根据项目实际响应格式修改
        if (res.code !== 200) {
            ElMessage.error(res.message || '请求失败')
            if (res.code == 401) {
                // 跳转登录页
                router.push({
                    name: "Login"
                })
            }
            return Promise.reject(new Error(res.message || 'Error'))
        }

        return res.data
    },
    error => {
        debugger
        if(error.config.config.isLoading){
            const appStore = useAppStore();
            appStore.isLoading = false
        }
        // 处理 HTTP 状态码
        if (error.response) {
            switch (error.response.status) {
                case 401:
                    Message.error('登录过期，请重新登录')
                    break
                case 403:
                    Message.error('没有权限')
                    break
                case 500:
                    Message.error('服务器错误')
                    break
                default:
                    Message.error(error.response.data.message || '请求失败')
            }
        } else {
            // 处理网络错误
            if (error.message.includes('timeout')) {
                ElMessage.error('请求超时')
            } else {
                ElMessage.error('网络连接异常')
            }
        }

        return Promise.reject(error)
    }
)

// 封装通用请求方法
const request = (options) => {
    return service(options)
}

// 封装常用方法
export const get = (url, params,config={}) => {
    return request({
        url,
        method: 'get',
        params,
        config
    })
}

export const post = (url, data,config={}) => {
    return request({
        url,
        method: 'post',
        data,
        config
    })
}

window.$http = {
    get,
    post
}
export default {
    get,
    post,
    fetch: get
}
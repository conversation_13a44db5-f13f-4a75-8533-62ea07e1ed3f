package cn.fight.village.domain.land.entity;

import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 地理土地
 */
@Data
@TableName("public.rlams_real_land")
public class RealLand extends BaseEntity {
    //地块代码，关联GIS土地
    public String landNo;

    //土地类型
    public String landType;

    //是否承包
    public String contract;

    //面积
    public Double area;

    //是否种子
    public String farming;

    //是否流转
    public String transfer;

    //是否新建，人工添加 1是
    public Integer newCreated;

    //备注
    public String remark;
}

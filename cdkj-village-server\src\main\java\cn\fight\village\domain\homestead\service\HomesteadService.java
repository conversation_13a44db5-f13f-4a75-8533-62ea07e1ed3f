package cn.fight.village.domain.homestead.service;

import cn.fight.village.domain.common.entity.BaseEntity;
import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.util.CommonUtils;
import cn.fight.village.domain.homestead.entity.Homestead;
import cn.fight.village.domain.homestead.repository.HomesteadRepository;
import cn.fight.village.domain.homestead.request.HomesteadQuery;
import cn.fight.village.domain.homestead.request.HomesteadRequest;
import cn.fight.village.domain.homestead.value.HomesteadListVo;
import cn.fight.village.domain.homestead.value.HomesteadValue;
import cn.fight.village.domain.homestead.value.HouseholdHomesteadVo;
import cn.fight.village.domain.household.service.HouseholdService;
import cn.fight.village.domain.user.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 宅基地信息服务层
 *
 */
@Service
public class HomesteadService {

    @Resource
    private HomesteadRepository homesteadRepository;

    @Lazy
    @Resource
    private HouseholdService householdService;

    /**
     * 添加宅基地信息
     *
     * @param homesteadRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult addHomesteadInfo(HomesteadRequest homesteadRequest, User user) {
        Homestead existStead = this.getHomeSteadByNo(homesteadRequest.getCode());
        if (existStead != null) {
            throw new BusinessException("宅基地编号不允许重复");
        }

        Homestead creator = new Homestead();
        BeanUtils.copyProperties(homesteadRequest,creator);
        creator.manageCreateInfo(user);

        if (homesteadRepository.insert(creator) != 1) {
            throw new BusinessException("添加失败");
        }
        return JsonResult.valueOfObject(creator.getUuid());
    }

    /**
     * 断言宅基地编号不能重复
     *
     * @param steadNo
     */
    public Homestead getHomeSteadByNo(String steadNo) {
        if (StringUtils.isEmpty(steadNo)) {
            return null;
        }

        return homesteadRepository.selectOne(new LambdaQueryWrapper<Homestead>()
            .eq(Homestead::getCode,steadNo)
                .eq(BaseEntity::getDeleted,Homestead.IS_NOT_DELETED)
        );
    }

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    public JsonResult listQuery(HomesteadQuery query) {
        query.checkPageParam();
        PageHelper.startPage(query.getPageNo(),query.getPageSize());
        List<HomesteadListVo> result = homesteadRepository.pageQuery(query);
        return JsonResult.valueOfObject(new PageInfo<>(result));
    }

    /**
     * 信息查询
     *
     * @param uuid
     * @return
     */
    public JsonResult infoQuery(String uuid) {
        Homestead homestead = this.selectById(uuid);
        if (homestead == null) {
            throw new BusinessException("获取宅基地信息失败");
        }
        HomesteadValue result = new HomesteadValue();
        BeanUtils.copyProperties(homestead,result);

        result.setHouseMembers(householdService.getHouseMembersByHouseId(homestead.getHousehold()));
        return JsonResult.valueOfObject(result);
    }

    /**
     * 删除宅基地信息
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult remove(HomesteadRequest request,User user) {
        String uuid = request.getUuid();
        CommonUtils.notNull(uuid,"待删除件ID不能为空");
        Homestead deleter = new Homestead();
        deleter.setUuid(uuid);
        deleter.manageDeleteInfo(user);

        if (homesteadRepository.updateById(deleter) != 1) {
            throw new BusinessException("删除失败");
        }

        return JsonResult.successMessage("删除成功");
    }

    /**
     * 根据户ID获取关联的宅基地
     *
     * @param houseId
     * @return
     */
    public List<Homestead> selectHomesteadByHouseId(String houseId) {
        if (StringUtils.isEmpty(houseId)) {
            return null;
        }

        return homesteadRepository.selectList(new LambdaQueryWrapper<Homestead>()
            .eq(Homestead::getHousehold,houseId)
                .eq(Homestead::getDeleted,Homestead.IS_NOT_DELETED)
        );
    }

    /**
     * 修改宅基地信息
     *
     * @param request
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JsonResult update(HomesteadRequest request,User user) {
        String uuid = request.getUuid();
        CommonUtils.notNull(uuid,"被修改件ID不能为空");

        Homestead homestead = this.selectById(uuid);
        if (homestead == null) {
            throw new BusinessException("宅基地信息不存在");
        }

        if (StringUtils.isNotBlank(request.getCode())) {
            Homestead existsStead = this.getHomeSteadByNo(request.getCode());
            if (existsStead != null && !homestead.getCode().equals(existsStead.getCode())) {
                throw new BusinessException("宅基地编号不允许重复");
            }
        }

        Homestead updater = new Homestead();
        BeanUtils.copyProperties(request,updater);
        updater.manageUpdateInfo(user);
        if (homesteadRepository.updateById(updater) != 1) {
            throw new BusinessException("更新失败");
        }

        return JsonResult.valueOfObject(updater.getUuid());
    }

    /**
     * 主键查询
     *
     * @param uuid
     * @return
     */
    public Homestead selectById(String uuid) {
        if (StringUtils.isEmpty(uuid)) {
            return null;
        }

        return homesteadRepository.selectOne(new LambdaQueryWrapper<Homestead>()
                .eq(Homestead::getUuid,uuid)
                .eq(Homestead::getDeleted,Homestead.IS_NOT_DELETED)
        );
    }

    /**
     * 根据户ID获取宅基地
     *
     * @param houseHoldId
     * @return
     */
    public JsonResult queryByHouseHold(String houseHoldId) {
        CommonUtils.notNull(houseHoldId,"查询目标户ID不能为空");

        List<Homestead> homesteads = this.selectHomesteadByHouseId(houseHoldId);
        if (CollectionUtils.isNotEmpty(homesteads)) {
            List<HouseholdHomesteadVo> results = homesteads.stream().map(i -> {
                HouseholdHomesteadVo householdHomesteadVo = new HouseholdHomesteadVo();
                BeanUtils.copyProperties(i, householdHomesteadVo);
                return householdHomesteadVo;
            }).collect(Collectors.toList());

            return JsonResult.valueOfObject(results);
        }

        return JsonResult.valueOfObject(Collections.emptyList());
    }
}

<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-02-09 14:06:14
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2023-07-19 15:37:05
 * @FilePath: /funi-cloud-web-gsbms/src/components/FuniCurd/components/CurdPagination.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <el-pagination
    class="justify-end"
    background
    v-model:currentPage="currentPage"
    v-model:page-size="pageSize"
    :page-sizes="validPageSizes"
    :layout="props.layout"
    :total="props.total"
    small
    @size-change="resetPageIndex"
    @current-change="handleCurrentChange"
  >
    <div class="el-pagination__extra">
      <slot v-bind="{ total: total, currentPage: currentPage }"></slot>
    </div>
  </el-pagination>
</template>

<script setup>
import { computed, onMounted, ref, watchEffect } from 'vue';

const props = defineProps({
  pageSizes: { type: Array, default: () => [10, 20, 50] },
  layout: {
    type: String,
    default: 'slot, total, sizes, prev, pager, next, jumper'
  },
  total: { type: Number, default: 0 }
});

const currentPage = ref(1);
const pageSize = ref();

const validPageSizes = computed(() => {
  return (props.pageSizes || []).filter(item => parseInt(item) < 100);
});

const emit = defineEmits(['pageChange']);

watchEffect(() => (pageSize.value = validPageSizes.value[0]));

onMounted(() => resetPageIndex());

const handleCurrentChange = val => {
  emit('pageChange', {
    pageSize: pageSize.value,
    pageNo: currentPage.value
  });
};

function resetPageIndex() {
  currentPage.value = 1;
  handleCurrentChange();
}

defineExpose({
  resetPageIndex,
  currentPage,
  pageSize
});
</script>

<style lang="scss" scoped>
.el-pagination__extra {
  margin-right: 16px;
  font-weight: 400;
  color: var(--el-text-color-regular);
  flex-grow: 1;
  display: flex;
  flex-direction: row;
}
</style>

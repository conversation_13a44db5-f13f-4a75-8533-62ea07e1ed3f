<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" title="选择承包人">
      <div>
        <FuniCurd
          ref="funiCurd"
          height="calc(50vh - 40px)"
          :columns="columnsProject"
          :useSearchV2="false"
          :isShowSearch="true"
          :searchConfig="searchConfig"
          :lodaData="lodaData"
          :loading="loading"
          @row-click="getData"
          :rowKey="keyType"
          size="small"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :disabled="selection.length == 0" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref } from 'vue';
import { qyeryByHouseholder } from "@/apps/api/household.js"

const dialogVisible = ref(false); // 控制模态框显示隐藏
const funiCurd = ref(void 0);

const selection = ref([]);

const keyType = ref('uuid');
const loading = ref(false);

// 模态框表格配置
const columnsProject = computed(() => {
  return [
    {
      type: 'radio',
      width: '55px',
      fixed: 'left',
    },
    {
      label: '户编号',
      prop: 'householdCode'
    },
    {
      label: '户主姓名',
      prop: 'householder'
    },
    {
      label: '住址',
      prop: 'location'
    }
  ];
});
const searchConfig = computed(() => {
  let obj = {
    schema: [
      {
        label: '户主姓名',
        component: 'el-input',
        prop: 'name',
        props: {
          placeholder: '输入户主姓名查询',

        },
        colProps: { span: 12 },
        style:'width:100%'

      }
    ]
  };
  return obj;
});
const emit = defineEmits(['exportObject']);
// 选择表格项
const getData = ({column,row, selection:selcetRows}) => {
  selection.value = [];
  selection.value = [row]
};
//显示dailog框
const show = async (list, key) => {
  selection.value = [];
  keyType.value = key || 'uuid';
  dialogVisible.value = true;
};
//获取列表数据
const lodaData = async (page, params) => {
  loading.value = true;
  let res = await qyeryByHouseholder({...page,...params}).finally(()=>{
    loading.value = false;
  })
  funiCurd.value.setCurrentRow(selection.value);
  return res;
};

//  确认按钮
const confirmFunc = () => {
  emit('exportObject', unref(selection));
  cancelClick();
};
//  取消按钮
const cancelClick = () => {
  dialogVisible.value = false;
  selection.value = [];
};

defineExpose({
  show
});
</script>
<style scoped>

</style>

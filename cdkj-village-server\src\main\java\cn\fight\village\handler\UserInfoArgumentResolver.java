package cn.fight.village.handler;

import cn.fight.village.anno.UserInfo;
import cn.fight.village.domain.common.exception.BusinessException;
import cn.fight.village.domain.common.service.CacheService;
import cn.fight.village.domain.common.util.SecureUtils;
import cn.fight.village.domain.user.entity.User;
import cn.hutool.core.util.StrUtil;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户信息获取参数解析器
 */
@Component
public class UserInfoArgumentResolver implements HandlerMethodArgumentResolver {

    @Resource
    private CacheService cacheService;

    /**
     * 解析判断
     * @param parameter
     * @return
     */
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(UserInfo.class);
    }

    @Override
    public User resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
        if (request == null)
            throw new BusinessException("获取用户信息请求失败");

        String token = request.getHeader("token");
        if (StrUtil.isBlank(token))
            throw new BusinessException(401,"获取用户token失败");

        //从缓存中获取用户
        User user = cacheService.getUserLoginCache(SecureUtils.stringDecode(token));
        if (user == null)
            throw new BusinessException(401,"登录失效,请重新登录");

        return user;
    }
}

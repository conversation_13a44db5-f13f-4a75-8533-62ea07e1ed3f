<template>
  <div>
    <funiGroupTitle title="权属信息"></funiGroupTitle>
    <div class="owner_info">
      <div class="owner_info_add" @click="_add" v-if="!cardTab.dataList.length">
        <span>关联集体组织成员家庭（使用人）</span>
      </div>
      <funi-curd-v2
        v-else
        :data="cardTab.dataList"
        :columns="cardTab.columns"
        :pagination="false"
        :loading="loading"
        :stripe="false"
      >
      </funi-curd-v2>
    </div>
    <AddModal ref="addModalRef" :isDetail="isDetail" @exportObject="setCollection" />
  </div>
</template>
<script setup lang="jsx">
import { ref, reactive, watch } from 'vue';
import { ElNotification } from 'element-plus';
import AddModal from './addModal.vue';
const props = defineProps({
  personnelData: {
    type: Array,
    default: []
  },
  isDetail: {}
});
const addModalRef = ref(null);
const cardTab = reactive({
  dataList: [],
  columns: [
    {
      label: '成员编号',
      prop: 'householdCode',
    },
    {
      label: '户主姓名',
      prop: 'name'
    },
    { label: '户主证件号码', prop: 'idCode' },
    { label: '家庭住址', prop: 'location' },
    {
      label: '操作',
      prop: 'operList',
      align: 'center',
      fixed: 'right',
      width: 120,
      hidden: props.isDetail,
      render: ({ row, index }) => {
        return (
          <div>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                _delete(row, index);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ]
});

const subscript = ref();

const setCollection = e => {
  cardTab.dataList = e;
};

const _add = () => {
  addModalRef.value.show();
};
const _edit = (row, index) => {
  subscript.value = index;
  addModalRef.value.show(row);
};
const _delete = (row, index) => {
  cardTab.dataList.splice(index, 1);
};
defineExpose({
  getData: () => cardTab.dataList
});

watch(
  () => props.personnelData,
  () => {
    if (props.personnelData.length > 0) {
      cardTab.dataList = props.personnelData;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.owner_info {
  width: 100%;
  .owner_info_add {
    width: 100%;
    padding: 30px 0;
    display: flex;
    justify-content: center;
    border: 1px dashed var(--el-color-primary);
    border-radius: 8px;
    cursor: pointer;
    span {
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }
}
</style>

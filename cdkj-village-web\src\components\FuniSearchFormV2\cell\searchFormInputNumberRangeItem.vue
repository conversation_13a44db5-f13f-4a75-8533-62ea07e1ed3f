<template>
  <div class="search-form-item__number-range">
    <el-input-number class="search-form-item__number-range-item" placeholder="请输入" v-model="minValue" />
    <div class="divider">-</div>
    <el-input-number class="search-form-item__number-range-item" placeholder="请输入" v-model="maxValue" />
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  prop: Object,
  modelValue: { type: [Array, String], default: () => [] },
  attribute: { type: Object, default: () => ({}) }
});

const emit = defineEmits(['update:modelValue']);
const minValue = ref();
const maxValue = ref();

watch(
  () => props.modelValue,
  () => {
    const [min, max] = props.modelValue;
    minValue.value = min;
    maxValue.value = max;
  }
);

watch(
  () => [minValue.value, maxValue.value],
  ([min, max]) => {
    emit('update:modelValue', [min, max]);
  }
);
</script>

<style lang="scss" scoped>
.search-form-item__number-range {
  display: flex;
  justify-content: space-between;
  width: 100%;

  &-item {
    min-width: calc(50% - 16px);
  }
  .divider {
    margin: 0 10px;
    width: 12px;
  }
}
</style>

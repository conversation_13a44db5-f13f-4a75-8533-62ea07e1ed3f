package cn.fight.village.domain.statistics;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.statistics.entity.StatisticsQuery;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("statistics")
public class StatisticsApi {

    @Resource
    private StatisticsService statisticsService;

    @PostMapping("village")
    public JsonResult villageStatistics(@RequestBody StatisticsQuery query) {
        return statisticsService.villageStatistics(query);
    }

    /**
     * 获取乡村村组
     * @return
     */
    @GetMapping("getVillageTeams")
    public JsonResult getVillageTeams() {
        return statisticsService.getVillageTeams();
    }
}

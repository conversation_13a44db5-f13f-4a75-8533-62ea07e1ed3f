
-- 系统用户表创建
create table public.rlams_user (
   uuid                 VARCHAR(64)          not null,
   account              VARCHAR(64)          null,
   username             VARCHA<PERSON>(64)          null,
   password             VARCHAR(256)         null,
   user_type            integer         	 null,
   deleted              integer          	 null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_USER primary key (uuid)
);

comment on table village.rlams_user is
'系统用户表';

comment on column village.rlams_user.uuid is
'uuid';

comment on column village.rlams_user.account is
'account';

comment on column village.rlams_user.username is
'username';

comment on column village.rlams_user.password is
'password';

comment on column village.rlams_user.user_type is
'user_type';

comment on column village.rlams_user.deleted is
'deleted';

comment on column village.rlams_user.creator_id is
'creator_id';

comment on column village.rlams_user.updater_id is
'updater_id';

comment on column village.rlams_user.deleter_id is
'deleter_id';

comment on column village.rlams_user.create_time is
'create_time ';

comment on column village.rlams_user.update_time is
'update_time';

comment on column village.rlams_user.delete_time is
'delete_time';

-- 超级管理员用户创建
insert into public.rlams_user(uuid,account,password,user_type,username,deleted)
values('1','admin','88bc83a4dfbc1d7779bd43e3dc89b05b7ef8a4d22d7f37a414e67579d6a64e3d',1,'超级管理员',0);



/*==============================================================*/
/* Table: rlams_household  家庭信息表                                     */
/*==============================================================*/
create table public.rlams_household (
   uuid                 VARCHAR(64)          not null,
   householder_id       VARCHAR(64)          null,
   household_code       VARCHAR(64)          not null,
   location             VARCHAR(500)         null,
   poor                 VARCHAR(16)          null,
   lower                VARCHAR(16)          null,
   backup               VARCHAR(500)         null,
   deleted              SMALLINT             null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_HOUSEHOLD primary key (uuid)
);

comment on table village.rlams_household is
'农户家庭信息表';

comment on column village.rlams_household.householder_id is
'户主id';

comment on column village.rlams_household.household_code is
'户编号';

comment on column village.rlams_household.location is
'地址';

comment on column village.rlams_household.poor is
'是否贫困户';

comment on column village.rlams_household.lower is
'是否低保户';

comment on column village.rlams_household.backup is
'备注';

comment on column village.rlams_household.deleted is
'是否删除';

comment on column village.rlams_household.creator_id is
'creator_id';

comment on column village.rlams_household.updater_id is
'updater_id';

comment on column village.rlams_household.deleter_id is
'deleter_id';

comment on column village.rlams_household.create_time is
'create_time ';

comment on column village.rlams_household.update_time is
'update_time';

comment on column village.rlams_household.delete_time is
'delete_time';


/*==============================================================*/
/* Table: rlams_member  家庭成员表                                        */
/*==============================================================*/
create table public.rlams_member (
   uuid                 VARCHAR(64)          not null,
   house_id             VARCHAR(64)         not null,
   name                 VARCHAR(64)          null,
   gender               VARCHAR(8)           null,
   id_type              VARCHAR(128)          null,
   id_code              VARCHAR(64)          null,
   phone                VARCHAR(64)          null,
   phone_sec            VARCHAR(128)         null,
   relation             VARCHAR(64)          null,
   householder          SMALLINT           null,
   coll_member          VARCHAR(8)           null,
   coll_group           VARCHAR(64)           null,
   huko                 VARCHAR(8)           null,
   disabled             VARCHAR(8)           null,
   veteran              VARCHAR(8)           null,
   remark               VARCHAR(128)         null,
   deleted              SMALLINT           null,
   remark_time          TIMESTAMP            null,
   remark_reason         VARCHAR(512)         null,
   other                VARCHAR(512)         null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_MEMBER primary key (uuid)
);

comment on table village.rlams_member is
'家庭成员信息表';

comment on column village.rlams_member.name is
'姓名';

comment on column village.rlams_member.gender is
'性别';

comment on column village.rlams_member.id_type is
'证件类型';

comment on column village.rlams_member.id_code is
'证件号码';

comment on column village.rlams_member.phone is
'联系电话';

comment on column village.rlams_member.phone_sec is
'第二联系方式';

comment on column village.rlams_member.relation is
'家庭关系';

comment on column village.rlams_member.householder is
'是否户主';

comment on column village.rlams_member.coll_member is
'是否集体经济组织成员';

comment on column village.rlams_member.coll_group is
'集体经济组';

comment on column village.rlams_member.huko is
'是否户籍人员';

comment on column village.rlams_member.disabled is
'是否残疾人员';

comment on column village.rlams_member.veteran is
'退伍军人';

comment on column village.rlams_member.remark is
'人员备注';

comment on column village.rlams_member.deleted is
'删除标识';

comment on column village.rlams_member.remark_time is
'备注时间';

comment on column village.rlams_member.remark_reason is
'备注原因';

comment on column village.rlams_member.other is
'其他备注';

comment on column village.rlams_member.creator_id is
'creator_id';

comment on column village.rlams_member.updater_id is
'updater_id';

comment on column village.rlams_member.deleter_id is
'deleter_id';

comment on column village.rlams_member.create_time is
'create_time ';

comment on column village.rlams_member.update_time is
'update_time';

comment on column village.rlams_member.delete_time is
'delete_time';

/*==============================================================*/
/* Table: rlams_homestead   宅基地信息表                                    */
/*==============================================================*/
create table public.rlams_homestead (
   uuid                 VARCHAR(64)          not null,
   household            VARCHAR(64)          null,
   code                 VARCHAR(64)          null,
   area                 NUMERIC(10, 2)        null,
   land_type            VARCHAR(128)         null,
   has_cert             VARCHAR(8)           null,
   cert_no              VARCHAR(128)         null,
   no_cert_reason       VARCHAR(512)         null,
   floor                VARCHAR(64)          null,
   structure            VARCHAR(128)         null,
   usage                VARCHAR(128)         null,
   east                 VARCHAR(128)         null,
   west                 VARCHAR(128)         null,
   south                VARCHAR(128)         null,
   north                VARCHAR(128)         null,
   remark               VARCHAR(512)         null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   deleted              DECIMAL(1)           null,
   constraint PK_RLAMS_HOMESTEAD primary key (uuid)
);

comment on table village.rlams_homestead is
'宅基地信息';

comment on column village.rlams_homestead.uuid is
'主键';

comment on column village.rlams_homestead.household is
'家庭主键';

comment on column village.rlams_homestead.code is
'宅基地编号';

comment on column village.rlams_homestead.area is
'面积';

comment on column village.rlams_homestead.land_type is
'土地类型';

comment on column village.rlams_homestead.has_cert is
'是否取证';

comment on column village.rlams_homestead.cert_no is
'证书编号';

comment on column village.rlams_homestead.no_cert_reason is
'未取证原因';

comment on column village.rlams_homestead.floor is
'楼层';

comment on column village.rlams_homestead.structure is
'结构';

comment on column village.rlams_homestead.usage is
'使用情况';

comment on column village.rlams_homestead.east is
'东';

comment on column village.rlams_homestead.west is
'西';

comment on column village.rlams_homestead.south is
'南';

comment on column village.rlams_homestead.north is
'北';

comment on column village.rlams_homestead.remark is
'备注';

comment on column village.rlams_homestead.creator_id is
'creator_id';

comment on column village.rlams_homestead.updater_id is
'updater_id';

comment on column village.rlams_homestead.deleter_id is
'deleter_id';

comment on column village.rlams_homestead.create_time is
'create_time ';

comment on column village.rlams_homestead.update_time is
'update_time';

comment on column village.rlams_homestead.delete_time is
'delete_time';

comment on column village.rlams_homestead.deleted is
'deleted';


/*==============================================================*/
/* Table: rlams_contract   承包流转合同信息表                                     */
/*==============================================================*/
create table public.rlams_contract (
   uuid                 VARCHAR(64)          not null,
   type                 VARCHAR(64)          null,
   contract_no          VARCHAR(64)          null,
   household_id          VARCHAR(64)          null,
   remark               VARCHAR(512)         null,
   deleted              SMALLINT           null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_CONTRACT primary key (uuid)
);

comment on table village.rlams_contract is
'合同信息表';

comment on column village.rlams_contract.uuid is
'uuid';

comment on column village.rlams_contract.household_id is
'关联家庭户ID';

comment on column village.rlams_contract.type is
'type';

comment on column village.rlams_contract.contract_no is
'档案编号';

comment on column village.rlams_contract.remark is
'备注';

comment on column village.rlams_contract.deleted is
'deleted';

comment on column village.rlams_contract.creator_id is
'creator_id';

comment on column village.rlams_contract.updater_id is
'updater_id';

comment on column village.rlams_contract.deleter_id is
'deleter_id';

comment on column village.rlams_contract.create_time is
'create_time ';

comment on column village.rlams_contract.update_time is
'update_time';

comment on column village.rlams_contract.delete_time is
'delete_time';


/*==============================================================*/
/* Table: village.rlams_land    土地信息                                       */
/*==============================================================*/
create table public.rlams_land (
   uuid                 VARCHAR(64)          not null,
   contract_id          VARCHAR(64)          null,
   type                 VARCHAR(32)          null,
   land_no              VARCHAR(128)         null,
   land_type            VARCHAR(64)          null,
   contract             VARCHAR(8)           null,
   area                 NUMERIC(10,2)         null,
   farming              VARCHAR(128)         null,
   transfer             VARCHAR(128)         null,
   remark               VARCHAR(512)         null,
   deleted              SMALLINT           null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_LAND primary key (uuid)
);

comment on table village.rlams_land is
'土地信息表';

comment on column village.rlams_land.uuid is
'uuid';

comment on column village.rlams_land.contract_id is
'合同ID';

comment on column village.rlams_land.type is
'类型（坑塘.土地）';

comment on column village.rlams_land.land_no is
'地块代码';

comment on column village.rlams_land.land_type is
'地块代码';

comment on column village.rlams_land.contract is
'是否承包';

comment on column village.rlams_land.area is
'面积';

comment on column village.rlams_land.farming is
'耕种情况';

comment on column village.rlams_land.transfer is
'是否流转';

comment on column village.rlams_land.remark is
'remark';

comment on column village.rlams_land.deleted is
'deleted';

comment on column village.rlams_land.creator_id is
'creator_id';

comment on column village.rlams_land.updater_id is
'updater_id';

comment on column village.rlams_land.deleter_id is
'deleter_id';

comment on column village.rlams_land.create_time is
'create_time ';

comment on column village.rlams_land.update_time is
'update_time';

comment on column village.rlams_land.delete_time is
'delete_time';

/*==============================================================*/
/* Table: rlams_upper   发包方信息                                        */
/*==============================================================*/
create table public.rlams_upper (
   uuid                 VARCHAR(64)          not null,
   contract_id          VARCHAR(64)          null,
   upper_no             VARCHAR(64)          null,
   upper                VARCHAR(128)         null,
   upper_name           VARCHAR(64)          null,
   upper_id_no          VARCHAR(64)          null,
   upper_id_type        VARCHAR(16)          null,
   upper_phone          VARCHAR(64)          null,
   remark               VARCHAR(512)         null,
   deleted              SMALLINT           null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_UPPER primary key (uuid)
);

comment on table village.rlams_upper is
'发包方信息表';

comment on column village.rlams_upper.uuid is
'uuid';

comment on column village.rlams_upper.contract_id is
'合同id';

comment on column village.rlams_upper.upper_no is
'发包方人代码';

comment on column village.rlams_upper.upper is
'发包方名称';

comment on column village.rlams_upper.upper_name is
'发包人姓名';

comment on column village.rlams_upper.upper_id_no is
'发包人证件号';

comment on column village.rlams_upper.upper_id_type is
'发包人证件类型';

comment on column village.rlams_upper.upper_phone is
'发包方电话';

comment on column village.rlams_upper.remark is
'备注';

comment on column village.rlams_upper.deleted is
'delted';

comment on column village.rlams_upper.creator_id is
'creator_id';

comment on column village.rlams_upper.updater_id is
'updater_id';

comment on column village.rlams_upper.deleter_id is
'deleter_id';

comment on column village.rlams_upper.create_time is
'create_time ';

comment on column village.rlams_upper.update_time is
'update_time';

comment on column village.rlams_upper.delete_time is
'delete_time';


/*==============================================================*/
/* Table: rlams_under   承包方信息                                        */
/*==============================================================*/
create table public.rlams_under (
   uuid                 VARCHAR(64)          not null,
   contract_id          VARCHAR(64)          null,
   under_id             VARCHAR(64)          null,
   under_no             VARCHAR(128)         null,
   upper                VARCHAR(255)         null,
   under                VARCHAR(255)         null,
   villager             VARCHAR(8)           null,
   under_name           VARCHAR(64)          null,
   under_id_type        VARCHAR(64)          null,
   under_id_no          VARCHAR(64)          null,
   under_location       VARCHAR(256)         null,
   under_phone          VARCHAR(64)          null,
   contract_type        VARCHAR(64)          null,
   contract_cret_no     VARCHAR(128)         null,
   right_org            VARCHAR(64)          null,
   usage                VARCHAR(128)         null,
   price                NUMERIC(10,2)         null,
   right_area           NUMERIC(10,2)         null,
   remark               VARCHAR(512)         null,
   deleted              SMALLINT           null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_UNDER primary key (uuid)
);

comment on table village.rlams_under is
'土地承包方信息';

comment on column village.rlams_under.uuid is
'uuid';

comment on column village.rlams_under.contract_id is
'合同id';

comment on column village.rlams_under.under_id is
'承包方ID(家庭承包为户ID）';

comment on column village.rlams_under.under_no is
'承包方代码';

comment on column village.rlams_under.upper is
'承包方名字';

comment on column village.rlams_under.villager is
'是否村民';

comment on column village.rlams_under.under_name is
'承包方负责人名称';

comment on column village.rlams_under.under_id_type is
'承包方证件类型';

comment on column village.rlams_under.under_id_no is
'承包方证件类型';

comment on column village.rlams_under.under_location is
'承包方地址';

comment on column village.rlams_under.under_phone is
'承包方电话';

comment on column village.rlams_under.contract_type is
'承包方式';

comment on column village.rlams_under.contract_cret_no is
'承包经营产权证书号';

comment on column village.rlams_under.right_org is
'经营承包权取得方式';

comment on column village.rlams_under.usage is
'承包用途';

comment on column village.rlams_under.price is
'流转金额';

comment on column village.rlams_under.right_area is
'确权面积';

comment on column village.rlams_under.remark is
'备注';

comment on column village.rlams_under.deleted is
'deleted';

comment on column village.rlams_under.creator_id is
'creator_id';

comment on column village.rlams_under.updater_id is
'updater_id';

comment on column village.rlams_under.deleter_id is
'deleter_id';

comment on column village.rlams_under.create_time is
'create_time ';

comment on column village.rlams_under.update_time is
'update_time';

comment on column village.rlams_under.delete_time is
'delete_time';

/*==============================================================*/
/* Table: under_members   共有人信息                                      */
/*==============================================================*/

create table public.under_members (
   uuid                 VARCHAR(64)          not null,
   under_id             VARCHAR(64)          null,
   name                 VARCHAR(128)         null,
   gender               VARCHAR(8)           null,
   id_type              VARCHAR(64)          null,
   id_code              VARCHAR(255)         null,
    relation			VARCHAR(64)			NULL,
   deleted              SMALLINT           null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_UNDER_MEMBERS primary key (uuid)
);

comment on column village.under_members.uuid is
'uuid';

comment on column village.under_members.under_id is
'承包方id';

comment on column village.under_members.name is
'姓名';

comment on column village.under_members.gender is
'性别';

comment on column village.under_members.id_type is
'证件类型';

comment on column village.under_members.id_code is
'证件号码';

comment on column village.under_members.deleted is
'deleted';

comment on column village.under_members.creator_id is
'creator_id';

comment on column village.under_members.updater_id is
'updater_id';

comment on column village.under_members.deleter_id is
'deleter_id';

comment on column village.under_members.create_time is
'create_time ';

comment on column village.under_members.update_time is
'update_time';

comment on column village.under_members.delete_time is
'delete_time';

comment on column village.under_members.RELATION is
'家庭关系';


/*==============================================================*/
/* Table: rlams_document       合同档案表                                */
/*==============================================================*/
create table public.rlams_document (
   uuid                 VARCHAR(64)          not null,
   contract_id          VARCHAR(64)          null,
   doc_no               VARCHAR(65)          null,
   path                 VARCHAR(512)         null,
   deleted              SMALLINT           null,
   creator_id           VARCHAR(64)          null,
   updater_id           VARCHAR(64)          null,
   deleter_id           VARCHAR(64)          null,
   create_time          DATE                 null,
   update_time          DATE                 null,
   delete_time          DATE                 null,
   constraint PK_RLAMS_DOCUMENT primary key (uuid)
);

comment on table village.rlams_document is
'合同关联档案信息';

comment on column village.rlams_document.uuid is
'uuid';

comment on column village.rlams_document.contract_id is
'合同ID';

comment on column village.rlams_document.doc_no is
'流水号';

comment on column village.rlams_document.path is
'路径';

comment on column village.rlams_document.deleted is
'deleted';

comment on column village.rlams_document.creator_id is
'creator_id';

comment on column village.rlams_document.updater_id is
'updater_id';

comment on column village.rlams_document.deleter_id is
'deleter_id';

comment on column village.rlams_document.create_time is
'create_time ';

comment on column village.rlams_document.update_time is
'update_time';

comment on column village.rlams_document.delete_time is
'delete_time';


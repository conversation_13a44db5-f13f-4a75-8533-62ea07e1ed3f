package cn.fight.village.domain.land.repository;

import cn.fight.village.domain.common.entity.JsonResult;
import cn.fight.village.domain.contract.entity.TransProtocolLand;
import cn.fight.village.domain.land.entity.GisLand;
import cn.fight.village.domain.land.entity.RealLandRequest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * gis地块仓库
 */
public interface GisLandRepository extends BaseMapper<GisLand> {
    /**
     * 根据地块编码获取签订协议的土地
     * @param query
     * @return
     */
    List<TransProtocolLand> selectProtocolLands(RealLandRequest query);

    /**
     * 插入GIS土地信息
     * @param gisLand
     * @return
     */
    int updateGeo(GisLand gisLand);

    //获取下一个gid
    Integer getNextGid();

    //删除Gis土地
    int deleteGisLand(String dkdm);

    //土地签署状态更新
    int landSign(@Param("dkbms") List<String> dkbms);
}

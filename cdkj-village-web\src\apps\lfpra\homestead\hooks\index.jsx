import { moreBtnRender,erm_intl } from '@/apps/lfpra/common/hooks/utils.jsx';
export const userTableColumns = ({ seeDateils, editFunc = () => {}, delFunc = () => {} }) => {
  return [
    {
      label: '宅基地编号',
      prop: 'siteCode',
      render: ({ row, index }) => {
        return (<el-button type="primary" link  onClick={()=>seeDateils} >{row.memberCode}</el-button>);
      },
      fixed: 'left'
    },
    {
      label: '宅基地面积（㎡）',
      prop: 'siteArea',
      render: ({ row }) => {
        return `${erm_intl(row.siteArea)}`;
      }
    },
    {
      label: '是否取证',
      prop: 'isForensicsName'
    },
    {
      label: '使用情况',
      prop: 'dicUseCondName'
    },
    {
      label: '使用人（户主）名称',
      prop: 'memberName'
    },
    {
      label: '使用人（户主）证件类型',
      prop: 'dicCardTypeCodeName'
    },
    {
      label: '使用人（户主）证件号码',
      prop: 'cerCertificateNo'
    },
    {
      label: '使用人（家庭）地址',
      prop: 'ownerAddress'
    },
    {
      label: '核查时间',
      prop: 'createTime'
    },
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        return (
          <div>
            <el-button
              type="primary"
              link
              onClick={() => {
                _edit(row);
              }}
            >
              编辑
            </el-button>
            <el-popconfirm
              title="确认删除该条数据吗？"
              onConfirm={() => {
                deleteClick(row);
              }}
              v-slots={{
                reference: () => (
                  <el-button type="primary" link>
                    删除
                  </el-button>
                )
              }}
            ></el-popconfirm>
          </div>
        );
      }
    }
  ];
};

// 按钮
export const useBtnsConfig = ({ addFunc = () => {}, importFunc = () => {}, exportFunc=()=>{} }) => {
  return [
    {
      component: () => (
         <el-button onClick={addFunc} type="primary">
         新增
       </el-button>
      )
    }
  ];
};

<template>
  <template v-for="item in layerTree.filter(x => x.showInTree !== false)">
    <el-sub-menu :index="item.name" v-if="item.children" :key="item.name" class="layer_item">
      <template #title>
        <el-checkbox
          v-if="item.showCheck !== false"
          v-model="item.checked"
          :indeterminate="!item.checked && item.indeterminate"
          @change="layerChange(item)"
          @click.stop
          :title="item.name"
          >{{ item.name }}
        </el-checkbox>
        <span v-else>{{ item.name }}</span>
      </template>
      <FuniLayerTree :layerTree="item.children" @layerChange="item => layerChange(item)"></FuniLayerTree>
    </el-sub-menu>
    <el-menu-item v-else :index="item.name" class="layer_item">
      <el-checkbox v-model="item.checked" @change="layerChange(item)" @click.stop :title="item.name">{{ item.name }} </el-checkbox>
    </el-menu-item>
  </template>
</template>

<script setup>
const emits = defineEmits(['layerChange']);
defineOptions({
  name: 'FuniLayerTree',
  inheritAttrs: false
});
const props = defineProps({
  layerTree: {}
});

/**
 * 图层切换
 */
function layerChange(item) {
  emits('layerChange', item);
}
</script>

<style lang="scss" scoped>
.layer_item {
  :deep(.el-checkbox) {
    width: 100%;
    .el-checkbox__label {
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.5;
      white-space: nowrap;
    }
  }
}
</style>

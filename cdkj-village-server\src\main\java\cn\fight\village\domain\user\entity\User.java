package cn.fight.village.domain.user.entity;

import cn.fight.village.domain.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 系统用户实体类
 *
 */
@TableName("public.rlams_user")
public class User extends BaseEntity {
    /**账号**/
    private String account;

    /**姓名**/
    private String username;

    /**登录密码*/
    private String password;

    /**用户类型
     * 1 管理员 2其他
     * */
    private Integer userType;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }
}

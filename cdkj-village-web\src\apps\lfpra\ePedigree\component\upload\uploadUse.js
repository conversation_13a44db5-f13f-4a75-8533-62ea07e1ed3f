import { ElNotification } from 'element-plus';

/**
 * @description 统一下载模板接口
 * @param {String} downloadTemplate 下载函数
 * @param {String} templateName 模板名称
 * **/
const downloadTemplateUse = async (downloadTemplate, params,templateName) => {
  const formData = new FormData();
  // formData.append('downloadTemplate',params.downloadTemplate)
  // formData.append('responseType','blob')
  // let resData = await $http.post(`${downloadTemplate}?downloadTemplate=${params.downloadTemplate}`);
  let resData = await $http.post(downloadTemplate,params,{responseType: 'blob'})

  var downloadElement = document.createElement('a');
  var href = window.URL.createObjectURL(resData); //创建下载的链接
  downloadElement.href = href;
  downloadElement.download = templateName + '.xls'; //下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); //点击下载
  document.body.removeChild(downloadElement); //下载完成移除元素
};

export { downloadTemplateUse };
